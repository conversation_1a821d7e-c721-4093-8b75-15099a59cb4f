<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="uni.UNI8DE29FC.test" >

    <uses-sdk
        android:minSdkVersion="21"
        android:targetSdkVersion="33" />

    <instrumentation
        android:name="android.test.InstrumentationTestRunner"
        android:functionalTest="false"
        android:handleProfiling="false"
        android:label="Tests for uni.UNI8DE29FC"
        android:targetPackage="uni.UNI8DE29FC" />

    <application
        android:debuggable="true"
        android:extractNativeLibs="true" >
        <uses-library android:name="android.test.runner" />
    </application>

</manifest>