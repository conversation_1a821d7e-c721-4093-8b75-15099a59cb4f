
  ;(function(){
  let u=void 0,isReady=false,onReadyCallbacks=[],isServiceReady=false,onServiceReadyCallbacks=[];
  const __uniConfig = {"pages":[],"globalStyle":{"backgroundColor":"#FFFFFF","navigationBar":{"backgroundColor":"#FFFFFF","titleText":"星品购","style":"custom","type":"default","titleColor":"#000000"},"isNVue":false},"nvue":{"compiler":"uni-app","styleCompiler":"uni-app","flex-direction":"column"},"renderer":"auto","appname":"融业港","splashscreen":{"alwaysShowBeforeRender":true,"autoclose":true},"compilerVersion":"4.64","entryPagePath":"pages/index/index","entryPageQuery":"","realEntryPagePath":"","networkTimeout":{"request":60000,"connectSocket":60000,"uploadFile":60000,"downloadFile":60000},"tabBar":{"position":"bottom","color":"#999","selectedColor":"#007aff","borderStyle":"black","blurEffect":"none","fontSize":"10px","iconWidth":"24px","spacing":"3px","height":"50px","list":[{"pagePath":"pages/index/index"},{"pagePath":"pages/index/cart"},{"pagePath":"pages/index/user"}],"selectedIndex":0,"shown":true},"fallbackLocale":"zh-Hans","locales":{},"darkmode":false,"themeConfig":{}};
  const __uniRoutes = [{"path":"pages/index/index","meta":{"isQuit":true,"isEntry":true,"isTabBar":true,"tabBarIndex":0,"enablePullDownRefresh":true,"navigationBar":{"titleText":"首页","type":"default"},"isNVue":false}},{"path":"pages/index/test","meta":{"navigationBar":{"titleText":"1","type":"default"},"isNVue":false}},{"path":"pages/index/user","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":2,"enablePullDownRefresh":true,"navigationBar":{"titleText":"个人中心","type":"default"},"isNVue":false}},{"path":"pages/index/category","meta":{"navigationBar":{"titleText":"商品分类","type":"default"},"isNVue":false}},{"path":"pages/index/cart","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":1,"navigationBar":{"titleText":"购物车","type":"default"},"isNVue":false}},{"path":"pages/index/login","meta":{"navigationBar":{"titleText":"登录","type":"default"},"isNVue":false}},{"path":"pages/index/search","meta":{"navigationBar":{"titleText":"搜索","type":"default"},"isNVue":false}},{"path":"pages/index/page","meta":{"navigationBar":{"titleText":"","type":"default"},"isNVue":false}},{"path":"pages/notice/index","meta":{"navigationBar":{"titleText":"公告列表","type":"default"},"isNVue":false}},{"path":"pages/notice/detail","meta":{"navigationBar":{"titleText":"公告详情","type":"default"},"isNVue":false}},{"path":"pages/culture/index","meta":{"navigationBar":{"titleText":"公司文化","type":"default"},"isNVue":false}},{"path":"pages/culture/detail","meta":{"navigationBar":{"titleText":"文化详情","type":"default"},"isNVue":false}},{"path":"pages/health/index","meta":{"navigationBar":{"titleText":"大健康","type":"default"},"isNVue":false}},{"path":"pages/health/detail","meta":{"navigationBar":{"titleText":"健康详情","type":"default"},"isNVue":false}},{"path":"pages/tourism/index","meta":{"navigationBar":{"titleText":"文旅","type":"default"},"isNVue":false}},{"path":"pages/tourism/detail","meta":{"navigationBar":{"titleText":"文旅详情","type":"default"},"isNVue":false}},{"path":"pages/store/list","meta":{"navigationBar":{"titleText":"店铺列表","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/store/detail","meta":{"navigationBar":{"titleText":"店铺详情","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/goods/index","meta":{"navigationBar":{"titleText":"商品详情","type":"default"},"isNVue":false}},{"path":"pages/goods/groupon","meta":{"navigationBar":{"titleText":"拼团商品","type":"default"},"isNVue":false}},{"path":"pages/goods/seckill","meta":{"navigationBar":{"titleText":"秒杀商品","type":"default"},"isNVue":false}},{"path":"pages/goods/score","meta":{"navigationBar":{"titleText":"积分商品","type":"default"},"isNVue":false}},{"path":"pages/goods/list","meta":{"navigationBar":{"titleText":"商品列表","type":"default"},"isNVue":false}},{"path":"pages/goods/comment/add","meta":{"navigationBar":{"titleText":"评价商品","type":"default"},"isNVue":false}},{"path":"pages/goods/comment/list","meta":{"navigationBar":{"titleText":"商品评价","type":"default"},"isNVue":false}},{"path":"pages/order/detail","meta":{"navigationBar":{"titleText":"订单详情","type":"default"},"isNVue":false}},{"path":"pages/order/confirm","meta":{"navigationBar":{"titleText":"确认订单","type":"default"},"isNVue":false}},{"path":"pages/order/list","meta":{"enablePullDownRefresh":true,"navigationBar":{"titleText":"我的订单","type":"default"},"isNVue":false}},{"path":"pages/order/invoice","meta":{"navigationBar":{"titleText":"发票详情","type":"default"},"isNVue":false}},{"path":"pages/order/dispatch/content","meta":{"navigationBar":{"titleText":"发货内容","type":"default"},"isNVue":false}},{"path":"pages/order/aftersale/apply","meta":{"navigationBar":{"titleText":"申请售后","type":"default"},"isNVue":false}},{"path":"pages/order/aftersale/list","meta":{"navigationBar":{"titleText":"售后列表","type":"default"},"isNVue":false}},{"path":"pages/order/aftersale/detail","meta":{"navigationBar":{"titleText":"售后详情","type":"default"},"isNVue":false}},{"path":"pages/order/aftersale/log","meta":{"navigationBar":{"titleText":"售后进度","type":"default"},"isNVue":false}},{"path":"pages/order/express/log","meta":{"navigationBar":{"titleText":"物流轨迹","type":"default"},"isNVue":false}},{"path":"pages/order/express/list","meta":{"navigationBar":{"titleText":"订单包裹","type":"default"},"isNVue":false}},{"path":"pages/user/info","meta":{"navigationBar":{"titleText":"我的信息","type":"default"},"isNVue":false}},{"path":"pages/user/goods-collect","meta":{"navigationBar":{"titleText":"我的收藏","type":"default"},"isNVue":false}},{"path":"pages/user/goods-log","meta":{"navigationBar":{"titleText":"我的足迹","type":"default"},"isNVue":false}},{"path":"pages/user/address/list","meta":{"navigationBar":{"titleText":"收货地址","type":"default"},"isNVue":false}},{"path":"pages/user/address/edit","meta":{"navigationBar":{"titleText":"编辑地址","type":"default"},"isNVue":false}},{"path":"pages/user/invoice/list","meta":{"navigationBar":{"titleText":"发票管理","type":"default"},"isNVue":false}},{"path":"pages/user/invoice/edit","meta":{"navigationBar":{"titleText":"编辑发票","type":"default"},"isNVue":false}},{"path":"pages/user/wallet/money","meta":{"navigationBar":{"titleText":"我的余额","type":"default"},"isNVue":false}},{"path":"pages/user/wallet/commission","meta":{"navigationBar":{"titleText":"我的佣金","type":"default"},"isNVue":false}},{"path":"pages/user/wallet/score","meta":{"navigationBar":{"titleText":"我的积分","type":"default"},"isNVue":false}},{"path":"pages/user/team","meta":{"navigationBar":{"titleText":"我的团队","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/user/agent/team","meta":{"navigationBar":{"titleText":"代理团队","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/user/store/apply","meta":{"navigationBar":{"titleText":"申请商家","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/user/store/info","meta":{"navigationBar":{"titleText":"商家信息","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/user/store/pay-qrcode","meta":{"navigationBar":{"titleText":"收款码","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/user/store/index","meta":{"navigationBar":{"titleText":"商户中心","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/user/wallet/pay","meta":{"navigationBar":{"titleText":"转账","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/user/wallet/transfer","meta":{"navigationBar":{"titleText":"","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/user/store/goods","meta":{"navigationBar":{"titleText":"商品管理","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/user/store/goods-edit","meta":{"navigationBar":{"titleText":"编辑商品","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/user/store/goods-info","meta":{"navigationBar":{"titleText":"商品详情","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/user/store/detail","meta":{"navigationBar":{"titleText":"商户信息","type":"default"},"isNVue":false}},{"path":"pages/user/store/order","meta":{"navigationBar":{"titleText":"收款记录","type":"default"},"isNVue":false}},{"path":"pages/user/store/order-info","meta":{"navigationBar":{"titleText":"收款详情","type":"default"},"isNVue":false}},{"path":"pages/user/store/edit","meta":{"navigationBar":{"titleText":"编辑商户信息","type":"default"},"isNVue":false}},{"path":"pages/user/activate","meta":{"navigationBar":{"titleText":"会员激活","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/commission/index","meta":{"navigationBar":{"titleText":"分销","type":"default"},"isNVue":false}},{"path":"pages/commission/apply","meta":{"navigationBar":{"titleText":"申请分销商","type":"default"},"isNVue":false}},{"path":"pages/commission/goods","meta":{"navigationBar":{"titleText":"推广商品","type":"default"},"isNVue":false}},{"path":"pages/commission/order","meta":{"navigationBar":{"titleText":"分销订单","type":"default"},"isNVue":false}},{"path":"pages/commission/share-log","meta":{"navigationBar":{"titleText":"分享记录","type":"default"},"isNVue":false}},{"path":"pages/commission/team","meta":{"navigationBar":{"titleText":"我的团队","type":"default"},"isNVue":false}},{"path":"pages/app/sign","meta":{"navigationBar":{"titleText":"签到中心","type":"default"},"isNVue":false}},{"path":"pages/app/score-shop","meta":{"navigationBar":{"titleText":"积分商城","type":"default"},"isNVue":false}},{"path":"pages/public/setting","meta":{"navigationBar":{"titleText":"系统设置","type":"default"},"isNVue":false}},{"path":"pages/public/feedback","meta":{"navigationBar":{"titleText":"问题反馈","type":"default"},"isNVue":false}},{"path":"pages/public/richtext","meta":{"navigationBar":{"titleText":"富文本","type":"default"},"isNVue":false}},{"path":"pages/public/faq","meta":{"navigationBar":{"titleText":"常见问题","type":"default"},"isNVue":false}},{"path":"pages/public/error","meta":{"navigationBar":{"titleText":"错误页面","type":"default"},"isNVue":false}},{"path":"pages/public/webview","meta":{"navigationBar":{"titleText":"","type":"default"},"isNVue":false}},{"path":"pages/coupon/list","meta":{"navigationBar":{"titleText":"领券中心","type":"default"},"isNVue":false}},{"path":"pages/coupon/detail","meta":{"navigationBar":{"titleText":"优惠券","type":"default"},"isNVue":false}},{"path":"pages/chat/index","meta":{"navigationBar":{"titleText":"客服","type":"default"},"isNVue":false}},{"path":"pages/pay/index","meta":{"navigationBar":{"titleText":"收银台","type":"default"},"isNVue":false}},{"path":"pages/pay/result","meta":{"navigationBar":{"titleText":"支付结果","type":"default"},"isNVue":false}},{"path":"pages/pay/recharge","meta":{"navigationBar":{"titleText":"充值余额","type":"default"},"isNVue":false}},{"path":"pages/pay/recharge-log","meta":{"navigationBar":{"titleText":"充值记录","type":"default"},"isNVue":false}},{"path":"pages/pay/withdraw","meta":{"navigationBar":{"titleText":"申请提现","type":"default"},"isNVue":false}},{"path":"pages/pay/withdraw-log","meta":{"navigationBar":{"titleText":"提现记录","type":"default"},"isNVue":false}},{"path":"pages/pay/recharge-upload","meta":{"navigationBar":{"titleText":"上传凭证","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/activity/groupon/detail","meta":{"navigationBar":{"titleText":"拼团详情","type":"default"},"isNVue":false}},{"path":"pages/activity/groupon/order","meta":{"enablePullDownRefresh":true,"navigationBar":{"titleText":"我的拼团","type":"default"},"isNVue":false}},{"path":"pages/activity/index","meta":{"navigationBar":{"titleText":"营销商品","type":"default"},"isNVue":false}},{"path":"pages/activity/groupon/list","meta":{"navigationBar":{"titleText":"拼团活动","type":"default"},"isNVue":false}},{"path":"pages/activity/seckill/list","meta":{"navigationBar":{"titleText":"秒杀活动","type":"default"},"isNVue":false}}].map(uniRoute=>(uniRoute.meta.route=uniRoute.path,__uniConfig.pages.push(uniRoute.path),uniRoute.path='/'+uniRoute.path,uniRoute));
  __uniConfig.styles=[];//styles
  __uniConfig.onReady=function(callback){if(__uniConfig.ready){callback()}else{onReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"ready",{get:function(){return isReady},set:function(val){isReady=val;if(!isReady){return}const callbacks=onReadyCallbacks.slice(0);onReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
  __uniConfig.onServiceReady=function(callback){if(__uniConfig.serviceReady){callback()}else{onServiceReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"serviceReady",{get:function(){return isServiceReady},set:function(val){isServiceReady=val;if(!isServiceReady){return}const callbacks=onServiceReadyCallbacks.slice(0);onServiceReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
  service.register("uni-app-config",{create(a,b,c){if(!__uniConfig.viewport){var d=b.weex.config.env.scale,e=b.weex.config.env.deviceWidth,f=Math.ceil(e/d);Object.assign(__uniConfig,{viewport:f,defaultFontSize:16})}return{instance:{__uniConfig:__uniConfig,__uniRoutes:__uniRoutes,global:u,window:u,document:u,frames:u,self:u,location:u,navigator:u,localStorage:u,history:u,Caches:u,screen:u,alert:u,confirm:u,prompt:u,fetch:u,XMLHttpRequest:u,WebSocket:u,webkit:u,print:u}}}}); 
  })();
  