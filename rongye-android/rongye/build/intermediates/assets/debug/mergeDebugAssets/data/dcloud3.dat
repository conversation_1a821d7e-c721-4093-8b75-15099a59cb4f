// { "framework": "Vue"} 
var plus=weex.requireModule('plus'),globalEvent=weex.requireModule('globalEvent');function normalize(a,b){return'string'==typeof a&&0===a.indexOf('CALLBACK_')?function(d,f){plus.postMessage({type:'requireNativePluginCallback',data:{id:parseInt(a.replace('CALLBACK_','')),ret:d,keepAlive:!!f}},b)}:a}globalEvent.addEventListener('plusMessage',function(a){if(a.data&&'requireNativePlugin'===a.data.type){var b=a.originId,c=a.data.plugin,d=a.data.method,f=a.data.args;f=f.map(function(h){return normalize(h,b)});var g=weex.requireModule(c);g[d].apply(g,f)}});