{"logs": [{"outputFile": "com.android.simple.rongye-mergeDebugResources-12:/values-es/values-es.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/res/values-es/values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,112,170,225,286,340,395,452,507,571,626,679,739,798,854,917,975,1029,1092,1176,1248,1318,1405,1464,1521,1576,1656,1726,1794,1862,1932,2047,2252,2321,2427,2504,2583,2653,2723,2788,2858,2933,3039,3177,3295,3371,3439,3557,3617,3698,3786,3872,3952,4007,4082,4173,4257,4369", "endColumns": "56,57,54,60,53,54,56,54,63,54,52,59,58,55,62,57,53,62,83,71,69,86,58,56,54,79,69,67,67,69,114,204,68,105,76,78,69,69,64,69,74,105,137,117,75,67,117,59,80,87,85,79,54,74,90,83,111,117", "endOffsets": "107,165,220,281,335,390,447,502,566,621,674,734,793,849,912,970,1024,1087,1171,1243,1313,1400,1459,1516,1571,1651,1721,1789,1857,1927,2042,2247,2316,2422,2499,2578,2648,2718,2783,2853,2928,3034,3172,3290,3366,3434,3552,3612,3693,3781,3867,3947,4002,4077,4168,4252,4364,4482"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2836,2893,2951,3006,3067,3121,3176,3233,3288,3352,3407,3460,3520,3579,3635,3698,3756,3810,3873,3957,4029,4099,4186,4245,4302,4357,4437,4507,4575,4643,4713,4828,5033,5102,5208,5285,5364,5434,5504,5569,5639,5714,5820,5958,6076,6152,6220,6338,6398,6479,6567,6653,6733,6788,6863,6954,7038,7150", "endColumns": "56,57,54,60,53,54,56,54,63,54,52,59,58,55,62,57,53,62,83,71,69,86,58,56,54,79,69,67,67,69,114,204,68,105,76,78,69,69,64,69,74,105,137,117,75,67,117,59,80,87,85,79,54,74,90,83,111,117", "endOffsets": "2888,2946,3001,3062,3116,3171,3228,3283,3347,3402,3455,3515,3574,3630,3693,3751,3805,3868,3952,4024,4094,4181,4240,4297,4352,4432,4502,4570,4638,4708,4823,5028,5097,5203,5280,5359,5429,5499,5564,5634,5709,5815,5953,6071,6147,6215,6333,6393,6474,6562,6648,6728,6783,6858,6949,7033,7145,7263"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/9eeb30f4aa8cd90015309ee238e675a8/transformed/appcompat-1.1.0/res/values-es/values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,320,428,513,614,742,828,910,1002,1095,1192,1286,1387,1481,1577,1673,1765,1857,1938,2045,2156,2255,2363,2471,2578,2737,2836", "endColumns": "101,112,107,84,100,127,85,81,91,92,96,93,100,93,95,95,91,91,80,106,110,98,107,107,106,158,98,81", "endOffsets": "202,315,423,508,609,737,823,905,997,1090,1187,1281,1382,1476,1572,1668,1760,1852,1933,2040,2151,2250,2358,2466,2573,2732,2831,2913"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,320,428,513,614,742,828,910,1002,1095,1192,1286,1387,1481,1577,1673,1765,1857,1938,2045,2156,2255,2363,2471,2578,2737,7268", "endColumns": "101,112,107,84,100,127,85,81,91,92,96,93,100,93,95,95,91,91,80,106,110,98,107,107,106,158,98,81", "endOffsets": "202,315,423,508,609,737,823,905,997,1090,1187,1281,1382,1476,1572,1668,1760,1852,1933,2040,2151,2250,2358,2466,2573,2732,2831,7345"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/b208fc1fa734e0528687ea61bd7ccbb7/transformed/core-1.1.0/res/values-es/values-es.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "88", "startColumns": "4", "startOffsets": "7350", "endColumns": "100", "endOffsets": "7446"}}]}]}