{"logs": [{"outputFile": "com.android.simple.rongye-mergeDebugResources-12:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/b208fc1fa734e0528687ea61bd7ccbb7/transformed/core-1.1.0/res/values-zh-rTW/values-zh-rTW.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "89", "startColumns": "4", "startOffsets": "6391", "endColumns": "100", "endOffsets": "6487"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/res/values-zh-rTW/values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,106,158,209,263,313,363,415,465,520,568,618,671,722,774,826,877,927,985,1037,1101,1159,1217,1286,1344,1395,1446,1515,1579,1641,1703,1767,1855,1961,2026,2103,2169,2234,2297,2361,2423,2485,2547,2617,2693,2776,2846,2900,2977,3030,3097,3158,3222,3289,3341,3404,3472,3537,3619", "endColumns": "50,51,50,53,49,49,51,49,54,47,49,52,50,51,51,50,49,57,51,63,57,57,68,57,50,50,68,63,61,61,63,87,105,64,76,65,64,62,63,61,61,61,69,75,82,69,53,76,52,66,60,63,66,51,62,67,64,81,77", "endOffsets": "101,153,204,258,308,358,410,460,515,563,613,666,717,769,821,872,922,980,1032,1096,1154,1212,1281,1339,1390,1441,1510,1574,1636,1698,1762,1850,1956,2021,2098,2164,2229,2292,2356,2418,2480,2542,2612,2688,2771,2841,2895,2972,3025,3092,3153,3217,3284,3336,3399,3467,3532,3614,3692"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2671,2722,2774,2825,2879,2929,2979,3031,3081,3136,3184,3234,3287,3338,3390,3442,3493,3543,3601,3653,3717,3775,3833,3902,3960,4011,4062,4131,4195,4257,4319,4383,4471,4577,4642,4719,4785,4850,4913,4977,5039,5101,5163,5233,5309,5392,5462,5516,5593,5646,5713,5774,5838,5905,5957,6020,6088,6153,6235", "endColumns": "50,51,50,53,49,49,51,49,54,47,49,52,50,51,51,50,49,57,51,63,57,57,68,57,50,50,68,63,61,61,63,87,105,64,76,65,64,62,63,61,61,61,69,75,82,69,53,76,52,66,60,63,66,51,62,67,64,81,77", "endOffsets": "2717,2769,2820,2874,2924,2974,3026,3076,3131,3179,3229,3282,3333,3385,3437,3488,3538,3596,3648,3712,3770,3828,3897,3955,4006,4057,4126,4190,4252,4314,4378,4466,4572,4637,4714,4780,4845,4908,4972,5034,5096,5158,5228,5304,5387,5457,5511,5588,5641,5708,5769,5833,5900,5952,6015,6083,6148,6230,6308"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/9eeb30f4aa8cd90015309ee238e675a8/transformed/appcompat-1.1.0/res/values-zh-rTW/values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,833,925,1018,1115,1211,1307,1401,1497,1589,1681,1773,1850,1946,2041,2136,2233,2329,2427,2577,2671", "endColumns": "94,92,99,81,96,107,76,75,91,92,96,95,95,93,95,91,91,91,76,95,94,94,96,95,97,149,93,77", "endOffsets": "195,288,388,470,567,675,752,828,920,1013,1110,1206,1302,1396,1492,1584,1676,1768,1845,1941,2036,2131,2228,2324,2422,2572,2666,2744"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,833,925,1018,1115,1211,1307,1401,1497,1589,1681,1773,1850,1946,2041,2136,2233,2329,2427,2577,6313", "endColumns": "94,92,99,81,96,107,76,75,91,92,96,95,95,93,95,91,91,91,76,95,94,94,96,95,97,149,93,77", "endOffsets": "195,288,388,470,567,675,752,828,920,1013,1110,1206,1302,1396,1492,1584,1676,1768,1845,1941,2036,2131,2228,2324,2422,2572,2666,6386"}}]}]}