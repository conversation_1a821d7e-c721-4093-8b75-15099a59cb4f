{"logs": [{"outputFile": "com.android.simple.rongye-mergeDebugResources-12:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/b208fc1fa734e0528687ea61bd7ccbb7/transformed/core-1.1.0/res/values-zh-rHK/values-zh-rHK.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "89", "startColumns": "4", "startOffsets": "6387", "endColumns": "100", "endOffsets": "6483"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/res/values-zh-rHK/values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,106,158,209,263,313,363,415,465,520,568,618,671,722,774,826,877,927,985,1037,1101,1159,1217,1286,1344,1395,1446,1515,1579,1641,1703,1767,1855,1961,2026,2103,2169,2234,2297,2361,2423,2485,2547,2617,2693,2777,2847,2901,2978,3031,3098,3159,3223,3290,3342,3405,3473,3538,3620", "endColumns": "50,51,50,53,49,49,51,49,54,47,49,52,50,51,51,50,49,57,51,63,57,57,68,57,50,50,68,63,61,61,63,87,105,64,76,65,64,62,63,61,61,61,69,75,83,69,53,76,52,66,60,63,66,51,62,67,64,81,77", "endOffsets": "101,153,204,258,308,358,410,460,515,563,613,666,717,769,821,872,922,980,1032,1096,1154,1212,1281,1339,1390,1441,1510,1574,1636,1698,1762,1850,1956,2021,2098,2164,2229,2292,2356,2418,2480,2542,2612,2688,2772,2842,2896,2973,3026,3093,3154,3218,3285,3337,3400,3468,3533,3615,3693"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2666,2717,2769,2820,2874,2924,2974,3026,3076,3131,3179,3229,3282,3333,3385,3437,3488,3538,3596,3648,3712,3770,3828,3897,3955,4006,4057,4126,4190,4252,4314,4378,4466,4572,4637,4714,4780,4845,4908,4972,5034,5096,5158,5228,5304,5388,5458,5512,5589,5642,5709,5770,5834,5901,5953,6016,6084,6149,6231", "endColumns": "50,51,50,53,49,49,51,49,54,47,49,52,50,51,51,50,49,57,51,63,57,57,68,57,50,50,68,63,61,61,63,87,105,64,76,65,64,62,63,61,61,61,69,75,83,69,53,76,52,66,60,63,66,51,62,67,64,81,77", "endOffsets": "2712,2764,2815,2869,2919,2969,3021,3071,3126,3174,3224,3277,3328,3380,3432,3483,3533,3591,3643,3707,3765,3823,3892,3950,4001,4052,4121,4185,4247,4309,4373,4461,4567,4632,4709,4775,4840,4903,4967,5029,5091,5153,5223,5299,5383,5453,5507,5584,5637,5704,5765,5829,5896,5948,6011,6079,6144,6226,6304"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/9eeb30f4aa8cd90015309ee238e675a8/transformed/appcompat-1.1.0/res/values-zh-rHK/values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,833,925,1018,1109,1205,1301,1395,1491,1583,1675,1767,1844,1940,2035,2130,2227,2323,2421,2572,2666", "endColumns": "94,92,99,81,96,107,76,75,91,92,90,95,95,93,95,91,91,91,76,95,94,94,96,95,97,150,93,77", "endOffsets": "195,288,388,470,567,675,752,828,920,1013,1104,1200,1296,1390,1486,1578,1670,1762,1839,1935,2030,2125,2222,2318,2416,2567,2661,2739"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,833,925,1018,1109,1205,1301,1395,1491,1583,1675,1767,1844,1940,2035,2130,2227,2323,2421,2572,6309", "endColumns": "94,92,99,81,96,107,76,75,91,92,90,95,95,93,95,91,91,91,76,95,94,94,96,95,97,150,93,77", "endOffsets": "195,288,388,470,567,675,752,828,920,1013,1104,1200,1296,1390,1486,1578,1670,1762,1839,1935,2030,2125,2222,2318,2416,2567,2661,6382"}}]}]}