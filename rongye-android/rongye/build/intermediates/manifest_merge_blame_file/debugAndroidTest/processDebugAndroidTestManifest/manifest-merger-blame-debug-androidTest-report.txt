1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="uni.UNI8DE29FC.test" >
4
5    <uses-sdk
5-->/Users/<USER>/Project/rongye/rongye-android/rongye/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest4519490638237128097.xml:5:5-74
6        android:minSdkVersion="21"
6-->/Users/<USER>/Project/rongye/rongye-android/rongye/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest4519490638237128097.xml:5:15-41
7        android:targetSdkVersion="33" />
7-->/Users/<USER>/Project/rongye/rongye-android/rongye/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest4519490638237128097.xml:5:42-71
8
9    <instrumentation
9-->/Users/<USER>/Project/rongye/rongye-android/rongye/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest4519490638237128097.xml:11:5-15:64
10        android:name="android.test.InstrumentationTestRunner"
10-->/Users/<USER>/Project/rongye/rongye-android/rongye/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest4519490638237128097.xml:11:22-75
11        android:functionalTest="false"
11-->/Users/<USER>/Project/rongye/rongye-android/rongye/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest4519490638237128097.xml:14:22-52
12        android:handleProfiling="false"
12-->/Users/<USER>/Project/rongye/rongye-android/rongye/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest4519490638237128097.xml:13:22-53
13        android:label="Tests for uni.UNI8DE29FC"
13-->/Users/<USER>/Project/rongye/rongye-android/rongye/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest4519490638237128097.xml:15:22-62
14        android:targetPackage="uni.UNI8DE29FC" />
14-->/Users/<USER>/Project/rongye/rongye-android/rongye/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest4519490638237128097.xml:12:22-60
15
16    <application
16-->/Users/<USER>/Project/rongye/rongye-android/rongye/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest4519490638237128097.xml:7:5-9:19
17        android:debuggable="true"
18        android:extractNativeLibs="true" >
19        <uses-library android:name="android.test.runner" />
19-->/Users/<USER>/Project/rongye/rongye-android/rongye/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest4519490638237128097.xml:8:9-60
19-->/Users/<USER>/Project/rongye/rongye-android/rongye/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest4519490638237128097.xml:8:23-57
20    </application>
21
22</manifest>
