-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:2:1-48:12
INJECTED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:2:1-48:12
INJECTED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:2:1-48:12
INJECTED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:2:1-48:12
MERGED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:2:1-280:12
MERGED from [uniapp-v8-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/be18ab0828c64768cf1ec6dfab35b9e1/transformed/jetified-uniapp-v8-release/AndroidManifest.xml:20:1-35:12
MERGED from [android-gif-drawable-1.2.28.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/06388c87e1c288568e36099356b812fb/transformed/jetified-android-gif-drawable-1.2.28/AndroidManifest.xml:2:1-7:12
MERGED from [oaid_sdk_1.0.25.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/e293a48d0426c071bb942e6321ccce3a/transformed/jetified-oaid_sdk_1.0.25/AndroidManifest.xml:2:1-17:12
MERGED from [breakpad-build-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/c1a2a37f6bfdbb795957a5bace971b01/transformed/jetified-breakpad-build-release/AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/9eeb30f4aa8cd90015309ee238e675a8/transformed/appcompat-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/c72e6779054f809da409cf6a5c7e8b9b/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:glide:4.9.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/749f7d253d6272a1512fcdb00bcd3c5b/transformed/jetified-glide-4.9.0/AndroidManifest.xml:2:1-12:12
MERGED from [androidx.fragment:fragment:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1cd6843987475c35ab0c860d0df93693/transformed/fragment-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/7ebe4587b02fdf6b712b324d7d3adb55/transformed/recyclerview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.webkit:webkit:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6eefa1986fd028d9c2a6d06175d4f2fd/transformed/webkit-1.5.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/93773354437d2fdb0cec786de892b229/transformed/jetified-appcompat-resources-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/69203ce7a2c06f6a825dcab1d74a714d/transformed/drawerlayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2d8fa74b1f4ca5c076182437449b2295/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6834ace68f8aa4ccd6a7f0606ee81fd7/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/dd0a81ce75084cc0c9ff30b21bd266eb/transformed/jetified-activity-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/aa9620d037a8ac4812cf95f9ef11e6e0/transformed/customview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/fe8ad3c8e1169ec770e9bcc3337117f1/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f49ebcb56f1574ca0d7d82dd42d35bd4/transformed/vectordrawable-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/b208fc1fa734e0528687ea61bd7ccbb7/transformed/core-1.1.0/AndroidManifest.xml:17:1-26:12
MERGED from [com.facebook.fresco:fresco:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/73aa1ce44b5336a1ac083af498f0d42a/transformed/jetified-fresco-2.5.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:animated-gif:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a2bca994e3a1736259967e476fd2bb77/transformed/jetified-animated-gif-2.5.0/AndroidManifest.xml:2:1-9:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/59628f70395a84a0d28721f3accb3fb4/transformed/cursoradapter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/bce69b476ae81bfe7a2cc3a94c9ad064/transformed/versionedparcelable-1.1.0/AndroidManifest.xml:17:1-27:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.9.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5f5221b858618905af35acf9a5fb73d1/transformed/jetified-gifdecoder-4.9.0/AndroidManifest.xml:2:1-11:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/e0425a064789fab98c1f1af3aea6bbdd/transformed/lifecycle-runtime-2.1.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/b9acdf07edc1841919e9acae38d85737/transformed/lifecycle-viewmodel-2.1.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/333ea104ff85e182ed4078877d282719/transformed/jetified-savedstate-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2f00b0316b3ea715f1122055364c04a2/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/84ef0e01d3ba6b69f390ee963fcaa2d6/transformed/lifecycle-livedata-2.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/9debccd1dbb3b10d3d0d289cf2bfc22a/transformed/lifecycle-livedata-core-2.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a51aacf585a310914b2b1f5f681568ed/transformed/core-runtime-2.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fresco:soloader:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/267ef64ed29f05cdacb3f45608583489/transformed/jetified-soloader-2.5.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:animated-base:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/b9d3a55a189994f4716536c927272cf8/transformed/jetified-animated-base-2.5.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:animated-drawable:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/973ddde2fb359ddc8161202716873e3a/transformed/jetified-animated-drawable-2.5.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:drawee:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0678f6ec823ec63e3ee8dc05fdae0e6e/transformed/jetified-drawee-2.5.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:nativeimagefilters:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1d7d66a80c027e63ebfcf8efc082c3f1/transformed/jetified-nativeimagefilters-2.5.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:memory-type-native:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/012fd9a21cb6e5037eb2031631f9d3ab/transformed/jetified-memory-type-native-2.5.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:memory-type-java:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/28bd6483f7903ddae698d77b64ddebfd/transformed/jetified-memory-type-java-2.5.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:imagepipeline-native:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5535916c518576588eb971d8b9347977/transformed/jetified-imagepipeline-native-2.5.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:memory-type-ashmem:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f6e43c5f56d8533ba5c577146e7017c4/transformed/jetified-memory-type-ashmem-2.5.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:imagepipeline:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/7a966bd8b6d395727996cf742c78580e/transformed/jetified-imagepipeline-2.5.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/cb4d8775ab589a7390899533a823c1a4/transformed/jetified-nativeimagetranscoder-2.5.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.soloader:soloader:0.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/1b73001a1a0f8a3f7dc235dbf297ffe1/transformed/jetified-soloader-0.10.1/AndroidManifest.xml:2:1-13:12
MERGED from [com.facebook.fresco:middleware:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/fa624f05da5ecd48d134399877ef85f3/transformed/jetified-middleware-2.5.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:ui-common:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/b07a43c7e15a0765f198ac5c27c8c00a/transformed/jetified-ui-common-2.5.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:imagepipeline-base:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f3f51a8eb494f432708d82fa0241b327/transformed/jetified-imagepipeline-base-2.5.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:fbcore:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/8c5824d83f65310b8fbcde9573bb6381/transformed/jetified-fbcore-2.5.0/AndroidManifest.xml:2:1-9:12
	package
		INJECTED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml
	android:versionCode
		INJECTED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:2:11-69
application
ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:4:5-46:19
INJECTED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:4:5-46:19
MERGED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:172:5-278:19
MERGED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:172:5-278:19
MERGED from [uniapp-v8-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/be18ab0828c64768cf1ec6dfab35b9e1/transformed/jetified-uniapp-v8-release/AndroidManifest.xml:27:5-33:19
MERGED from [uniapp-v8-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/be18ab0828c64768cf1ec6dfab35b9e1/transformed/jetified-uniapp-v8-release/AndroidManifest.xml:27:5-33:19
MERGED from [com.github.bumptech.glide:glide:4.9.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/749f7d253d6272a1512fcdb00bcd3c5b/transformed/jetified-glide-4.9.0/AndroidManifest.xml:10:5-20
MERGED from [com.github.bumptech.glide:glide:4.9.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/749f7d253d6272a1512fcdb00bcd3c5b/transformed/jetified-glide-4.9.0/AndroidManifest.xml:10:5-20
MERGED from [androidx.core:core:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/b208fc1fa734e0528687ea61bd7ccbb7/transformed/core-1.1.0/AndroidManifest.xml:24:5-89
MERGED from [androidx.core:core:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/b208fc1fa734e0528687ea61bd7ccbb7/transformed/core-1.1.0/AndroidManifest.xml:24:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/bce69b476ae81bfe7a2cc3a94c9ad064/transformed/versionedparcelable-1.1.0/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/bce69b476ae81bfe7a2cc3a94c9ad064/transformed/versionedparcelable-1.1.0/AndroidManifest.xml:24:5-25:19
MERGED from [com.github.bumptech.glide:gifdecoder:4.9.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5f5221b858618905af35acf9a5fb73d1/transformed/jetified-gifdecoder-4.9.0/AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:gifdecoder:4.9.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5f5221b858618905af35acf9a5fb73d1/transformed/jetified-gifdecoder-4.9.0/AndroidManifest.xml:9:5-20
MERGED from [com.facebook.soloader:soloader:0.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/1b73001a1a0f8a3f7dc235dbf297ffe1/transformed/jetified-soloader-0.10.1/AndroidManifest.xml:11:5-20
MERGED from [com.facebook.soloader:soloader:0.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/1b73001a1a0f8a3f7dc235dbf297ffe1/transformed/jetified-soloader-0.10.1/AndroidManifest.xml:11:5-20
	android:extractNativeLibs
		INJECTED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml
		REJECTED from [uniapp-v8-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/be18ab0828c64768cf1ec6dfab35b9e1/transformed/jetified-uniapp-v8-release/AndroidManifest.xml:27:18-50
	android:appComponentFactory
		ADDED from [androidx.core:core:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/b208fc1fa734e0528687ea61bd7ccbb7/transformed/core-1.1.0/AndroidManifest.xml:24:18-86
	android:supportsRtl
		ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:10:9-35
	android:label
		ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:8:9-41
	android:allowClearUserData
		ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:6:9-42
	android:largeHeap
		ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:9:9-33
	android:icon
		ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:7:9-38
	android:allowBackup
		ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:5:9-35
	android:usesCleartextTraffic
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:175:9-44
	android:name
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:173:9-63
activity#io.dcloud.PandoraEntry
ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:11:9-25:20
	android:screenOrientation
		ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:18:13-45
	android:label
		ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:14:13-45
	android:launchMode
		ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:15:13-44
	android:hardwareAccelerated
		ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:16:13-47
	android:windowSoftInputMode
		ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:20:13-55
	android:exported
		ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:19:13-36
	android:configChanges
		ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:13:13-83
	android:theme
		ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:17:13-52
	android:name
		ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:12:13-50
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:21:13-24:29
action#android.intent.action.MAIN
ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:22:17-69
	android:name
		ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:22:25-66
category#android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:23:17-77
	android:name
		ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:23:27-74
activity#io.dcloud.PandoraEntryActivity
ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:26:9-42:20
	android:screenOrientation
		ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:32:13-45
	android:launchMode
		ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:28:13-44
	android:hardwareAccelerated
		ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:30:13-47
	android:windowSoftInputMode
		ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:35:13-55
	android:exported
		ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:34:13-36
	android:permission
		ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:31:13-89
	android:configChanges
		ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:29:13-151
	android:theme
		ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:33:13-47
	android:name
		ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:27:13-58
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme: 
ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:36:13-41:29
category#android.intent.category.DEFAULT
ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:37:17-76
	android:name
		ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:37:27-73
category#android.intent.category.BROWSABLE
ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:38:17-78
	android:name
		ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:38:27-75
action#android.intent.action.VIEW
ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:39:17-69
	android:name
		ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:39:25-66
data
ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:40:17-44
	android:scheme
		ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:40:23-41
meta-data#dcloud_appkey
ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:43:9-45:68
	android:value
		ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:45:13-65
	android:name
		ADDED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:44:13-41
uses-sdk
INJECTED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml
INJECTED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml
MERGED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:5:5-44
MERGED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:5:5-44
MERGED from [uniapp-v8-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/be18ab0828c64768cf1ec6dfab35b9e1/transformed/jetified-uniapp-v8-release/AndroidManifest.xml:25:5-44
MERGED from [uniapp-v8-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/be18ab0828c64768cf1ec6dfab35b9e1/transformed/jetified-uniapp-v8-release/AndroidManifest.xml:25:5-44
MERGED from [android-gif-drawable-1.2.28.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/06388c87e1c288568e36099356b812fb/transformed/jetified-android-gif-drawable-1.2.28/AndroidManifest.xml:5:5-44
MERGED from [android-gif-drawable-1.2.28.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/06388c87e1c288568e36099356b812fb/transformed/jetified-android-gif-drawable-1.2.28/AndroidManifest.xml:5:5-44
MERGED from [oaid_sdk_1.0.25.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/e293a48d0426c071bb942e6321ccce3a/transformed/jetified-oaid_sdk_1.0.25/AndroidManifest.xml:8:5-11:63
MERGED from [oaid_sdk_1.0.25.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/e293a48d0426c071bb942e6321ccce3a/transformed/jetified-oaid_sdk_1.0.25/AndroidManifest.xml:8:5-11:63
MERGED from [breakpad-build-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/c1a2a37f6bfdbb795957a5bace971b01/transformed/jetified-breakpad-build-release/AndroidManifest.xml:5:5-7:41
MERGED from [breakpad-build-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/c1a2a37f6bfdbb795957a5bace971b01/transformed/jetified-breakpad-build-release/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/9eeb30f4aa8cd90015309ee238e675a8/transformed/appcompat-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/9eeb30f4aa8cd90015309ee238e675a8/transformed/appcompat-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/c72e6779054f809da409cf6a5c7e8b9b/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/c72e6779054f809da409cf6a5c7e8b9b/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:glide:4.9.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/749f7d253d6272a1512fcdb00bcd3c5b/transformed/jetified-glide-4.9.0/AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:glide:4.9.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/749f7d253d6272a1512fcdb00bcd3c5b/transformed/jetified-glide-4.9.0/AndroidManifest.xml:6:5-8:41
MERGED from [androidx.fragment:fragment:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1cd6843987475c35ab0c860d0df93693/transformed/fragment-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1cd6843987475c35ab0c860d0df93693/transformed/fragment-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/7ebe4587b02fdf6b712b324d7d3adb55/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/7ebe4587b02fdf6b712b324d7d3adb55/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6eefa1986fd028d9c2a6d06175d4f2fd/transformed/webkit-1.5.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.webkit:webkit:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6eefa1986fd028d9c2a6d06175d4f2fd/transformed/webkit-1.5.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/93773354437d2fdb0cec786de892b229/transformed/jetified-appcompat-resources-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/93773354437d2fdb0cec786de892b229/transformed/jetified-appcompat-resources-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/69203ce7a2c06f6a825dcab1d74a714d/transformed/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/69203ce7a2c06f6a825dcab1d74a714d/transformed/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2d8fa74b1f4ca5c076182437449b2295/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2d8fa74b1f4ca5c076182437449b2295/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6834ace68f8aa4ccd6a7f0606ee81fd7/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6834ace68f8aa4ccd6a7f0606ee81fd7/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/dd0a81ce75084cc0c9ff30b21bd266eb/transformed/jetified-activity-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/dd0a81ce75084cc0c9ff30b21bd266eb/transformed/jetified-activity-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/aa9620d037a8ac4812cf95f9ef11e6e0/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/aa9620d037a8ac4812cf95f9ef11e6e0/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/fe8ad3c8e1169ec770e9bcc3337117f1/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/fe8ad3c8e1169ec770e9bcc3337117f1/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f49ebcb56f1574ca0d7d82dd42d35bd4/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f49ebcb56f1574ca0d7d82dd42d35bd4/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/b208fc1fa734e0528687ea61bd7ccbb7/transformed/core-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/b208fc1fa734e0528687ea61bd7ccbb7/transformed/core-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.fresco:fresco:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/73aa1ce44b5336a1ac083af498f0d42a/transformed/jetified-fresco-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:fresco:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/73aa1ce44b5336a1ac083af498f0d42a/transformed/jetified-fresco-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:animated-gif:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a2bca994e3a1736259967e476fd2bb77/transformed/jetified-animated-gif-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:animated-gif:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a2bca994e3a1736259967e476fd2bb77/transformed/jetified-animated-gif-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/59628f70395a84a0d28721f3accb3fb4/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/59628f70395a84a0d28721f3accb3fb4/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/bce69b476ae81bfe7a2cc3a94c9ad064/transformed/versionedparcelable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/bce69b476ae81bfe7a2cc3a94c9ad064/transformed/versionedparcelable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.9.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5f5221b858618905af35acf9a5fb73d1/transformed/jetified-gifdecoder-4.9.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.9.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5f5221b858618905af35acf9a5fb73d1/transformed/jetified-gifdecoder-4.9.0/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/e0425a064789fab98c1f1af3aea6bbdd/transformed/lifecycle-runtime-2.1.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/e0425a064789fab98c1f1af3aea6bbdd/transformed/lifecycle-runtime-2.1.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/b9acdf07edc1841919e9acae38d85737/transformed/lifecycle-viewmodel-2.1.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/b9acdf07edc1841919e9acae38d85737/transformed/lifecycle-viewmodel-2.1.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/333ea104ff85e182ed4078877d282719/transformed/jetified-savedstate-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/333ea104ff85e182ed4078877d282719/transformed/jetified-savedstate-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2f00b0316b3ea715f1122055364c04a2/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/2f00b0316b3ea715f1122055364c04a2/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/84ef0e01d3ba6b69f390ee963fcaa2d6/transformed/lifecycle-livedata-2.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/84ef0e01d3ba6b69f390ee963fcaa2d6/transformed/lifecycle-livedata-2.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/9debccd1dbb3b10d3d0d289cf2bfc22a/transformed/lifecycle-livedata-core-2.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/9debccd1dbb3b10d3d0d289cf2bfc22a/transformed/lifecycle-livedata-core-2.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a51aacf585a310914b2b1f5f681568ed/transformed/core-runtime-2.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a51aacf585a310914b2b1f5f681568ed/transformed/core-runtime-2.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:soloader:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/267ef64ed29f05cdacb3f45608583489/transformed/jetified-soloader-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:soloader:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/267ef64ed29f05cdacb3f45608583489/transformed/jetified-soloader-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:animated-base:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/b9d3a55a189994f4716536c927272cf8/transformed/jetified-animated-base-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:animated-base:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/b9d3a55a189994f4716536c927272cf8/transformed/jetified-animated-base-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:animated-drawable:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/973ddde2fb359ddc8161202716873e3a/transformed/jetified-animated-drawable-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:animated-drawable:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/973ddde2fb359ddc8161202716873e3a/transformed/jetified-animated-drawable-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:drawee:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0678f6ec823ec63e3ee8dc05fdae0e6e/transformed/jetified-drawee-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:drawee:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0678f6ec823ec63e3ee8dc05fdae0e6e/transformed/jetified-drawee-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:nativeimagefilters:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1d7d66a80c027e63ebfcf8efc082c3f1/transformed/jetified-nativeimagefilters-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:nativeimagefilters:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1d7d66a80c027e63ebfcf8efc082c3f1/transformed/jetified-nativeimagefilters-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-native:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/012fd9a21cb6e5037eb2031631f9d3ab/transformed/jetified-memory-type-native-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-native:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/012fd9a21cb6e5037eb2031631f9d3ab/transformed/jetified-memory-type-native-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-java:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/28bd6483f7903ddae698d77b64ddebfd/transformed/jetified-memory-type-java-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-java:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/28bd6483f7903ddae698d77b64ddebfd/transformed/jetified-memory-type-java-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-native:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5535916c518576588eb971d8b9347977/transformed/jetified-imagepipeline-native-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-native:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5535916c518576588eb971d8b9347977/transformed/jetified-imagepipeline-native-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-ashmem:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f6e43c5f56d8533ba5c577146e7017c4/transformed/jetified-memory-type-ashmem-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-ashmem:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f6e43c5f56d8533ba5c577146e7017c4/transformed/jetified-memory-type-ashmem-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/7a966bd8b6d395727996cf742c78580e/transformed/jetified-imagepipeline-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/7a966bd8b6d395727996cf742c78580e/transformed/jetified-imagepipeline-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:nativeimagetranscoder:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/cb4d8775ab589a7390899533a823c1a4/transformed/jetified-nativeimagetranscoder-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:nativeimagetranscoder:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/cb4d8775ab589a7390899533a823c1a4/transformed/jetified-nativeimagetranscoder-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.soloader:soloader:0.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/1b73001a1a0f8a3f7dc235dbf297ffe1/transformed/jetified-soloader-0.10.1/AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/1b73001a1a0f8a3f7dc235dbf297ffe1/transformed/jetified-soloader-0.10.1/AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.fresco:middleware:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/fa624f05da5ecd48d134399877ef85f3/transformed/jetified-middleware-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:middleware:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/fa624f05da5ecd48d134399877ef85f3/transformed/jetified-middleware-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:ui-common:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/b07a43c7e15a0765f198ac5c27c8c00a/transformed/jetified-ui-common-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:ui-common:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/b07a43c7e15a0765f198ac5c27c8c00a/transformed/jetified-ui-common-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-base:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f3f51a8eb494f432708d82fa0241b327/transformed/jetified-imagepipeline-base-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-base:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f3f51a8eb494f432708d82fa0241b327/transformed/jetified-imagepipeline-base-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:fbcore:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/8c5824d83f65310b8fbcde9573bb6381/transformed/jetified-fbcore-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:fbcore:2.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/8c5824d83f65310b8fbcde9573bb6381/transformed/jetified-fbcore-2.5.0/AndroidManifest.xml:5:5-7:41
	tools:overrideLibrary
		ADDED from [oaid_sdk_1.0.25.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/e293a48d0426c071bb942e6321ccce3a/transformed/jetified-oaid_sdk_1.0.25/AndroidManifest.xml:11:9-60
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml
supports-screens
ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:7:5-12:39
	android:largeScreens
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:9:9-36
	android:smallScreens
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:12:9-36
	android:normalScreens
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:10:9-37
	android:resizeable
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:11:9-34
	android:anyDensity
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:8:9-34
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:14:5-81
	android:name
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:14:22-78
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:17:5-76
	android:name
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:17:22-73
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:18:5-75
	android:name
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:18:22-72
uses-permission#android.permission.READ_MEDIA_VISUAL_USER_SELECTED
ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:19:5-90
	android:name
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:19:22-87
uses-permission#android.permission.INTERNET
ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:20:5-67
	android:name
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:20:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:21:5-79
	android:name
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:21:22-76
uses-permission#com.huawei.android.launcher.permission.CHANGE_BADGE
ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:27:5-91
	android:name
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:27:22-88
uses-permission#com.vivo.notification.permission.BADGE_ICON
ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:29:5-83
	android:name
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:29:22-80
queries
ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:31:5-40:15
intent#action:name:android.intent.action.MAIN
ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:32:9-34:18
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.DEFAULT
ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:35:9-39:18
activity#io.dcloud.feature.nativeObj.photoview.PhotoActivity
ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:184:9-190:56
	android:screenOrientation
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:189:13-47
	android:noHistory
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:188:13-38
	android:exported
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:187:13-37
	android:configChanges
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:186:13-63
	android:theme
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:190:13-53
	android:name
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:185:13-79
activity#io.dcloud.WebAppActivity
ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:191:9-200:58
	android:screenOrientation
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:198:13-49
	android:label
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:196:13-46
	android:launchMode
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:197:13-44
	android:hardwareAccelerated
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:194:13-47
	android:windowSoftInputMode
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:200:13-55
	android:configChanges
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:193:13-144
	android:icon
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:195:13-51
	android:theme
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:199:13-47
	android:name
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:192:13-52
activity#io.dcloud.ProcessMediator
ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:201:9-205:75
	android:excludeFromRecents
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:203:13-46
	android:exported
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:204:13-37
	android:theme
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:205:13-72
	android:name
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:202:13-53
activity#io.dcloud.WebviewActivity
ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:206:9-212:20
	android:screenOrientation
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:209:13-49
	android:windowSoftInputMode
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:211:13-52
	android:exported
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:208:13-37
	android:theme
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:210:13-61
	android:name
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:207:13-53
activity#com.dmcbig.mediapicker.PickerActivity
ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:215:9-220:63
	android:windowSoftInputMode
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:220:13-60
	android:exported
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:218:13-37
	android:configChanges
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:217:13-59
	android:theme
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:219:13-55
	android:name
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:216:13-65
activity#com.dmcbig.mediapicker.PreviewActivity
ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:221:9-226:63
	android:windowSoftInputMode
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:226:13-60
	android:exported
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:224:13-37
	android:configChanges
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:223:13-59
	android:theme
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:225:13-55
	android:name
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:222:13-66
provider#io.dcloud.common.util.DCloud_FileProvider
ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:229:9-237:20
	android:grantUriPermissions
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:233:13-47
	android:authorities
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:231:13-67
	android:exported
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:232:13-37
	android:name
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:230:13-69
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:234:13-236:64
	android:resource
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:236:17-61
	android:name
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:235:17-67
meta-data#android.max_aspect
ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:244:9-246:35
	android:value
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:246:13-32
	android:name
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:245:13-46
activity#io.dcloud.feature.gallery.imageedit.IMGEditActivity
ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:248:9-253:63
	android:windowSoftInputMode
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:253:13-60
	android:exported
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:251:13-37
	android:configChanges
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:250:13-74
	android:theme
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:252:13-50
	android:name
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:249:13-79
activity#io.dcloud.sdk.activity.WebViewActivity
ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:256:9-261:55
	android:screenOrientation
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:259:13-49
	android:windowSoftInputMode
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:261:13-52
	android:exported
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:258:13-37
	android:theme
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:260:13-51
	android:name
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:257:13-66
service#io.dcloud.sdk.base.service.DownloadService
ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:263:9-266:72
	android:exported
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:265:13-37
	android:permission
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:266:13-69
	android:name
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:264:13-70
provider#io.dcloud.sdk.base.service.provider.DCloudAdFileProvider
ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:268:9-276:20
	android:grantUriPermissions
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:272:13-47
	android:authorities
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:270:13-67
	android:exported
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:271:13-37
	android:name
		ADDED from [lib.5plus.base-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/0e11fdd0f7cc79913383886944f13f9a/transformed/jetified-lib.5plus.base-release/AndroidManifest.xml:269:13-84
uses-permission#android.permission.READ_EXTERNAL_STORAGE
IMPLIED from /Users/<USER>/Project/rongye/rongye-android/rongye/src/main/AndroidManifest.xml:2:1-48:12 reason: io.dcloud.base requested WRITE_EXTERNAL_STORAGE
receiver#com.taobao.weex.WXGlobalEventReceiver
ADDED from [uniapp-v8-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/be18ab0828c64768cf1ec6dfab35b9e1/transformed/jetified-uniapp-v8-release/AndroidManifest.xml:28:9-32:20
	android:enabled
		ADDED from [uniapp-v8-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/be18ab0828c64768cf1ec6dfab35b9e1/transformed/jetified-uniapp-v8-release/AndroidManifest.xml:30:13-35
	android:exported
		ADDED from [uniapp-v8-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/be18ab0828c64768cf1ec6dfab35b9e1/transformed/jetified-uniapp-v8-release/AndroidManifest.xml:31:13-37
	android:name
		ADDED from [uniapp-v8-release.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/be18ab0828c64768cf1ec6dfab35b9e1/transformed/jetified-uniapp-v8-release/AndroidManifest.xml:29:13-65
uses-permission#com.asus.msa.SupplementaryDID.ACCESS
ADDED from [oaid_sdk_1.0.25.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/e293a48d0426c071bb942e6321ccce3a/transformed/jetified-oaid_sdk_1.0.25/AndroidManifest.xml:13:5-76
	android:name
		ADDED from [oaid_sdk_1.0.25.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/e293a48d0426c071bb942e6321ccce3a/transformed/jetified-oaid_sdk_1.0.25/AndroidManifest.xml:13:22-73
uses-permission#freemme.permission.msa
ADDED from [oaid_sdk_1.0.25.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/e293a48d0426c071bb942e6321ccce3a/transformed/jetified-oaid_sdk_1.0.25/AndroidManifest.xml:15:5-62
	android:name
		ADDED from [oaid_sdk_1.0.25.aar] /Users/<USER>/.gradle/caches/8.11.1/transforms/e293a48d0426c071bb942e6321ccce3a/transformed/jetified-oaid_sdk_1.0.25/AndroidManifest.xml:15:22-59
