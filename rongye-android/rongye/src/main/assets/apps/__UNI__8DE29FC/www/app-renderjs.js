var __renderjsModules={};
__renderjsModules["09897cbd"]=(()=>{var d=Object.defineProperty;var s=Object.getOwnPropertyDescriptor;var a=Object.getOwnPropertyNames;var l=Object.prototype.hasOwnProperty;var h=(e,t)=>{for(var i in t)d(e,i,{get:t[i],enumerable:!0})},v=(e,t,i,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let n of a(t))!l.call(e,n)&&n!==i&&d(e,n,{get:()=>t[n],enumerable:!(o=s(t,n))||o.enumerable});return e};var m=e=>v(d({},"__esModule",{value:!0}),e);var p={};h(p,{default:()=>u});var u={data(){return{video:null,num:"",options:{}}},mounted(){this.initVideoEvent()},methods:{initVideoEvent(){setTimeout(()=>{let e=document.getElementById(`dom-html-video_${this.num}`);this.video=e,e.addEventListener("play",()=>{this.$ownerInstance.callMethod("videoEvent","play")}),e.addEventListener("pause",()=>{this.$ownerInstance.callMethod("videoEvent","pause")}),e.addEventListener("ended",()=>{this.$ownerInstance.callMethod("videoEvent","ended"),this.$ownerInstance.callMethod("resetEventDrive")})},100)},eventHandle(e){e&&(this.video=document.getElementById(`dom-html-video_${this.num}`),e==="play"?this.video.play():e==="pause"?this.video.pause():e==="stop"&&this.video.stop())},srcChange(e){this.initVideoEvent(),setTimeout(()=>{let t=document.getElementById(`dom-html-video_${this.num}`);t.addEventListener("loadedmetadata",()=>{let{autoplay:i}=this.options;t.play(),i||t.pause()})},0)},propsChange(e){this.options=e},randomNumChange(e){this.num=e}}};return m(p);})();
