<template>
	<view class="register">

		<view class="content">
			<!-- 头部logo -->
			<view class="header">
				<image :src="logoImage" mode="widthFix"></image>
			</view>
			<!-- 主体 -->
			<view class="main">
				<wInput v-model="username" type="text" placeholder="账号"></wInput>
				<wInput v-model="password" type="password" placeholder="密码" isShowPass></wInput>
				<wInput v-model="confirmPassword" type="password" placeholder="确认密码" isShowPass></wInput>

				<view class="tips">邀请码</view>
				<wInput v-model="inviteCode" disabled type="text" maxlength="11" placeholder="邀请码"></wInput>
				<!-- <wInput v-model="verCode" type="number" maxlength="4" placeholder="验证码" isShowCode ref="runCode"
					@setCode="getVerCode()"></wInput> -->

			</view>

			<wButton class="wbutton" text="注 册" :rotate="isRotate" @click.native="startReg()"></wButton>

			<view class="download-btn">
				<view class="btn" @tap="toDownload">下载APP</view>
			</view>

			<!-- 底部信息 -->
			<view class="footer">
				<text @tap="isShowAgree" class="cuIcon" :class="showAgree ? 'cuIcon-radiobox' : 'cuIcon-round'">
					同意</text>
				<!-- 协议地址 -->
				<navigator url="" open-type="navigate">《协议》</navigator>
			</view>
		</view>
	</view>
</template>

<script>
let _this;
import wInput from '../../components/watch-login/watch-input.vue' //input
import wButton from '../../components/watch-login/watch-button.vue' //button
import { API_URL } from '@/env.js' //api地址

export default {
	data() {
		return {
			//logo图片 base64
			logoImage: '../../static/image/logo.jpg',
			username: '', // 用户/电话
			password: '', //密码
			confirmPassword: '', //确认密码
			verCode: "", //验证码
			showAgree: true, //协议是否选择
			isRotate: false, //是否加载旋转
			spm: '',
			inviteCode: null,
		}
	},
	components: {
		wInput,
		wButton,
	},
	onLoad(options) {
		this.spm = options.spm || ''
		if (this.spm != '') {
			// .分割spm
			let spmArr = this.spm.split('.')
			if (spmArr.length > 1) {
				this.inviteCode = spmArr[0]
			}
		}
	},
	mounted() {
		_this = this;
		console.log(API_URL)
	},
	methods: {
		isShowAgree() {
			//是否选择协议
			_this.showAgree = !_this.showAgree;
		},
		getVerCode() {
			//获取验证码
			if (_this.phoneData.length != 11) {
				uni.showToast({
					icon: 'none',
					position: 'bottom',
					title: '手机号不正确'
				});
				return false;
			}
			console.log("获取验证码")
			this.$refs.runCode.$emit('runCode'); //触发倒计时（一般用于请求成功验证码后调用）
			uni.showToast({
				icon: 'none',
				position: 'bottom',
				title: '模拟倒计时触发'
			});

			setTimeout(function () {
				_this.$refs.runCode.$emit('runCode', 0); //假装模拟下需要 终止倒计时
				uni.showToast({
					icon: 'none',
					position: 'bottom',
					title: '模拟倒计时终止'
				});
			}, 3000)
		},
		toDownload() {
			// #ifdef H5
			window.location.href = '/download/'
			// #endif
		},
		startReg() {
			//注册
			if (_this.username == '') {
				uni.showToast({
					icon: 'none',
					title: '请输入用户名'
				});
				return false;
			}
			if (_this.password == '') {
				uni.showToast({
					icon: 'none',
					title: '请输入密码'
				});
				return false;
			}
			if (_this.confirmPassword != _this.password) {
				uni.showToast({
					icon: 'none',
					title: '两次密码不一致'
				});
				return false;
			}
			if (!_this.showAgree) {
				uni.showToast({
					icon: 'none',
					title: '请同意协议'
				});
				return false;
			}

			uni.request({
				url: API_URL + '/user.user/smsRegister',
				data: {
					username: _this.username,
					code: _this.verCode,
					password: _this.password,
					invite_code: _this.inviteCode,
				},
				header: {
					'content-type': 'application/json'
				},
				method: 'POST',
				success: (res) => {
					var data = res.data;
					uni.showToast({
						title: data.msg,
						icon: 'none',
					})
					if (data.code == 1) {
						setTimeout(() => {
							_this.toDownload()
						}, 1000)
					}
				}
			})

		}
	}
}
</script>

<style>
@import url("../../components/watch-login/css/icon.css");
@import url("./css/main.css");

.tips {
	font-size: 28rpx;
	color: #999;
	margin-top: 20rpx;
}

.download-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 30rpx;
}

.download-btn .btn {
	display: inline-block;
	padding: 10rpx 20rpx;
	border-radius: 20rpx;
	font-size: 28rpx;
	color: #333;
	text-align: center;
}
</style>