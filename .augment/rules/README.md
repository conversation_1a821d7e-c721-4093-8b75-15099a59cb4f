---
type: "always_apply"
---

# 融业港项目开发规则文档总览

## 文档说明

本目录包含融业港项目的完整开发规则文档，旨在规范开发流程、提高代码质量、确保团队协作效率。

## 规则文档列表

### 1. [代码规范 (coding-standards.md)](./coding-standards.md)
**适用范围**: 前端和后端代码开发
**主要内容**:
- 通用编码规范（文件编码、缩进、命名等）
- 前端Vue3+uni-app代码规范
- 后端PHP+ThinkPHP5代码规范
- 注释规范和错误处理规范
- 代码质量检查清单

### 2. [开发流程规范 (development-workflow.md)](./development-workflow.md)
**适用范围**: 所有开发活动
**主要内容**:
- Git分支管理策略
- 提交信息规范
- 代码审查流程
- 测试要求和部署流程
- 问题处理和团队协作规范

### 3. [业务逻辑规范 (business-logic.md)](./business-logic.md)
**适用范围**: 核心业务功能开发
**主要内容**:
- 分润系统开发规范
- 钱包系统开发规范
- 用户等级管理规范
- 复购判定规范
- 数据一致性保证和性能优化

### 4. [API设计规范 (api-design.md)](./api-design.md)
**适用范围**: 前后端接口设计
**主要内容**:
- RESTful API设计原则
- 请求和响应格式规范
- 认证授权机制
- 参数验证和错误处理
- 接口版本控制和文档规范

### 5. [数据库设计规范 (database-design.md)](./database-design.md)
**适用范围**: 数据库相关开发
**主要内容**:
- 表和字段命名规范
- 索引设计规范
- 数据迁移规范
- 性能优化和安全规范
- 数据字典维护

### 6. [项目特定规范 (project-specific.md)](./project-specific.md)
**适用范围**: 融业港项目特定需求
**主要内容**:
- 多平台兼容性规范
- 性能优化规范
- 安全要求规范
- 第三方集成规范
- 错误监控和日志规范

### 7. [前端特定规范 (frontend-specific.md)](./frontend-specific.md)
**适用范围**: 前端开发
**主要内容**:
- uni-app项目结构规范
- Vue3组件开发规范
- Pinia状态管理规范
- API接口调用规范
- 路由和页面管理规范

### 8. [后端特定规范 (backend-specific.md)](./backend-specific.md)
**适用范围**: 后端开发
**主要内容**:
- FastAdmin+ThinkPHP5项目结构
- 控制器和模型开发规范
- 中间件开发规范
- 验证器和服务层规范
- 配置管理规范

## 规则优先级

### 🔴 必须遵守（P0）
- 代码规范中的基础规范
- 业务逻辑规范中的数据一致性要求
- API设计规范中的安全要求
- 数据库设计规范中的命名和索引规范

### 🟡 强烈建议（P1）
- 开发流程规范中的Git和代码审查流程
- 项目特定规范中的性能优化要求
- 前后端特定规范中的架构设计

### 🟢 建议遵守（P2）
- 注释和文档规范
- 代码风格的细节要求
- 工具和辅助功能的使用建议

## 规则执行机制

### 1. 自动化检查
- **代码格式**: 使用Prettier、ESLint、PHP CodeSniffer等工具
- **提交规范**: 使用Git hooks检查提交信息格式
- **测试覆盖率**: CI/CD流程中自动检查测试覆盖率

### 2. 代码审查
- 所有代码变更必须经过代码审查
- 审查者需要检查规范遵守情况
- 不符合规范的代码不允许合并

### 3. 定期检查
- 每周进行代码质量检查
- 每月进行规范执行情况回顾
- 根据项目发展调整规范内容

## 违规处理

### 轻微违规
- **定义**: 代码风格、注释不完整等
- **处理**: 代码审查时指出，要求修改后合并

### 严重违规
- **定义**: 安全漏洞、数据一致性问题、架构违反等
- **处理**: 拒绝合并，要求重新开发

### 重复违规
- **定义**: 同一开发者多次违反同类规范
- **处理**: 团队内部讨论，加强培训

## 规范更新机制

### 1. 更新触发条件
- 项目需求变化
- 技术栈升级
- 团队反馈和建议
- 行业最佳实践更新

### 2. 更新流程
1. 提出更新建议
2. 团队讨论和评估
3. 更新规范文档
4. 通知团队成员
5. 培训和推广

### 3. 版本管理
- 规范文档使用Git进行版本控制
- 重要更新需要标记版本号
- 保留历史版本供参考

## 工具和资源

### 开发工具
- **前端**: VSCode + Vetur + ESLint + Prettier
- **后端**: PhpStorm + PHP CodeSniffer + PHPStan
- **数据库**: Navicat + MySQL Workbench
- **版本控制**: Git + GitHub/GitLab

### 文档工具
- **API文档**: Postman + Swagger
- **项目文档**: Markdown + GitBook
- **流程图**: Draw.io + Mermaid

### 监控工具
- **错误监控**: Sentry
- **性能监控**: New Relic
- **日志分析**: ELK Stack

## 学习资源

### 官方文档
- [uni-app官方文档](https://uniapp.dcloud.io/)
- [Vue3官方文档](https://v3.vuejs.org/)
- [ThinkPHP5官方文档](https://www.thinkphp.cn/topic/2.html)
- [FastAdmin官方文档](https://www.fastadmin.net/)

### 最佳实践
- [Vue.js风格指南](https://v3.vuejs.org/style-guide/)
- [PHP编码规范PSR](https://www.php-fig.org/psr/)
- [RESTful API设计指南](https://restfulapi.net/)

## 常见问题

### Q: 如何处理规范冲突？
A: 按照优先级处理，P0规范优先于P1和P2。如有疑问，提交团队讨论。

### Q: 新技术如何融入现有规范？
A: 先进行技术调研和小范围试验，确认可行后更新相关规范文档。

### Q: 如何平衡规范严格性和开发效率？
A: 核心规范（安全、数据一致性）必须严格执行，风格类规范可以适当灵活。

### Q: 规范执行遇到阻力怎么办？
A: 加强培训和沟通，说明规范的必要性，必要时调整规范内容。

## 联系方式

如有规范相关问题或建议，请通过以下方式联系：
- 技术负责人: [联系方式]
- 项目经理: [联系方式]
- 团队群组: [群组信息]

---

**最后更新**: 2024年12月
**文档版本**: v1.0
**维护者**: 融业港开发团队

*本规范文档是团队协作的基础，请所有开发人员认真学习并严格执行。*
