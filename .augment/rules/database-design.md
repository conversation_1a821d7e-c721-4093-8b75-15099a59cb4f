---
type: "manual"
---

# 融业港项目数据库设计规范

## 适用范围
本规范适用于融业港项目的所有数据库设计，包括表结构设计、索引设计、数据迁移等。

## 1. 表命名规范

### 1.1 表名规范
- 使用统一前缀：`sys_`
- 使用小写字母和下划线
- 表名应该是复数形式或描述性名词
- 避免使用MySQL保留字

```sql
-- ✅ 正确示例
sys_users           -- 用户表
sys_orders          -- 订单表
sys_user_wallets    -- 用户钱包表
sys_bonus_logs      -- 分润日志表
sys_wallet_logs     -- 钱包流水表

-- ❌ 错误示例
User               -- 首字母大写
sys_user           -- 单数形式
order              -- 没有前缀，且是保留字
sys_userWallet     -- 驼峰命名
```

### 1.2 字段命名规范
- 使用小写字母和下划线
- 主键统一使用 `id`
- 外键使用 `表名_id` 格式
- 布尔字段使用 `is_` 前缀
- 时间字段使用 `_time` 后缀

```sql
-- ✅ 正确示例
CREATE TABLE sys_users (
    id int(11) NOT NULL AUTO_INCREMENT,
    username varchar(50) NOT NULL COMMENT '用户名',
    mobile varchar(11) NOT NULL COMMENT '手机号',
    password varchar(255) NOT NULL COMMENT '密码',
    nickname varchar(50) DEFAULT NULL COMMENT '昵称',
    avatar varchar(255) DEFAULT NULL COMMENT '头像',
    user_group varchar(20) DEFAULT 'normal' COMMENT '用户组',
    parent_id int(11) DEFAULT 0 COMMENT '上级用户ID',
    is_active tinyint(1) DEFAULT 1 COMMENT '是否激活',
    create_time int(11) NOT NULL COMMENT '创建时间',
    update_time int(11) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (id)
);

-- ❌ 错误示例
CREATE TABLE sys_users (
    userId int(11) NOT NULL AUTO_INCREMENT,    -- 驼峰命名
    userName varchar(50) NOT NULL,             -- 驼峰命名
    parentUserId int(11) DEFAULT 0,            -- 外键命名不规范
    active tinyint(1) DEFAULT 1,               -- 布尔字段缺少is_前缀
    createTime int(11) NOT NULL,               -- 驼峰命名
    PRIMARY KEY (userId)                       -- 主键名不规范
);
```

## 2. 字段类型规范

### 2.1 常用字段类型
```sql
-- 整数类型
id              int(11) NOT NULL AUTO_INCREMENT     -- 主键
user_id         int(11) NOT NULL                    -- 外键
quantity        int(11) DEFAULT 0                   -- 数量
is_active       tinyint(1) DEFAULT 1                -- 布尔值

-- 字符串类型
username        varchar(50) NOT NULL                -- 用户名
mobile          varchar(11) NOT NULL                -- 手机号
email           varchar(100) DEFAULT NULL           -- 邮箱
content         text                                -- 长文本
remark          varchar(500) DEFAULT NULL           -- 备注

-- 金额类型（使用decimal避免精度问题）
amount          decimal(10,2) DEFAULT 0.00          -- 金额
balance         decimal(10,2) DEFAULT 0.00          -- 余额
price           decimal(8,2) DEFAULT 0.00           -- 价格

-- 时间类型
create_time     int(11) NOT NULL                    -- 创建时间（时间戳）
update_time     int(11) NOT NULL                    -- 更新时间（时间戳）
```

### 2.2 字段长度规范
```sql
-- 常用字段长度建议
username        varchar(50)     -- 用户名
password        varchar(255)    -- 密码（加密后）
mobile          varchar(11)     -- 手机号
email           varchar(100)    -- 邮箱
nickname        varchar(50)     -- 昵称
title           varchar(200)    -- 标题
url             varchar(500)    -- URL地址
ip_address      varchar(45)     -- IP地址（支持IPv6）
```

## 3. 索引设计规范

### 3.1 主键索引
```sql
-- 每个表必须有主键
PRIMARY KEY (id)

-- 复合主键（特殊情况）
PRIMARY KEY (user_id, product_id)
```

### 3.2 唯一索引
```sql
-- 用户表唯一索引
UNIQUE KEY uk_username (username),
UNIQUE KEY uk_mobile (mobile),
UNIQUE KEY uk_email (email)

-- 订单表唯一索引
UNIQUE KEY uk_order_no (order_no)
```

### 3.3 普通索引
```sql
-- 外键索引
KEY idx_user_id (user_id),
KEY idx_parent_id (parent_id),

-- 状态索引
KEY idx_status (status),
KEY idx_is_active (is_active),

-- 时间索引
KEY idx_create_time (create_time),
KEY idx_update_time (update_time),

-- 复合索引
KEY idx_user_status (user_id, status),
KEY idx_create_status (create_time, status)
```

### 3.4 索引设计原则
```sql
-- ✅ 正确示例：根据查询需求设计索引
-- 查询：WHERE user_id = ? AND status = ? ORDER BY create_time DESC
KEY idx_user_status_time (user_id, status, create_time)

-- 查询：WHERE mobile = ?
UNIQUE KEY uk_mobile (mobile)

-- ❌ 错误示例：过多的单列索引
KEY idx_user_id (user_id),
KEY idx_status (status),
KEY idx_create_time (create_time)
-- 应该合并为复合索引
```

## 4. 表结构设计示例

### 4.1 用户表设计
```sql
CREATE TABLE sys_users (
    id int(11) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    username varchar(50) NOT NULL COMMENT '用户名',
    mobile varchar(11) NOT NULL COMMENT '手机号',
    password varchar(255) NOT NULL COMMENT '密码',
    nickname varchar(50) DEFAULT NULL COMMENT '昵称',
    avatar varchar(255) DEFAULT NULL COMMENT '头像URL',
    user_group varchar(20) DEFAULT 'normal' COMMENT '用户组：normal,entrepreneur,advisor,manager,director,wisdom_director',
    parent_id int(11) DEFAULT 0 COMMENT '上级用户ID',
    parent_user_path varchar(500) DEFAULT '' COMMENT '上级用户路径，用逗号分隔',
    level int(11) DEFAULT 1 COMMENT '用户层级',
    is_active tinyint(1) DEFAULT 1 COMMENT '是否激活：0-未激活，1-已激活',
    last_login_time int(11) DEFAULT 0 COMMENT '最后登录时间',
    last_login_ip varchar(45) DEFAULT NULL COMMENT '最后登录IP',
    create_time int(11) NOT NULL COMMENT '创建时间',
    update_time int(11) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_username (username),
    UNIQUE KEY uk_mobile (mobile),
    KEY idx_parent_id (parent_id),
    KEY idx_user_group (user_group),
    KEY idx_is_active (is_active),
    KEY idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
```

### 4.2 用户钱包表设计
```sql
CREATE TABLE sys_user_wallets (
    id int(11) NOT NULL AUTO_INCREMENT COMMENT '钱包ID',
    user_id int(11) NOT NULL COMMENT '用户ID',
    balance decimal(10,2) DEFAULT 0.00 COMMENT '余额',
    frozen_balance decimal(10,2) DEFAULT 0.00 COMMENT '冻结余额',
    total_recharge decimal(10,2) DEFAULT 0.00 COMMENT '累计充值',
    total_withdraw decimal(10,2) DEFAULT 0.00 COMMENT '累计提现',
    total_bonus decimal(10,2) DEFAULT 0.00 COMMENT '累计分润',
    points int(11) DEFAULT 0 COMMENT '积分',
    create_time int(11) NOT NULL COMMENT '创建时间',
    update_time int(11) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_user_id (user_id),
    KEY idx_balance (balance),
    KEY idx_update_time (update_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户钱包表';
```

### 4.3 钱包流水表设计
```sql
CREATE TABLE sys_wallet_logs (
    id int(11) NOT NULL AUTO_INCREMENT COMMENT '流水ID',
    user_id int(11) NOT NULL COMMENT '用户ID',
    amount decimal(10,2) NOT NULL COMMENT '变动金额',
    type varchar(20) NOT NULL COMMENT '变动类型：recharge,withdraw,bonus,consume,refund',
    before_balance decimal(10,2) NOT NULL COMMENT '变动前余额',
    after_balance decimal(10,2) NOT NULL COMMENT '变动后余额',
    order_id int(11) DEFAULT 0 COMMENT '关联订单ID',
    remark varchar(500) DEFAULT NULL COMMENT '备注',
    create_time int(11) NOT NULL COMMENT '创建时间',
    PRIMARY KEY (id),
    KEY idx_user_id (user_id),
    KEY idx_type (type),
    KEY idx_order_id (order_id),
    KEY idx_create_time (create_time),
    KEY idx_user_type_time (user_id, type, create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='钱包流水表';
```

### 4.4 分润记录表设计
```sql
CREATE TABLE sys_bonus_logs (
    id int(11) NOT NULL AUTO_INCREMENT COMMENT '分润ID',
    order_id int(11) NOT NULL COMMENT '订单ID',
    from_user_id int(11) NOT NULL COMMENT '产生分润的用户ID',
    to_user_id int(11) NOT NULL COMMENT '获得分润的用户ID',
    bonus_amount decimal(10,2) NOT NULL COMMENT '分润金额',
    bonus_type varchar(20) NOT NULL COMMENT '分润类型：direct,indirect',
    user_level varchar(20) NOT NULL COMMENT '获得分润时的用户等级',
    level_diff int(11) NOT NULL COMMENT '层级差',
    status varchar(20) DEFAULT 'pending' COMMENT '状态：pending,completed,failed',
    create_time int(11) NOT NULL COMMENT '创建时间',
    update_time int(11) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (id),
    KEY idx_order_id (order_id),
    KEY idx_from_user_id (from_user_id),
    KEY idx_to_user_id (to_user_id),
    KEY idx_status (status),
    KEY idx_create_time (create_time),
    KEY idx_to_user_time (to_user_id, create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分润记录表';
```

## 5. 数据迁移规范

### 5.1 迁移文件命名
```
database_migrations/
├── 20241201_create_users_table.sql
├── 20241202_create_user_wallets_table.sql
├── 20241203_add_parent_user_path_to_users.sql
└── 20241204_create_bonus_logs_table.sql
```

### 5.2 迁移文件格式
```sql
-- 20241201_create_users_table.sql
-- 创建用户表

-- 创建表
CREATE TABLE sys_users (
    -- 表结构定义
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 插入初始数据（如果需要）
INSERT INTO sys_users (username, mobile, password, user_group, create_time, update_time) VALUES
('admin', '13800138000', '$2y$10$...', 'admin', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 回滚语句（注释形式）
-- DROP TABLE IF EXISTS sys_users;
```

### 5.3 字段修改迁移
```sql
-- 20241203_add_parent_user_path_to_users.sql
-- 添加用户上级路径字段

-- 添加字段
ALTER TABLE sys_users 
ADD COLUMN parent_user_path varchar(500) DEFAULT '' COMMENT '上级用户路径，用逗号分隔' 
AFTER parent_id;

-- 添加索引
ALTER TABLE sys_users 
ADD KEY idx_parent_user_path (parent_user_path(100));

-- 更新现有数据（如果需要）
-- UPDATE sys_users SET parent_user_path = CONCAT(parent_id) WHERE parent_id > 0;

-- 回滚语句（注释形式）
-- ALTER TABLE sys_users DROP COLUMN parent_user_path;
-- ALTER TABLE sys_users DROP KEY idx_parent_user_path;
```

## 6. 数据库性能优化

### 6.1 查询优化
```sql
-- ✅ 正确示例：使用索引
SELECT * FROM sys_orders 
WHERE user_id = 123 AND status = 'paid' 
ORDER BY create_time DESC 
LIMIT 20;

-- 对应索引
KEY idx_user_status_time (user_id, status, create_time)

-- ❌ 错误示例：全表扫描
SELECT * FROM sys_orders 
WHERE YEAR(FROM_UNIXTIME(create_time)) = 2024;

-- 应该改为
SELECT * FROM sys_orders 
WHERE create_time >= UNIX_TIMESTAMP('2024-01-01') 
AND create_time < UNIX_TIMESTAMP('2025-01-01');
```

### 6.2 分页优化
```sql
-- ✅ 正确示例：使用覆盖索引优化深分页
SELECT o.* FROM sys_orders o
INNER JOIN (
    SELECT id FROM sys_orders 
    WHERE user_id = 123 
    ORDER BY create_time DESC 
    LIMIT 1000, 20
) t ON o.id = t.id;

-- ❌ 错误示例：深分页性能差
SELECT * FROM sys_orders 
WHERE user_id = 123 
ORDER BY create_time DESC 
LIMIT 1000, 20;
```

## 7. 数据安全规范

### 7.1 敏感数据处理
```sql
-- 密码字段必须加密存储
password varchar(255) NOT NULL COMMENT '密码（加密）',

-- 手机号部分加密显示（应用层处理）
-- 13800138000 -> 138****8000

-- 身份证号加密存储
id_card varchar(255) DEFAULT NULL COMMENT '身份证号（加密）',
```

### 7.2 数据备份策略
```bash
# 每日备份脚本
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/mysql"
DB_NAME="rongye"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump -h127.0.0.1 -uroot -proot $DB_NAME > $BACKUP_DIR/rongye_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/rongye_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "rongye_*.sql.gz" -mtime +7 -delete
```

## 8. 数据字典维护

### 8.1 表注释规范
```sql
-- 每个表必须有注释
CREATE TABLE sys_users (
    -- 字段定义
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 每个字段必须有注释
id int(11) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
username varchar(50) NOT NULL COMMENT '用户名',
```

### 8.2 枚举值文档
```sql
-- 用户组枚举值
user_group varchar(20) DEFAULT 'normal' COMMENT '用户组：normal-普通用户,entrepreneur-创业销售商,advisor-拓业顾问,manager-守业经理,director-享业董事,wisdom_director-智慧董事'

-- 订单状态枚举值
status varchar(20) DEFAULT 'pending' COMMENT '订单状态：pending-待支付,paid-已支付,shipped-已发货,completed-已完成,cancelled-已取消'
```

## 9. 检查清单

### 表设计检查
- [ ] 表名使用统一前缀和命名规范
- [ ] 字段名使用下划线命名法
- [ ] 每个表都有主键
- [ ] 外键字段有对应索引
- [ ] 金额字段使用decimal类型
- [ ] 时间字段使用int类型存储时间戳
- [ ] 每个表和字段都有注释
- [ ] 枚举值在注释中说明

### 索引设计检查
- [ ] 根据查询需求设计索引
- [ ] 避免过多的单列索引
- [ ] 复合索引字段顺序合理
- [ ] 唯一约束使用唯一索引

### 性能检查
- [ ] 避免使用SELECT *
- [ ] 大表查询使用LIMIT
- [ ] 避免在WHERE子句中使用函数
- [ ] 合理使用JOIN查询

---
*数据库设计是系统的基础，任何结构变更都必须通过迁移脚本进行，并做好数据备份。*
