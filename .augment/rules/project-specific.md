---
type: "always_apply"
---

# 融业港项目特定开发规范

## 适用范围
本规范适用于融业港项目的特定业务需求和技术要求，包括多平台兼容性、性能优化、安全要求等。

## 1. 多平台兼容性规范

### 1.1 uni-app跨平台开发规范
```javascript
// ✅ 正确示例：使用uni-app API
// 获取系统信息
const systemInfo = uni.getSystemInfoSync()
console.log('平台:', systemInfo.platform)

// 页面跳转
uni.navigateTo({
  url: '/pages/user/profile'
})

// 显示提示
uni.showToast({
  title: '操作成功',
  icon: 'success'
})

// ❌ 错误示例：使用平台特定API
// window.location.href = '/pages/user/profile'  // 仅H5可用
// wx.showToast({ title: '操作成功' })           // 仅微信小程序可用
```

### 1.2 条件编译使用规范
```vue
<template>
  <view class="container">
    <!-- #ifdef H5 -->
    <view class="h5-header">H5专用头部</view>
    <!-- #endif -->
    
    <!-- #ifdef MP-WEIXIN -->
    <view class="mp-header">小程序专用头部</view>
    <!-- #endif -->
    
    <!-- #ifdef APP-PLUS -->
    <view class="app-header">APP专用头部</view>
    <!-- #endif -->
    
    <view class="content">通用内容</view>
  </view>
</template>

<script>
export default {
  methods: {
    handleShare() {
      // #ifdef H5
      this.shareToH5()
      // #endif
      
      // #ifdef MP-WEIXIN
      this.shareToWeixin()
      // #endif
      
      // #ifdef APP-PLUS
      this.shareToApp()
      // #endif
    }
  }
}
</script>
```

### 1.3 样式兼容性规范
```scss
// 使用rpx单位保证多端一致性
.user-card {
  width: 690rpx;
  height: 200rpx;
  padding: 20rpx;
  margin: 20rpx auto;
  
  // 安全区域适配
  /* #ifdef APP-PLUS */
  padding-top: calc(20rpx + var(--status-bar-height));
  /* #endif */
  
  .avatar {
    width: 100rpx;
    height: 100rpx;
    border-radius: 50rpx;
  }
  
  .username {
    font-size: 32rpx;
    color: #333;
    
    // 小程序特殊样式
    /* #ifdef MP-WEIXIN */
    font-weight: 500;
    /* #endif */
  }
}
```

## 2. 性能优化规范

### 2.1 图片优化规范
```javascript
// ✅ 正确示例：图片懒加载和压缩
<template>
  <image 
    :src="optimizeImageUrl(item.image)" 
    mode="aspectFill"
    lazy-load
    @load="onImageLoad"
    @error="onImageError"
  />
</template>

<script>
export default {
  methods: {
    // 图片URL优化
    optimizeImageUrl(url, width = 750, quality = 80) {
      if (!url) return '/static/default-image.png'
      
      // 如果是本地图片，直接返回
      if (url.startsWith('/static/')) return url
      
      // 添加压缩参数
      const separator = url.includes('?') ? '&' : '?'
      return `${url}${separator}w=${width}&q=${quality}`
    },
    
    onImageLoad() {
      console.log('图片加载成功')
    },
    
    onImageError() {
      console.log('图片加载失败，使用默认图片')
      // 可以设置默认图片
    }
  }
}
</script>
```

### 2.2 列表性能优化
```vue
<template>
  <scroll-view 
    scroll-y 
    class="scroll-container"
    @scrolltolower="loadMore"
    :refresher-enabled="true"
    @refresherrefresh="onRefresh"
  >
    <view 
      v-for="item in list" 
      :key="item.id"
      class="list-item"
    >
      <!-- 使用v-show而不是v-if来避免频繁的DOM操作 -->
      <view v-show="item.visible" class="item-content">
        {{ item.title }}
      </view>
    </view>
    
    <!-- 加载更多 -->
    <uni-load-more 
      :status="loadStatus"
      @clickLoadMore="loadMore"
    />
  </scroll-view>
</template>

<script>
export default {
  data() {
    return {
      list: [],
      page: 1,
      limit: 20,
      loadStatus: 'more', // more, loading, noMore
      isRefreshing: false
    }
  },
  
  methods: {
    async loadData(isRefresh = false) {
      if (isRefresh) {
        this.page = 1
        this.list = []
      }
      
      this.loadStatus = 'loading'
      
      try {
        const { data } = await this.$api.getList({
          page: this.page,
          limit: this.limit
        })
        
        if (isRefresh) {
          this.list = data.list
        } else {
          this.list.push(...data.list)
        }
        
        // 判断是否还有更多数据
        if (data.list.length < this.limit) {
          this.loadStatus = 'noMore'
        } else {
          this.loadStatus = 'more'
          this.page++
        }
      } catch (error) {
        this.loadStatus = 'more'
        uni.showToast({ title: '加载失败', icon: 'none' })
      }
    },
    
    async loadMore() {
      if (this.loadStatus !== 'more') return
      await this.loadData()
    },
    
    async onRefresh() {
      this.isRefreshing = true
      await this.loadData(true)
      this.isRefreshing = false
    }
  }
}
</script>
```

### 2.3 数据缓存规范
```javascript
// 缓存工具类
class CacheManager {
  constructor() {
    this.cache = new Map()
    this.expireTime = new Map()
  }
  
  // 设置缓存
  set(key, value, expire = 5 * 60 * 1000) { // 默认5分钟
    this.cache.set(key, value)
    this.expireTime.set(key, Date.now() + expire)
  }
  
  // 获取缓存
  get(key) {
    const expireTime = this.expireTime.get(key)
    
    if (!expireTime || Date.now() > expireTime) {
      this.delete(key)
      return null
    }
    
    return this.cache.get(key)
  }
  
  // 删除缓存
  delete(key) {
    this.cache.delete(key)
    this.expireTime.delete(key)
  }
  
  // 清空缓存
  clear() {
    this.cache.clear()
    this.expireTime.clear()
  }
}

// 使用示例
const cacheManager = new CacheManager()

// API请求缓存装饰器
const withCache = (fn, cacheKey, expire) => {
  return async (...args) => {
    const key = `${cacheKey}_${JSON.stringify(args)}`
    let result = cacheManager.get(key)
    
    if (!result) {
      result = await fn(...args)
      cacheManager.set(key, result, expire)
    }
    
    return result
  }
}

// 使用缓存的API调用
export const getUserInfo = withCache(
  (userId) => request.get(`/api/user/${userId}`),
  'user_info',
  10 * 60 * 1000 // 10分钟缓存
)
```

## 3. 安全要求规范

### 3.1 数据加密规范
```javascript
// 前端敏感数据加密
import CryptoJS from 'crypto-js'

class SecurityUtils {
  // AES加密
  static encrypt(data, key = 'rongye-secret-key') {
    const encrypted = CryptoJS.AES.encrypt(JSON.stringify(data), key).toString()
    return encrypted
  }
  
  // AES解密
  static decrypt(encryptedData, key = 'rongye-secret-key') {
    try {
      const bytes = CryptoJS.AES.decrypt(encryptedData, key)
      const decrypted = bytes.toString(CryptoJS.enc.Utf8)
      return JSON.parse(decrypted)
    } catch (error) {
      console.error('解密失败:', error)
      return null
    }
  }
  
  // MD5哈希
  static md5(data) {
    return CryptoJS.MD5(data).toString()
  }
  
  // 生成随机字符串
  static generateRandomString(length = 16) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let result = ''
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }
}

// 使用示例
const sensitiveData = { password: '123456', mobile: '13800138000' }
const encrypted = SecurityUtils.encrypt(sensitiveData)
const decrypted = SecurityUtils.decrypt(encrypted)
```

### 3.2 输入验证规范
```javascript
// 输入验证工具类
class Validator {
  // 手机号验证
  static isMobile(mobile) {
    const reg = /^1[3-9]\d{9}$/
    return reg.test(mobile)
  }
  
  // 邮箱验证
  static isEmail(email) {
    const reg = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
    return reg.test(email)
  }
  
  // 身份证验证
  static isIdCard(idCard) {
    const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
    return reg.test(idCard)
  }
  
  // 密码强度验证
  static isStrongPassword(password) {
    // 至少8位，包含大小写字母、数字和特殊字符
    const reg = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/
    return reg.test(password)
  }
  
  // XSS防护
  static escapeHtml(str) {
    const div = document.createElement('div')
    div.textContent = str
    return div.innerHTML
  }
  
  // SQL注入防护（前端基础检查）
  static hasSqlInjection(str) {
    const sqlKeywords = ['select', 'insert', 'update', 'delete', 'drop', 'union', 'script']
    const lowerStr = str.toLowerCase()
    return sqlKeywords.some(keyword => lowerStr.includes(keyword))
  }
}
```

### 3.3 权限控制规范
```javascript
// 权限管理类
class PermissionManager {
  constructor() {
    this.userPermissions = []
    this.userLevel = ''
  }
  
  // 设置用户权限
  setUserPermissions(permissions, level) {
    this.userPermissions = permissions
    this.userLevel = level
  }
  
  // 检查权限
  hasPermission(permission) {
    // 智慧董事拥有所有权限
    if (this.userLevel === 'wisdom_director') {
      return true
    }
    
    return this.userPermissions.includes(permission)
  }
  
  // 检查页面访问权限
  canAccessPage(pagePath) {
    const pagePermissions = {
      '/pages/user/wallet': ['view_wallet'],
      '/pages/commission/team': ['view_team'],
      '/pages/commission/bonus': ['view_bonus'],
      '/pages/store/manage': ['manage_store']
    }
    
    const requiredPermission = pagePermissions[pagePath]
    if (!requiredPermission) return true // 无需权限的页面
    
    return requiredPermission.every(permission => this.hasPermission(permission))
  }
}

// 路由守卫
const permissionManager = new PermissionManager()

// 页面跳转前检查权限
const navigateToWithPermission = (url) => {
  if (!permissionManager.canAccessPage(url)) {
    uni.showToast({
      title: '权限不足',
      icon: 'none'
    })
    return
  }
  
  uni.navigateTo({ url })
}
```

## 4. 第三方集成规范

### 4.1 微信支付集成规范
```javascript
// 微信支付工具类
class WechatPay {
  // 发起支付
  static async pay(orderInfo) {
    try {
      // #ifdef MP-WEIXIN
      const payResult = await this.wxPay(orderInfo)
      // #endif
      
      // #ifdef H5
      const payResult = await this.h5Pay(orderInfo)
      // #endif
      
      // #ifdef APP-PLUS
      const payResult = await this.appPay(orderInfo)
      // #endif
      
      return payResult
    } catch (error) {
      console.error('支付失败:', error)
      throw error
    }
  }
  
  // 小程序支付
  static wxPay(orderInfo) {
    return new Promise((resolve, reject) => {
      wx.requestPayment({
        timeStamp: orderInfo.timeStamp,
        nonceStr: orderInfo.nonceStr,
        package: orderInfo.package,
        signType: orderInfo.signType,
        paySign: orderInfo.paySign,
        success: (res) => {
          resolve(res)
        },
        fail: (err) => {
          reject(err)
        }
      })
    })
  }
  
  // H5支付
  static h5Pay(orderInfo) {
    // 跳转到微信支付页面
    window.location.href = orderInfo.mweb_url
  }
  
  // APP支付
  static appPay(orderInfo) {
    return new Promise((resolve, reject) => {
      plus.payment.request('wxpay', orderInfo, (result) => {
        resolve(result)
      }, (error) => {
        reject(error)
      })
    })
  }
}
```

### 4.2 地图集成规范
```javascript
// 地图工具类
class MapUtils {
  // 获取当前位置
  static getCurrentLocation() {
    return new Promise((resolve, reject) => {
      uni.getLocation({
        type: 'gcj02',
        success: (res) => {
          resolve({
            latitude: res.latitude,
            longitude: res.longitude,
            address: res.address
          })
        },
        fail: (err) => {
          reject(err)
        }
      })
    })
  }
  
  // 选择位置
  static chooseLocation() {
    return new Promise((resolve, reject) => {
      uni.chooseLocation({
        success: (res) => {
          resolve({
            name: res.name,
            address: res.address,
            latitude: res.latitude,
            longitude: res.longitude
          })
        },
        fail: (err) => {
          reject(err)
        }
      })
    })
  }
  
  // 计算距离
  static calculateDistance(lat1, lng1, lat2, lng2) {
    const R = 6371 // 地球半径（公里）
    const dLat = this.toRadians(lat2 - lat1)
    const dLng = this.toRadians(lng2 - lng1)
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
              Math.sin(dLng / 2) * Math.sin(dLng / 2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
    return R * c
  }
  
  static toRadians(degrees) {
    return degrees * (Math.PI / 180)
  }
}
```

## 5. 错误监控和日志规范

### 5.1 错误监控
```javascript
// 全局错误处理
Vue.config.errorHandler = (err, vm, info) => {
  console.error('Vue错误:', err, info)
  
  // 上报错误信息
  reportError({
    type: 'vue_error',
    message: err.message,
    stack: err.stack,
    info: info,
    url: window.location.href,
    userAgent: navigator.userAgent,
    timestamp: Date.now()
  })
}

// 未捕获的Promise错误
window.addEventListener('unhandledrejection', (event) => {
  console.error('未捕获的Promise错误:', event.reason)
  
  reportError({
    type: 'promise_error',
    message: event.reason.message || event.reason,
    stack: event.reason.stack,
    timestamp: Date.now()
  })
})

// 错误上报函数
const reportError = (errorInfo) => {
  // 开发环境不上报
  if (process.env.NODE_ENV === 'development') {
    return
  }
  
  // 上报到错误监控服务
  uni.request({
    url: '/api/error/report',
    method: 'POST',
    data: errorInfo,
    fail: (err) => {
      console.error('错误上报失败:', err)
    }
  })
}
```

### 5.2 日志记录规范
```javascript
// 日志工具类
class Logger {
  static levels = {
    DEBUG: 0,
    INFO: 1,
    WARN: 2,
    ERROR: 3
  }
  
  static currentLevel = process.env.NODE_ENV === 'production' ? this.levels.INFO : this.levels.DEBUG
  
  static log(level, message, data = null) {
    if (level < this.currentLevel) return
    
    const timestamp = new Date().toISOString()
    const levelName = Object.keys(this.levels)[level]
    
    console.log(`[${timestamp}] [${levelName}] ${message}`, data || '')
    
    // 生产环境上报重要日志
    if (process.env.NODE_ENV === 'production' && level >= this.levels.WARN) {
      this.reportLog(levelName, message, data)
    }
  }
  
  static debug(message, data) {
    this.log(this.levels.DEBUG, message, data)
  }
  
  static info(message, data) {
    this.log(this.levels.INFO, message, data)
  }
  
  static warn(message, data) {
    this.log(this.levels.WARN, message, data)
  }
  
  static error(message, data) {
    this.log(this.levels.ERROR, message, data)
  }
  
  static reportLog(level, message, data) {
    uni.request({
      url: '/api/log/report',
      method: 'POST',
      data: {
        level,
        message,
        data,
        timestamp: Date.now(),
        platform: uni.getSystemInfoSync().platform
      }
    })
  }
}

// 使用示例
Logger.info('用户登录', { userId: 123, loginTime: Date.now() })
Logger.error('支付失败', { orderId: 456, error: 'network_error' })
```

## 6. 检查清单

### 多平台兼容性检查
- [ ] 使用uni-app标准API
- [ ] 正确使用条件编译
- [ ] 样式使用rpx单位
- [ ] 测试各平台功能正常

### 性能优化检查
- [ ] 图片进行了压缩和懒加载
- [ ] 列表使用了分页加载
- [ ] 实现了数据缓存机制
- [ ] 避免了内存泄漏

### 安全检查
- [ ] 敏感数据进行了加密
- [ ] 实现了输入验证
- [ ] 添加了权限控制
- [ ] 防范了常见安全漏洞

### 第三方集成检查
- [ ] 支付功能在各平台正常
- [ ] 地图功能正确集成
- [ ] 第三方SDK版本兼容

### 监控和日志检查
- [ ] 添加了错误监控
- [ ] 实现了日志记录
- [ ] 错误信息能正确上报

---
*本规范针对融业港项目的特定需求制定，请严格遵守以确保项目质量和用户体验。*
