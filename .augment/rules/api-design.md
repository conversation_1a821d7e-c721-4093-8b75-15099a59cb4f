---
type: "manual"
---

# 融业港项目API设计规范

## 适用范围
本规范适用于融业港项目的所有API接口设计，包括前后端数据交互、第三方接口集成等。

## 1. RESTful API设计原则

### 1.1 URL设计规范
```
✅ 正确示例：
GET    /api/user/info              # 获取用户信息
POST   /api/user/login             # 用户登录
PUT    /api/user/profile           # 更新用户资料
DELETE /api/user/avatar            # 删除用户头像

GET    /api/orders                 # 获取订单列表
GET    /api/orders/123             # 获取指定订单
POST   /api/orders                 # 创建订单
PUT    /api/orders/123             # 更新订单
DELETE /api/orders/123             # 删除订单

❌ 错误示例：
GET    /api/getUserInfo
POST   /api/user_login
GET    /api/getOrderList
POST   /api/createOrder
```

### 1.2 HTTP状态码使用规范
```
200 OK                  # 请求成功
201 Created            # 创建成功
204 No Content         # 删除成功
400 Bad Request        # 请求参数错误
401 Unauthorized       # 未认证
403 Forbidden          # 权限不足
404 Not Found          # 资源不存在
422 Unprocessable Entity # 参数验证失败
500 Internal Server Error # 服务器内部错误
```

## 2. 请求和响应格式

### 2.1 统一响应格式
```json
{
  "code": 1,
  "msg": "success",
  "time": "1640995200",
  "data": {
    // 具体数据
  }
}
```

### 2.2 成功响应示例
```json
// 获取用户信息
{
  "code": 1,
  "msg": "获取成功",
  "time": "1640995200",
  "data": {
    "id": 123,
    "username": "user123",
    "nickname": "张三",
    "avatar": "https://example.com/avatar.jpg",
    "mobile": "13800138000",
    "level": "entrepreneur",
    "balance": 1000.50,
    "points": 500
  }
}

// 获取列表数据
{
  "code": 1,
  "msg": "获取成功",
  "time": "1640995200",
  "data": {
    "list": [
      {
        "id": 1,
        "title": "订单标题",
        "amount": 799.00,
        "status": "paid"
      }
    ],
    "total": 100,
    "page": 1,
    "limit": 20
  }
}
```

### 2.3 错误响应示例
```json
// 参数验证错误
{
  "code": 0,
  "msg": "参数验证失败",
  "time": "1640995200",
  "data": {
    "errors": {
      "mobile": ["手机号格式不正确"],
      "password": ["密码长度不能少于6位"]
    }
  }
}

// 业务逻辑错误
{
  "code": 0,
  "msg": "余额不足",
  "time": "1640995200",
  "data": {
    "current_balance": 100.00,
    "required_amount": 799.00
  }
}

// 系统错误
{
  "code": 0,
  "msg": "系统繁忙，请稍后重试",
  "time": "1640995200",
  "data": null
}
```

## 3. 认证和授权

### 3.1 Token认证机制
```javascript
// 前端请求头设置
const request = {
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  }
}
```

```php
<?php
// 后端Token验证
class AuthMiddleware
{
    public function handle($request, Closure $next)
    {
        $token = $request->header('Authorization');
        if (!$token || !str_starts_with($token, 'Bearer ')) {
            return json(['code' => 0, 'msg' => '未提供认证令牌'], 401);
        }
        
        $token = substr($token, 7); // 移除 "Bearer " 前缀
        $user = $this->validateToken($token);
        
        if (!$user) {
            return json(['code' => 0, 'msg' => '认证令牌无效'], 401);
        }
        
        $request->user = $user;
        return $next($request);
    }
}
```

### 3.2 权限控制
```php
<?php
// 权限检查示例
class PermissionMiddleware
{
    public function handle($request, Closure $next, $permission)
    {
        $user = $request->user;
        
        if (!$this->hasPermission($user, $permission)) {
            return json(['code' => 0, 'msg' => '权限不足'], 403);
        }
        
        return $next($request);
    }
    
    private function hasPermission($user, $permission)
    {
        // 根据用户等级检查权限
        $levelPermissions = [
            'entrepreneur' => ['view_own_orders', 'create_order'],
            'advisor' => ['view_own_orders', 'create_order', 'view_team_basic'],
            'manager' => ['view_own_orders', 'create_order', 'view_team_basic', 'view_team_detail'],
            'director' => ['*'], // 享业董事拥有所有权限
        ];
        
        $userPermissions = $levelPermissions[$user['level']] ?? [];
        
        return in_array('*', $userPermissions) || in_array($permission, $userPermissions);
    }
}
```

## 4. 参数验证规范

### 4.1 前端参数验证
```javascript
// 表单验证规则
const validateRules = {
  mobile: [
    { required: true, message: '请输入手机号' },
    { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确' }
  ],
  password: [
    { required: true, message: '请输入密码' },
    { min: 6, message: '密码长度不能少于6位' }
  ],
  amount: [
    { required: true, message: '请输入金额' },
    { type: 'number', min: 0.01, message: '金额必须大于0' }
  ]
}

// 参数验证函数
const validateParams = (data, rules) => {
  const errors = {}
  
  for (const field in rules) {
    const fieldRules = rules[field]
    const value = data[field]
    
    for (const rule of fieldRules) {
      if (rule.required && (!value || value === '')) {
        errors[field] = errors[field] || []
        errors[field].push(rule.message)
        break
      }
      
      if (rule.pattern && value && !rule.pattern.test(value)) {
        errors[field] = errors[field] || []
        errors[field].push(rule.message)
      }
      
      if (rule.min && value && value.length < rule.min) {
        errors[field] = errors[field] || []
        errors[field].push(rule.message)
      }
    }
  }
  
  return Object.keys(errors).length > 0 ? errors : null
}
```

### 4.2 后端参数验证
```php
<?php
/**
 * API参数验证基类
 */
class ApiValidator
{
    /**
     * 用户登录参数验证
     */
    public static function validateLogin($params)
    {
        $rules = [
            'mobile' => 'require|mobile',
            'password' => 'require|length:6,20',
            'captcha' => 'require|length:4'
        ];
        
        $messages = [
            'mobile.require' => '请输入手机号',
            'mobile.mobile' => '手机号格式不正确',
            'password.require' => '请输入密码',
            'password.length' => '密码长度为6-20位',
            'captcha.require' => '请输入验证码',
            'captcha.length' => '验证码长度不正确'
        ];
        
        $validate = new Validate($rules, $messages);
        
        if (!$validate->check($params)) {
            throw new ValidateException($validate->getError());
        }
    }
    
    /**
     * 订单创建参数验证
     */
    public static function validateCreateOrder($params)
    {
        $rules = [
            'product_id' => 'require|integer|gt:0',
            'quantity' => 'require|integer|between:1,999',
            'address_id' => 'require|integer|gt:0'
        ];
        
        $validate = new Validate($rules);
        
        if (!$validate->check($params)) {
            throw new ValidateException($validate->getError());
        }
        
        // 业务逻辑验证
        self::validateOrderBusiness($params);
    }
    
    private static function validateOrderBusiness($params)
    {
        // 检查商品是否存在且可购买
        $product = Db::name('product')->where('id', $params['product_id'])->find();
        if (!$product || $product['status'] !== 'active') {
            throw new BusinessException('商品不存在或已下架');
        }
        
        // 检查库存
        if ($product['stock'] < $params['quantity']) {
            throw new BusinessException('库存不足');
        }
    }
}
```

## 5. 分页和排序

### 5.1 分页参数规范
```javascript
// 前端分页请求
const getOrderList = (params = {}) => {
  const defaultParams = {
    page: 1,
    limit: 20,
    sort: 'createtime',
    order: 'desc'
  }
  
  return request.get('/api/orders', {
    params: { ...defaultParams, ...params }
  })
}
```

```php
<?php
// 后端分页处理
class OrderController extends Api
{
    public function getOrderList()
    {
        $page = $this->request->param('page', 1, 'intval');
        $limit = $this->request->param('limit', 20, 'intval');
        $sort = $this->request->param('sort', 'createtime');
        $order = $this->request->param('order', 'desc');
        
        // 限制分页参数
        $page = max(1, $page);
        $limit = min(100, max(1, $limit)); // 限制每页最多100条
        
        // 允许的排序字段
        $allowSortFields = ['createtime', 'amount', 'status'];
        $sort = in_array($sort, $allowSortFields) ? $sort : 'createtime';
        $order = in_array($order, ['asc', 'desc']) ? $order : 'desc';
        
        $userId = $this->auth->getUserId();
        
        $list = Db::name('order')
                  ->where('user_id', $userId)
                  ->order($sort . ' ' . $order)
                  ->page($page, $limit)
                  ->select();
        
        $total = Db::name('order')->where('user_id', $userId)->count();
        
        $this->success('获取成功', [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit
        ]);
    }
}
```

## 6. 文件上传规范

### 6.1 文件上传接口
```php
<?php
class UploadController extends Api
{
    /**
     * 上传文件
     */
    public function uploadFile()
    {
        $file = $this->request->file('file');
        
        if (!$file) {
            $this->error('请选择要上传的文件');
        }
        
        // 验证文件
        $validate = [
            'size' => 5 * 1024 * 1024, // 5MB
            'ext' => 'jpg,jpeg,png,gif'
        ];
        
        $info = $file->validate($validate)->move('./uploads');
        
        if (!$info) {
            $this->error($file->getError());
        }
        
        $filePath = '/uploads/' . $info->getSaveName();
        $fileUrl = $this->request->domain() . $filePath;
        
        $this->success('上传成功', [
            'path' => $filePath,
            'url' => $fileUrl,
            'size' => $info->getSize(),
            'ext' => $info->getExtension()
        ]);
    }
}
```

### 6.2 前端文件上传
```javascript
// uni-app文件上传
const uploadFile = (filePath) => {
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: '/api/upload/file',
      filePath: filePath,
      name: 'file',
      header: {
        'Authorization': 'Bearer ' + getToken()
      },
      success: (res) => {
        const data = JSON.parse(res.data)
        if (data.code === 1) {
          resolve(data.data)
        } else {
          reject(new Error(data.msg))
        }
      },
      fail: (error) => {
        reject(error)
      }
    })
  })
}
```

## 7. 接口版本控制

### 7.1 URL版本控制
```
/api/v1/user/info    # 版本1
/api/v2/user/info    # 版本2
```

### 7.2 版本兼容性处理
```php
<?php
class ApiVersionController
{
    public function getUserInfo()
    {
        $version = $this->getApiVersion();
        
        switch ($version) {
            case 'v1':
                return $this->getUserInfoV1();
            case 'v2':
                return $this->getUserInfoV2();
            default:
                return $this->getUserInfoV2(); // 默认最新版本
        }
    }
    
    private function getUserInfoV1()
    {
        // 旧版本接口逻辑
        $userInfo = $this->getUserBasicInfo();
        return $this->success('获取成功', $userInfo);
    }
    
    private function getUserInfoV2()
    {
        // 新版本接口逻辑，包含更多字段
        $userInfo = $this->getUserDetailInfo();
        return $this->success('获取成功', $userInfo);
    }
}
```

## 8. 接口文档规范

### 8.1 接口文档模板
```markdown
## 获取用户信息

### 请求信息
- **URL**: `/api/user/info`
- **Method**: `GET`
- **需要认证**: 是

### 请求参数
无

### 响应示例
```json
{
  "code": 1,
  "msg": "获取成功",
  "time": "1640995200",
  "data": {
    "id": 123,
    "username": "user123",
    "nickname": "张三",
    "level": "entrepreneur",
    "balance": 1000.50
  }
}
```

### 响应字段说明
| 字段 | 类型 | 说明 |
|------|------|------|
| id | integer | 用户ID |
| username | string | 用户名 |
| nickname | string | 昵称 |
| level | string | 用户等级 |
| balance | float | 钱包余额 |

### 错误码说明
| 错误码 | 说明 |
|--------|------|
| 401 | 未认证 |
| 403 | 权限不足 |
```

## 9. 接口测试规范

### 9.1 单元测试示例
```php
<?php
class UserApiTest extends TestCase
{
    public function testGetUserInfo()
    {
        // 模拟登录用户
        $user = $this->createTestUser();
        $token = $this->getAuthToken($user);
        
        // 发送请求
        $response = $this->get('/api/user/info', [
            'Authorization' => 'Bearer ' . $token
        ]);
        
        // 断言响应
        $response->assertStatus(200);
        $response->assertJson([
            'code' => 1,
            'msg' => '获取成功'
        ]);
        
        $data = $response->json('data');
        $this->assertEquals($user['id'], $data['id']);
        $this->assertEquals($user['username'], $data['username']);
    }
}
```

## 10. 检查清单

### API设计检查
- [ ] URL设计符合RESTful规范
- [ ] 使用了正确的HTTP状态码
- [ ] 响应格式统一
- [ ] 添加了参数验证
- [ ] 实现了认证和授权
- [ ] 处理了错误情况
- [ ] 编写了接口文档
- [ ] 添加了单元测试

### 安全检查
- [ ] 敏感信息不在URL中传递
- [ ] 实现了防重放攻击
- [ ] 添加了请求频率限制
- [ ] 验证了用户权限
- [ ] 过滤了危险字符

---
*API接口是前后端协作的基础，任何变更都需要及时更新文档并通知相关人员。*
