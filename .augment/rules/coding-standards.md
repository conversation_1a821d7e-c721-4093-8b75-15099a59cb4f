---
type: "manual"
---

# 融业港项目代码规范

## 适用范围
本规范适用于融业港项目的前端（uni-app + Vue3）和后端（FastAdmin + ThinkPHP5）开发。

## 1. 通用规范

### 1.1 文件编码
- 所有文件必须使用 UTF-8 编码
- 文件末尾必须有一个空行
- 使用 LF（\n）作为行结束符

### 1.2 缩进和空格
- 使用 2 个空格进行缩进，禁止使用 Tab
- 操作符前后必须有空格
- 逗号后必须有空格，逗号前不能有空格

### 1.3 命名规范
- 文件名使用小写字母，多个单词用连字符分隔：`user-profile.vue`
- 目录名使用小写字母，多个单词用连字符分隔：`order-management`
- 常量使用大写字母和下划线：`MAX_RETRY_COUNT`

## 2. 前端规范（Vue3 + uni-app）

### 2.1 文件命名
```
✅ 正确示例：
pages/user/user-profile.vue
components/goods/goods-card.vue
sheep/api/order-api.js

❌ 错误示例：
pages/user/UserProfile.vue
components/goods/goodsCard.vue
sheep/api/orderAPI.js
```

### 2.2 Vue组件规范
```vue
<!-- ✅ 正确示例 -->
<template>
  <view class="user-profile">
    <view class="user-info">
      <text class="user-name">{{ userInfo.name }}</text>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { userApi } from '@/sheep/api/user'

// 响应式数据
const userInfo = ref({})
const loading = ref(false)

// 方法
const getUserInfo = async () => {
  try {
    loading.value = true
    const { data } = await userApi.getUserInfo()
    userInfo.value = data
  } catch (error) {
    console.error('获取用户信息失败:', error)
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  getUserInfo()
})
</script>

<style lang="scss" scoped>
.user-profile {
  padding: 20rpx;
  
  .user-info {
    background: #fff;
    border-radius: 10rpx;
    
    .user-name {
      font-size: 32rpx;
      color: #333;
    }
  }
}
</style>
```

### 2.3 API调用规范
```javascript
// ✅ 正确示例 - sheep/api/user.js
import request from '@/sheep/request'

export const userApi = {
  // 获取用户信息
  getUserInfo: () => request.get('/api/user/info'),
  
  // 更新用户信息
  updateUserInfo: (data) => request.post('/api/user/update', data),
  
  // 获取用户钱包信息
  getWalletInfo: () => request.get('/api/user/wallet')
}
```

### 2.4 状态管理规范
```javascript
// ✅ 正确示例 - sheep/store/user.js
import { defineStore } from 'pinia'
import { userApi } from '@/sheep/api/user'

export const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: {},
    isLogin: false,
    token: ''
  }),
  
  getters: {
    // 是否为VIP用户
    isVip: (state) => state.userInfo.user_group === 'vip'
  },
  
  actions: {
    // 登录
    async login(loginData) {
      try {
        const { data } = await userApi.login(loginData)
        this.token = data.token
        this.userInfo = data.userInfo
        this.isLogin = true
        return data
      } catch (error) {
        throw error
      }
    },
    
    // 退出登录
    logout() {
      this.token = ''
      this.userInfo = {}
      this.isLogin = false
    }
  },
  
  persist: {
    enabled: true,
    strategies: [
      {
        key: 'user-store',
        storage: 'localStorage'
      }
    ]
  }
})
```

## 3. 后端规范（PHP + ThinkPHP5）

### 3.1 文件命名
```
✅ 正确示例：
application/api/controller/User.php
application/common/model/UserWallet.php
application/admin/controller/OrderManagement.php

❌ 错误示例：
application/api/controller/user.php
application/common/model/user_wallet.php
```

### 3.2 控制器规范
```php
<?php
// ✅ 正确示例
namespace app\api\controller;

use app\common\controller\Api;
use app\common\model\User as UserModel;
use think\Exception;

/**
 * 用户控制器
 */
class User extends Api
{
    // 无需登录的方法
    protected $noNeedLogin = ['login', 'register'];
    
    /**
     * 获取用户信息
     * @return \think\Response
     */
    public function getUserInfo()
    {
        try {
            $userId = $this->auth->getUserId();
            $userModel = new UserModel();
            $userInfo = $userModel->getUserDetail($userId);
            
            if (!$userInfo) {
                $this->error('用户不存在');
            }
            
            $this->success('获取成功', $userInfo);
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
    }
    
    /**
     * 更新用户信息
     * @return \think\Response
     */
    public function updateUserInfo()
    {
        $params = $this->request->post();
        
        // 参数验证
        $validate = $this->validate($params, [
            'nickname' => 'require|max:50',
            'avatar' => 'url'
        ]);
        
        if ($validate !== true) {
            $this->error($validate);
        }
        
        try {
            $userId = $this->auth->getUserId();
            $userModel = new UserModel();
            $result = $userModel->updateUserInfo($userId, $params);
            
            if ($result) {
                $this->success('更新成功');
            } else {
                $this->error('更新失败');
            }
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
    }
}
```

### 3.3 模型规范
```php
<?php
// ✅ 正确示例
namespace app\common\model;

use think\Model;
use think\Exception;

/**
 * 用户模型
 */
class User extends Model
{
    // 表名
    protected $table = 'sys_user';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    
    // 字段类型转换
    protected $type = [
        'createtime' => 'timestamp',
        'updatetime' => 'timestamp'
    ];
    
    /**
     * 获取用户详细信息
     * @param int $userId 用户ID
     * @return array|null
     */
    public function getUserDetail($userId)
    {
        try {
            $user = $this->where('id', $userId)
                        ->field('id,username,nickname,avatar,mobile,user_group,status')
                        ->find();
            
            if (!$user) {
                return null;
            }
            
            return $user->toArray();
        } catch (Exception $e) {
            throw new Exception('获取用户信息失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 更新用户信息
     * @param int $userId 用户ID
     * @param array $data 更新数据
     * @return bool
     */
    public function updateUserInfo($userId, $data)
    {
        try {
            // 过滤不允许更新的字段
            $allowFields = ['nickname', 'avatar'];
            $updateData = array_intersect_key($data, array_flip($allowFields));
            
            if (empty($updateData)) {
                throw new Exception('没有可更新的数据');
            }
            
            return $this->where('id', $userId)->update($updateData);
        } catch (Exception $e) {
            throw new Exception('更新用户信息失败: ' . $e->getMessage());
        }
    }
}
```

## 4. 注释规范

### 4.1 前端注释
```javascript
/**
 * 计算分润金额
 * @param {number} orderAmount - 订单金额
 * @param {string} userLevel - 用户等级
 * @returns {number} 分润金额
 */
const calculateBonus = (orderAmount, userLevel) => {
  // 根据用户等级获取分润比例
  const bonusRate = getBonusRate(userLevel)
  
  // 计算分润金额（保留两位小数）
  return Math.round(orderAmount * bonusRate * 100) / 100
}
```

### 4.2 后端注释
```php
/**
 * 处理订单分润
 * @param int $orderId 订单ID
 * @param float $orderAmount 订单金额
 * @return bool 处理结果
 * @throws Exception
 */
public function processOrderBonus($orderId, $orderAmount)
{
    // 获取订单信息
    $order = $this->getOrderInfo($orderId);
    
    // 计算分润金额
    $bonusAmount = $this->calculateBonusAmount($orderAmount, $order['user_level']);
    
    // 发放分润
    return $this->distributeBonusAmount($order['user_id'], $bonusAmount);
}
```

## 5. 错误处理规范

### 5.1 前端错误处理
```javascript
// ✅ 正确示例
const handleApiError = (error) => {
  console.error('API请求失败:', error)
  
  if (error.code === 401) {
    // 未登录，跳转到登录页
    uni.navigateTo({ url: '/pages/user/login' })
  } else if (error.code === 403) {
    // 权限不足
    uni.showToast({ title: '权限不足', icon: 'none' })
  } else {
    // 其他错误
    uni.showToast({ title: error.message || '请求失败', icon: 'none' })
  }
}
```

### 5.2 后端错误处理
```php
// ✅ 正确示例
try {
    // 业务逻辑
    $result = $this->processBusinessLogic();
    $this->success('操作成功', $result);
} catch (ValidateException $e) {
    // 参数验证错误
    $this->error($e->getMessage(), null, 400);
} catch (AuthException $e) {
    // 认证错误
    $this->error($e->getMessage(), null, 401);
} catch (Exception $e) {
    // 其他错误
    \think\Log::error('业务处理失败: ' . $e->getMessage());
    $this->error('系统错误，请稍后重试', null, 500);
}
```

## 6. 检查清单

### 代码提交前检查
- [ ] 代码格式符合规范（使用Prettier格式化）
- [ ] 变量和函数命名符合规范
- [ ] 添加了必要的注释
- [ ] 错误处理完整
- [ ] 没有console.log等调试代码
- [ ] 没有硬编码的配置信息
- [ ] API接口遵循RESTful规范

### 违规处理
1. **轻微违规**：代码审查时指出，要求修改
2. **严重违规**：拒绝合并，要求重新提交
3. **重复违规**：团队内部讨论，加强培训

## 7. 相关工具

### 前端工具
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **Vue DevTools**: Vue调试工具

### 后端工具
- **PHP CodeSniffer**: PHP代码规范检查
- **PHPStan**: PHP静态分析工具
- **Xdebug**: PHP调试工具

---
*本规范会根据项目发展持续更新，请定期查看最新版本。*
