---
type: "always_apply"
---

# 融业港项目开发流程规范

## 适用范围
本规范适用于融业港项目的所有开发活动，包括功能开发、Bug修复、代码审查等。

## 1. Git分支管理策略

### 1.1 分支类型
```
master/main     - 生产环境分支（受保护）
develop         - 开发环境分支
feature/*       - 功能开发分支
hotfix/*        - 紧急修复分支
release/*       - 发布准备分支
```

### 1.2 分支命名规范
```
✅ 正确示例：
feature/user-wallet-system
feature/bonus-calculation-logic
hotfix/order-payment-bug
release/v1.2.0

❌ 错误示例：
feature/wallet
fix/bug
new-feature
```

### 1.3 分支工作流程
```mermaid
graph LR
    A[develop] --> B[feature/xxx]
    B --> C[开发完成]
    C --> D[提交PR]
    D --> E[代码审查]
    E --> F[合并到develop]
    F --> G[测试环境验证]
    G --> H[合并到master]
```

## 2. 提交信息规范

### 2.1 提交信息格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

### 2.2 类型说明
- **feat**: 新功能
- **fix**: Bug修复
- **docs**: 文档更新
- **style**: 代码格式调整
- **refactor**: 代码重构
- **test**: 测试相关
- **chore**: 构建过程或辅助工具的变动

### 2.3 提交信息示例
```
✅ 正确示例：
feat(user): 添加用户钱包余额查询功能

- 新增钱包余额API接口
- 添加前端钱包余额显示组件
- 完善钱包相关的错误处理

Closes #123

fix(order): 修复订单支付回调处理异常

- 修复支付回调时订单状态更新失败的问题
- 添加支付回调的重试机制
- 完善支付异常的日志记录

Fixes #456

❌ 错误示例：
update code
fix bug
add new feature
```

## 3. 代码审查流程

### 3.1 Pull Request规范
```markdown
## 变更说明
简要描述本次变更的内容和目的

## 变更类型
- [ ] 新功能
- [ ] Bug修复
- [ ] 代码重构
- [ ] 文档更新
- [ ] 其他

## 测试情况
- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] 手动测试完成
- [ ] 性能测试通过（如需要）

## 影响范围
描述本次变更可能影响的功能模块

## 截图/录屏
如果是UI相关变更，请提供截图或录屏

## 检查清单
- [ ] 代码符合规范
- [ ] 添加了必要的注释
- [ ] 更新了相关文档
- [ ] 没有硬编码配置
- [ ] 错误处理完整
```

### 3.2 代码审查要点
1. **功能正确性**: 代码是否实现了预期功能
2. **代码质量**: 是否符合编码规范
3. **性能考虑**: 是否存在性能问题
4. **安全性**: 是否存在安全隐患
5. **可维护性**: 代码是否易于理解和维护

### 3.3 审查流程
```
1. 开发者提交PR
2. 自动化检查（CI/CD）
3. 指定审查者进行代码审查
4. 审查者提出修改意见
5. 开发者修改代码
6. 审查者确认修改
7. 合并代码
```

## 4. 测试要求

### 4.1 测试分类
- **单元测试**: 测试单个函数或方法
- **集成测试**: 测试模块间的交互
- **端到端测试**: 测试完整的用户流程
- **性能测试**: 测试系统性能指标

### 4.2 测试覆盖率要求
- 核心业务逻辑: ≥80%
- 工具函数: ≥90%
- API接口: ≥70%

### 4.3 测试文件命名
```
✅ 前端测试文件：
sheep/api/__tests__/user.test.js
sheep/helper/__tests__/utils.test.js

✅ 后端测试文件：
test/user_wallet_test.php
test/bonus_calculation_test.php
```

### 4.4 测试示例
```javascript
// 前端测试示例
import { calculateBonus } from '@/sheep/helper/bonus'

describe('分润计算', () => {
  test('创业销售商分润计算', () => {
    const result = calculateBonus(799, 'entrepreneur')
    expect(result).toBe(159.8)
  })
  
  test('拓业顾问分润计算', () => {
    const result = calculateBonus(799, 'advisor')
    expect(result).toBe(119.85)
  })
})
```

```php
<?php
// 后端测试示例
class BonusCalculationTest extends PHPUnit\Framework\TestCase
{
    public function testEntrepreneurBonus()
    {
        $calculator = new BonusCalculator();
        $result = $calculator->calculate(799, 'entrepreneur');
        $this->assertEquals(159.8, $result);
    }
    
    public function testAdvisorBonus()
    {
        $calculator = new BonusCalculator();
        $result = $calculator->calculate(799, 'advisor');
        $this->assertEquals(119.85, $result);
    }
}
```

## 5. 部署流程

### 5.1 环境说明
- **开发环境**: 本地开发使用
- **测试环境**: 功能测试和集成测试
- **预生产环境**: 生产前最后验证
- **生产环境**: 正式对外服务

### 5.2 部署步骤
```
1. 代码合并到对应分支
2. 触发自动化构建
3. 运行自动化测试
4. 部署到目标环境
5. 执行部署后检查
6. 通知相关人员
```

### 5.3 回滚策略
- 发现问题立即回滚到上一个稳定版本
- 记录回滚原因和解决方案
- 修复问题后重新部署

## 6. 开发环境配置

### 6.1 前端开发环境
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev:h5

# 构建生产版本
npm run build:h5
```

### 6.2 后端开发环境
```bash
# 安装依赖
composer install

# 配置数据库
cp .env.example .env
# 编辑.env文件配置数据库连接

# 运行数据库迁移
php think migrate:run

# 启动开发服务器
php think run
```

### 6.3 数据库配置
```php
// 开发环境数据库配置
return [
    'hostname' => '127.0.0.1',
    'database' => 'rongye_dev',
    'username' => 'root',
    'password' => 'root',
    'hostport' => '3306',
    'prefix'   => 'sys_',
];
```

## 7. 问题处理流程

### 7.1 Bug报告模板
```markdown
## Bug描述
简要描述遇到的问题

## 复现步骤
1. 打开xxx页面
2. 点击xxx按钮
3. 输入xxx信息
4. 看到错误信息

## 预期结果
描述期望看到的正确结果

## 实际结果
描述实际看到的错误结果

## 环境信息
- 操作系统: 
- 浏览器: 
- 版本号: 
- 设备型号: 

## 截图/录屏
如果可能，请提供截图或录屏
```

### 7.2 Bug处理优先级
- **P0 - 紧急**: 系统崩溃、数据丢失、安全漏洞
- **P1 - 高**: 核心功能异常、影响用户使用
- **P2 - 中**: 一般功能异常、体验问题
- **P3 - 低**: 优化建议、非关键问题

### 7.3 Bug处理流程
```
1. Bug报告提交
2. 开发团队评估优先级
3. 分配给相应开发者
4. 开发者修复并测试
5. 提交修复代码
6. 测试团队验证
7. 部署到生产环境
8. 关闭Bug报告
```

## 8. 团队协作规范

### 8.1 沟通渠道
- **日常沟通**: 微信群、钉钉
- **技术讨论**: 技术文档、代码注释
- **问题跟踪**: GitHub Issues、禅道

### 8.2 会议规范
- **每日站会**: 15分钟，同步进度和问题
- **周会**: 1小时，回顾本周工作和计划下周
- **技术分享**: 双周一次，分享技术心得

### 8.3 文档管理
- 技术文档统一存放在 `/doc` 目录
- API文档使用统一格式
- 重要变更必须更新相关文档

## 9. 质量保证

### 9.1 代码质量检查
- 使用ESLint检查JavaScript代码
- 使用PHP CodeSniffer检查PHP代码
- 代码覆盖率不低于70%

### 9.2 性能监控
- 页面加载时间 < 3秒
- API响应时间 < 500ms
- 数据库查询优化

### 9.3 安全检查
- 输入参数验证
- SQL注入防护
- XSS攻击防护
- 敏感信息加密

## 10. 检查清单

### 开发完成检查
- [ ] 功能实现完整
- [ ] 代码符合规范
- [ ] 添加了单元测试
- [ ] 更新了相关文档
- [ ] 通过了代码审查

### 发布前检查
- [ ] 所有测试通过
- [ ] 性能指标达标
- [ ] 安全检查通过
- [ ] 备份数据库
- [ ] 准备回滚方案

---
*本规范会根据项目发展和团队反馈持续优化，请定期查看最新版本。*
