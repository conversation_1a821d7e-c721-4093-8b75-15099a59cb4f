---
type: "manual"
---

# 融业港后端开发特定规范

## 适用范围
本规范适用于融业港后端项目（FastAdmin + ThinkPHP5），包括控制器开发、模型设计、中间件使用等后端特定开发规范。

## 1. 项目结构规范

### 1.1 目录结构
```
rongye-backend/
├── application/
│   ├── admin/             # 后台管理模块
│   │   ├── controller/    # 控制器
│   │   ├── model/         # 模型
│   │   └── validate/      # 验证器
│   ├── api/               # API接口模块
│   │   ├── controller/    # API控制器
│   │   └── library/       # API库文件
│   └── common/            # 公共模块
│       ├── controller/    # 基础控制器
│       ├── model/         # 公共模型
│       └── library/       # 公共库文件
├── addons/                # 插件目录
├── public/                # 公共访问目录
└── vendor/                # 第三方依赖
```

### 1.2 文件命名规范
```
✅ 正确示例：
application/api/controller/User.php
application/common/model/UserWallet.php
application/admin/controller/OrderManagement.php

❌ 错误示例：
application/api/controller/user.php
application/common/model/user_wallet.php
application/admin/controller/order_management.php
```

## 2. 控制器开发规范

### 2.1 API控制器基础结构
```php
<?php
namespace app\api\controller;

use app\common\controller\Api;
use app\common\model\User as UserModel;
use app\common\library\Auth;
use think\Exception;
use think\Validate;

/**
 * 用户API控制器
 */
class User extends Api
{
    // 无需登录的方法
    protected $noNeedLogin = ['login', 'register', 'captcha'];
    
    // 无需权限验证的方法
    protected $noNeedRight = ['info', 'profile'];
    
    /**
     * 用户登录
     * @return \think\Response
     */
    public function login()
    {
        $mobile = $this->request->post('mobile');
        $password = $this->request->post('password');
        $captcha = $this->request->post('captcha');
        
        // 参数验证
        $validate = new Validate([
            'mobile' => 'require|mobile',
            'password' => 'require|length:6,20',
            'captcha' => 'require|length:4'
        ]);
        
        $data = compact('mobile', 'password', 'captcha');
        if (!$validate->check($data)) {
            $this->error($validate->getError());
        }
        
        try {
            // 验证码检查
            if (!captcha_check($captcha)) {
                $this->error('验证码错误');
            }
            
            // 用户登录验证
            $userModel = new UserModel();
            $user = $userModel->login($mobile, $password);
            
            if (!$user) {
                $this->error('用户名或密码错误');
            }
            
            // 生成token
            $token = Auth::instance()->createToken($user['id']);
            
            // 更新登录信息
            $userModel->updateLoginInfo($user['id'], $this->request->ip());
            
            $this->success('登录成功', [
                'token' => $token,
                'userInfo' => $user
            ]);
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
    }
    
    /**
     * 获取用户信息
     * @return \think\Response
     */
    public function getUserInfo()
    {
        try {
            $userId = $this->auth->getUserId();
            $userModel = new UserModel();
            $userInfo = $userModel->getUserDetail($userId);
            
            if (!$userInfo) {
                $this->error('用户不存在');
            }
            
            $this->success('获取成功', $userInfo);
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
    }
    
    /**
     * 更新用户信息
     * @return \think\Response
     */
    public function updateUserInfo()
    {
        $params = $this->request->post();
        
        // 参数验证
        $validate = new Validate([
            'nickname' => 'max:50',
            'avatar' => 'url'
        ]);
        
        if (!$validate->check($params)) {
            $this->error($validate->getError());
        }
        
        try {
            $userId = $this->auth->getUserId();
            $userModel = new UserModel();
            $result = $userModel->updateUserInfo($userId, $params);
            
            if ($result) {
                $this->success('更新成功');
            } else {
                $this->error('更新失败');
            }
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
    }
}
```

### 2.2 后台控制器规范
```php
<?php
namespace app\admin\controller;

use app\common\controller\Backend;
use app\common\model\User as UserModel;
use think\Db;
use think\Exception;

/**
 * 用户管理控制器
 */
class User extends Backend
{
    protected $model = null;
    protected $searchFields = 'username,nickname,mobile';
    protected $childrenAdminIds = [];
    
    public function _initialize()
    {
        parent::_initialize();
        $this->model = new UserModel();
        
        // 获取当前管理员的下级管理员ID
        $this->childrenAdminIds = $this->auth->getChildrenAdminIds(true);
    }
    
    /**
     * 查看用户列表
     */
    public function index()
    {
        if ($this->request->isAjax()) {
            // 构建查询条件
            $where = [];
            
            // 权限过滤
            if (!$this->auth->isSuperAdmin()) {
                $where['admin_id'] = ['in', $this->childrenAdminIds];
            }
            
            // 搜索条件
            $search = $this->request->get('search');
            if ($search) {
                $where['username|nickname|mobile'] = ['like', "%{$search}%"];
            }
            
            // 状态过滤
            $status = $this->request->get('status');
            if ($status !== '') {
                $where['status'] = $status;
            }
            
            // 分页查询
            $page = $this->request->get('page', 1);
            $limit = $this->request->get('limit', 10);
            
            $list = $this->model
                ->where($where)
                ->order('id desc')
                ->paginate($limit, false, ['page' => $page]);
            
            $this->success('获取成功', $list);
        }
        
        return $this->view->fetch();
    }
    
    /**
     * 添加用户
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post();
            
            // 参数验证
            $validate = $this->validate($params, [
                'username' => 'require|unique:user',
                'mobile' => 'require|mobile|unique:user',
                'password' => 'require|length:6,20'
            ]);
            
            if ($validate !== true) {
                $this->error($validate);
            }
            
            Db::startTrans();
            try {
                // 密码加密
                $params['password'] = password_hash($params['password'], PASSWORD_DEFAULT);
                $params['createtime'] = time();
                
                $result = $this->model->save($params);
                
                if ($result) {
                    Db::commit();
                    $this->success('添加成功');
                } else {
                    Db::rollback();
                    $this->error('添加失败');
                }
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
        }
        
        return $this->view->fetch();
    }
}
```

## 3. 模型开发规范

### 3.1 模型基础结构
```php
<?php
namespace app\common\model;

use think\Model;
use think\Exception;

/**
 * 用户模型
 */
class User extends Model
{
    // 表名
    protected $table = 'sys_user';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    
    // 字段类型转换
    protected $type = [
        'createtime' => 'timestamp',
        'updatetime' => 'timestamp',
        'logintime' => 'timestamp'
    ];
    
    // 隐藏字段
    protected $hidden = ['password', 'salt'];
    
    // 追加字段
    protected $append = ['level_name', 'status_text'];
    
    /**
     * 用户等级名称获取器
     */
    public function getLevelNameAttr($value, $data)
    {
        $levelMap = [
            'normal' => '普通用户',
            'entrepreneur' => '创业销售商',
            'advisor' => '拓业顾问',
            'manager' => '守业经理',
            'director' => '享业董事',
            'wisdom_director' => '智慧董事'
        ];
        
        return $levelMap[$data['user_group']] ?? '普通用户';
    }
    
    /**
     * 状态文本获取器
     */
    public function getStatusTextAttr($value, $data)
    {
        $statusMap = [
            'normal' => '正常',
            'hidden' => '禁用'
        ];
        
        return $statusMap[$data['status']] ?? '未知';
    }
    
    /**
     * 密码修改器
     */
    public function setPasswordAttr($value)
    {
        return password_hash($value, PASSWORD_DEFAULT);
    }
    
    /**
     * 用户登录
     * @param string $mobile 手机号
     * @param string $password 密码
     * @return array|false
     */
    public function login($mobile, $password)
    {
        try {
            $user = $this->where('mobile', $mobile)
                        ->where('status', 'normal')
                        ->find();
            
            if (!$user) {
                return false;
            }
            
            // 验证密码
            if (!password_verify($password, $user['password'])) {
                return false;
            }
            
            return $user->toArray();
        } catch (Exception $e) {
            throw new Exception('登录验证失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取用户详细信息
     * @param int $userId 用户ID
     * @return array|null
     */
    public function getUserDetail($userId)
    {
        try {
            $user = $this->with(['wallet'])
                        ->where('id', $userId)
                        ->find();
            
            if (!$user) {
                return null;
            }
            
            return $user->toArray();
        } catch (Exception $e) {
            throw new Exception('获取用户信息失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 更新用户信息
     * @param int $userId 用户ID
     * @param array $data 更新数据
     * @return bool
     */
    public function updateUserInfo($userId, $data)
    {
        try {
            // 过滤不允许更新的字段
            $allowFields = ['nickname', 'avatar', 'gender', 'birthday'];
            $updateData = array_intersect_key($data, array_flip($allowFields));
            
            if (empty($updateData)) {
                throw new Exception('没有可更新的数据');
            }
            
            return $this->where('id', $userId)->update($updateData);
        } catch (Exception $e) {
            throw new Exception('更新用户信息失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 更新登录信息
     * @param int $userId 用户ID
     * @param string $ip 登录IP
     * @return bool
     */
    public function updateLoginInfo($userId, $ip)
    {
        return $this->where('id', $userId)->update([
            'logintime' => time(),
            'loginip' => $ip
        ]);
    }
    
    /**
     * 关联钱包模型
     */
    public function wallet()
    {
        return $this->hasOne('UserWallet', 'user_id');
    }
    
    /**
     * 关联上级用户
     */
    public function parent()
    {
        return $this->belongsTo('User', 'parent_id');
    }
    
    /**
     * 关联下级用户
     */
    public function children()
    {
        return $this->hasMany('User', 'parent_id');
    }
}
```

### 3.2 业务模型规范
```php
<?php
namespace app\common\model;

use think\Model;
use think\Db;
use think\Exception;

/**
 * 用户钱包模型
 */
class UserWallet extends Model
{
    protected $table = 'sys_user_wallet';
    protected $autoWriteTimestamp = true;
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    
    /**
     * 钱包余额变更
     * @param int $userId 用户ID
     * @param float $amount 变更金额
     * @param string $type 变更类型
     * @param string $remark 备注
     * @param int $orderId 关联订单ID
     * @return bool
     */
    public function changeBalance($userId, $amount, $type, $remark = '', $orderId = 0)
    {
        if ($amount == 0) {
            return true;
        }
        
        Db::startTrans();
        try {
            // 锁定用户钱包记录
            $wallet = $this->where('user_id', $userId)->lock(true)->find();
            
            if (!$wallet) {
                // 创建钱包记录
                $wallet = $this->create([
                    'user_id' => $userId,
                    'balance' => 0,
                    'createtime' => time()
                ]);
            }
            
            $beforeBalance = $wallet['balance'];
            
            // 检查余额是否足够（减少操作）
            if ($amount < 0 && $beforeBalance < abs($amount)) {
                throw new Exception('余额不足');
            }
            
            // 更新钱包余额
            $newBalance = $beforeBalance + $amount;
            $wallet->balance = $newBalance;
            $wallet->save();
            
            // 记录钱包流水
            $this->createWalletLog($userId, $amount, $type, $remark, $beforeBalance, $newBalance, $orderId);
            
            Db::commit();
            return true;
        } catch (Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 创建钱包流水记录
     */
    private function createWalletLog($userId, $amount, $type, $remark, $beforeBalance, $afterBalance, $orderId = 0)
    {
        return Db::name('wallet_log')->insert([
            'user_id' => $userId,
            'amount' => $amount,
            'type' => $type,
            'remark' => $remark,
            'before_balance' => $beforeBalance,
            'after_balance' => $afterBalance,
            'order_id' => $orderId,
            'createtime' => time()
        ]);
    }
    
    /**
     * 获取钱包信息
     * @param int $userId 用户ID
     * @return array
     */
    public function getWalletInfo($userId)
    {
        $wallet = $this->where('user_id', $userId)->find();
        
        if (!$wallet) {
            // 创建默认钱包
            $wallet = $this->create([
                'user_id' => $userId,
                'balance' => 0,
                'createtime' => time()
            ]);
        }
        
        return $wallet->toArray();
    }
    
    /**
     * 关联用户模型
     */
    public function user()
    {
        return $this->belongsTo('User', 'user_id');
    }
}
```

## 4. 中间件开发规范

### 4.1 认证中间件
```php
<?php
namespace app\api\middleware;

use app\common\library\Auth;
use think\Request;
use think\Response;

/**
 * API认证中间件
 */
class AuthMiddleware
{
    public function handle(Request $request, \Closure $next)
    {
        $token = $request->header('Authorization');
        
        if (!$token) {
            return json(['code' => 0, 'msg' => '缺少认证令牌'], 401);
        }
        
        // 移除Bearer前缀
        if (strpos($token, 'Bearer ') === 0) {
            $token = substr($token, 7);
        }
        
        $auth = Auth::instance();
        if (!$auth->init($token)) {
            return json(['code' => 0, 'msg' => '认证令牌无效'], 401);
        }
        
        // 将用户信息注入到请求中
        $request->user = $auth->getUser();
        
        return $next($request);
    }
}
```

### 4.2 权限验证中间件
```php
<?php
namespace app\api\middleware;

use think\Request;

/**
 * 权限验证中间件
 */
class PermissionMiddleware
{
    public function handle(Request $request, \Closure $next, $permission = '')
    {
        $user = $request->user;
        
        if (!$user) {
            return json(['code' => 0, 'msg' => '用户未登录'], 401);
        }
        
        // 检查权限
        if ($permission && !$this->hasPermission($user, $permission)) {
            return json(['code' => 0, 'msg' => '权限不足'], 403);
        }
        
        return $next($request);
    }
    
    /**
     * 检查用户权限
     */
    private function hasPermission($user, $permission)
    {
        // 智慧董事拥有所有权限
        if ($user['user_group'] === 'wisdom_director') {
            return true;
        }
        
        // 根据用户等级检查权限
        $levelPermissions = [
            'entrepreneur' => ['view_own_orders', 'create_order'],
            'advisor' => ['view_own_orders', 'create_order', 'view_team_basic'],
            'manager' => ['view_own_orders', 'create_order', 'view_team_basic', 'view_team_detail'],
            'director' => ['view_own_orders', 'create_order', 'view_team_basic', 'view_team_detail', 'withdraw'],
        ];
        
        $userPermissions = $levelPermissions[$user['user_group']] ?? [];
        
        return in_array($permission, $userPermissions);
    }
}
```

## 5. 验证器规范

### 5.1 验证器定义
```php
<?php
namespace app\api\validate;

use think\Validate;

/**
 * 用户验证器
 */
class User extends Validate
{
    protected $rule = [
        'username' => 'require|alphaNum|length:3,20|unique:user',
        'mobile' => 'require|mobile|unique:user',
        'password' => 'require|length:6,20',
        'confirm_password' => 'require|confirm:password',
        'nickname' => 'max:50',
        'avatar' => 'url',
        'captcha' => 'require|length:4'
    ];
    
    protected $message = [
        'username.require' => '用户名不能为空',
        'username.alphaNum' => '用户名只能是字母和数字',
        'username.length' => '用户名长度为3-20位',
        'username.unique' => '用户名已存在',
        'mobile.require' => '手机号不能为空',
        'mobile.mobile' => '手机号格式不正确',
        'mobile.unique' => '手机号已存在',
        'password.require' => '密码不能为空',
        'password.length' => '密码长度为6-20位',
        'confirm_password.require' => '确认密码不能为空',
        'confirm_password.confirm' => '两次密码不一致',
        'captcha.require' => '验证码不能为空',
        'captcha.length' => '验证码长度不正确'
    ];
    
    protected $scene = [
        'login' => ['mobile', 'password', 'captcha'],
        'register' => ['username', 'mobile', 'password', 'confirm_password', 'captcha'],
        'update' => ['nickname', 'avatar']
    ];
}
```

## 6. 服务层规范

### 6.1 业务服务类
```php
<?php
namespace app\common\service;

use app\common\model\User;
use app\common\model\UserWallet;
use app\common\model\Order;
use think\Db;
use think\Exception;

/**
 * 分润服务类
 */
class BonusService
{
    /**
     * 处理订单分润
     * @param int $orderId 订单ID
     * @return bool
     */
    public function processOrderBonus($orderId)
    {
        Db::startTrans();
        try {
            // 获取订单信息
            $order = Order::get($orderId);
            if (!$order || $order['status'] !== 'paid') {
                throw new Exception('订单状态异常');
            }
            
            // 获取用户上级关系链
            $userChain = $this->getUserChain($order['user_id']);
            
            // 按层级发放分润
            foreach ($userChain as $level => $user) {
                $bonusAmount = $this->calculateLevelBonus($order['amount'], $user['user_group'], $level);
                if ($bonusAmount > 0) {
                    $this->distributeBonusToUser($user['id'], $bonusAmount, $orderId);
                }
            }
            
            // 更新订单分润状态
            $order->bonus_status = 'completed';
            $order->save();
            
            Db::commit();
            return true;
        } catch (Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 获取用户上级关系链
     */
    private function getUserChain($userId, $maxLevel = 10)
    {
        $chain = [];
        $currentUserId = $userId;
        $level = 0;
        
        while ($level < $maxLevel) {
            $user = User::where('id', $currentUserId)->find();
            if (!$user || $user['parent_id'] == 0) {
                break;
            }
            
            $parentUser = User::where('id', $user['parent_id'])->find();
            if (!$parentUser) {
                break;
            }
            
            $chain[$level] = $parentUser->toArray();
            $currentUserId = $parentUser['id'];
            $level++;
        }
        
        return $chain;
    }
    
    /**
     * 计算层级分润
     */
    private function calculateLevelBonus($orderAmount, $userLevel, $level)
    {
        $bonusRates = [
            'entrepreneur' => [0.20], // 创业销售商只有直推奖励
            'advisor' => [0.15, 0.05], // 拓业顾问有2层奖励
            'manager' => [0.10, 0.05, 0.03], // 守业经理有3层奖励
            'director' => [0.05, 0.03, 0.02, 0.01] // 享业董事有4层奖励
        ];
        
        if (!isset($bonusRates[$userLevel][$level])) {
            return 0;
        }
        
        return $orderAmount * $bonusRates[$userLevel][$level];
    }
    
    /**
     * 发放分润给用户
     */
    private function distributeBonusToUser($userId, $amount, $orderId)
    {
        $walletService = new UserWallet();
        
        // 更新钱包余额
        $walletService->changeBalance($userId, $amount, 'bonus', '订单分润', $orderId);
        
        // 记录分润明细
        Db::name('bonus_log')->insert([
            'user_id' => $userId,
            'order_id' => $orderId,
            'amount' => $amount,
            'type' => 'order_bonus',
            'status' => 'completed',
            'createtime' => time()
        ]);
    }
}
```

## 7. 配置管理规范

### 7.1 数据库配置
```php
<?php
// application/database.php
return [
    // 数据库类型
    'type'            => 'mysql',
    // 服务器地址
    'hostname'        => env('database.hostname', '127.0.0.1'),
    // 数据库名
    'database'        => env('database.database', 'rongye'),
    // 用户名
    'username'        => env('database.username', 'root'),
    // 密码
    'password'        => env('database.password', 'root'),
    // 端口
    'hostport'        => env('database.hostport', '3306'),
    // 数据库表前缀
    'prefix'          => env('database.prefix', 'sys_'),
    // 数据库编码默认采用utf8mb4
    'charset'         => 'utf8mb4',
    // 数据库调试模式
    'debug'           => env('app.debug', false),
];
```

### 7.2 应用配置
```php
<?php
// application/config.php
return [
    // 应用调试模式
    'app_debug' => env('app.debug', false),
    
    // 应用Trace
    'app_trace' => env('app.trace', false),
    
    // 默认时区
    'default_timezone' => 'Asia/Shanghai',
    
    // 异常页面的模板文件
    'exception_tmpl' => Env::get('think_path') . 'tpl/think_exception.tpl',
    
    // 错误显示信息,非调试模式有效
    'error_message' => '页面错误！请稍后再试～',
    
    // 显示错误信息
    'show_error_msg' => false,
];
```

## 8. 检查清单

### 控制器开发检查
- [ ] 继承正确的基类
- [ ] 添加了参数验证
- [ ] 实现了错误处理
- [ ] 使用了事务保证数据一致性
- [ ] 添加了必要的注释

### 模型开发检查
- [ ] 定义了正确的表名和字段
- [ ] 添加了获取器和修改器
- [ ] 实现了关联关系
- [ ] 添加了业务方法
- [ ] 处理了异常情况

### 中间件检查
- [ ] 正确处理请求和响应
- [ ] 添加了错误处理
- [ ] 实现了权限验证
- [ ] 性能考虑合理

### 服务层检查
- [ ] 业务逻辑清晰
- [ ] 使用了事务保证一致性
- [ ] 错误处理完整
- [ ] 代码可复用性好

---
*后端开发规范是保证系统稳定性和可维护性的基础，请严格遵守。*
