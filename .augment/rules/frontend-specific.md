---
type: "always_apply"
---

# 融业港前端开发特定规范

## 适用范围
本规范适用于融业港前端项目（uni-app + Vue3），包括组件开发、状态管理、路由配置等前端特定开发规范。

## 1. 项目结构规范

### 1.1 目录结构
```
rongye-frontend/
├── pages/                  # 页面文件
│   ├── index/             # 首页模块
│   ├── user/              # 用户模块
│   ├── goods/             # 商品模块
│   ├── order/             # 订单模块
│   ├── commission/        # 分润模块
│   └── store/             # 商家模块
├── sheep/                 # 核心业务层
│   ├── api/               # API接口
│   ├── components/        # 业务组件
│   ├── store/             # 状态管理
│   ├── helper/            # 工具函数
│   └── config/            # 配置文件
├── static/                # 静态资源
├── uni_modules/           # uni-app组件库
└── components/            # 公共组件
```

### 1.2 文件命名规范
```
✅ 正确示例：
pages/user/user-profile.vue
pages/order/order-detail.vue
sheep/components/s-goods-card.vue
sheep/api/user-api.js

❌ 错误示例：
pages/user/UserProfile.vue
pages/order/orderDetail.vue
sheep/components/GoodsCard.vue
sheep/api/userAPI.js
```

## 2. Vue3组件开发规范

### 2.1 组件结构规范
```vue
<template>
  <view class="user-profile">
    <!-- 页面内容 -->
    <view class="profile-header">
      <image :src="userInfo.avatar" class="avatar" />
      <text class="username">{{ userInfo.nickname }}</text>
    </view>
    
    <!-- 使用组件时添加前缀 -->
    <s-wallet-card :balance="userInfo.balance" />
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/sheep/store/user'
import { userApi } from '@/sheep/api/user'

// 响应式数据
const userStore = useUserStore()
const userInfo = ref({})
const loading = ref(false)

// 计算属性
const isVip = computed(() => {
  return userInfo.value.user_group === 'director'
})

// 方法
const getUserInfo = async () => {
  try {
    loading.value = true
    const { data } = await userApi.getUserInfo()
    userInfo.value = data
  } catch (error) {
    console.error('获取用户信息失败:', error)
    uni.showToast({
      title: '获取信息失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  getUserInfo()
})

// 页面生命周期（uni-app特有）
onLoad((options) => {
  console.log('页面参数:', options)
})

onShow(() => {
  // 页面显示时刷新数据
  getUserInfo()
})
</script>

<style lang="scss" scoped>
.user-profile {
  padding: 20rpx;
  
  .profile-header {
    display: flex;
    align-items: center;
    padding: 40rpx 20rpx;
    background: #fff;
    border-radius: 20rpx;
    
    .avatar {
      width: 120rpx;
      height: 120rpx;
      border-radius: 60rpx;
      margin-right: 20rpx;
    }
    
    .username {
      font-size: 36rpx;
      font-weight: 500;
      color: #333;
    }
  }
}
</style>
```

### 2.2 组件Props规范
```vue
<script setup>
// Props定义
const props = defineProps({
  // 基础类型
  title: {
    type: String,
    required: true
  },
  
  // 数字类型
  amount: {
    type: Number,
    default: 0,
    validator: (value) => value >= 0
  },
  
  // 布尔类型
  disabled: {
    type: Boolean,
    default: false
  },
  
  // 对象类型
  userInfo: {
    type: Object,
    default: () => ({})
  },
  
  // 数组类型
  list: {
    type: Array,
    default: () => []
  },
  
  // 枚举类型
  type: {
    type: String,
    default: 'primary',
    validator: (value) => ['primary', 'secondary', 'danger'].includes(value)
  }
})

// Emits定义
const emit = defineEmits(['click', 'change', 'update:modelValue'])

// 方法
const handleClick = () => {
  emit('click', props.userInfo)
}

const handleChange = (value) => {
  emit('change', value)
  emit('update:modelValue', value)
}
</script>
```

### 2.3 组件插槽规范
```vue
<template>
  <view class="card-container">
    <!-- 默认插槽 -->
    <view class="card-header">
      <slot name="header">
        <text class="default-title">默认标题</text>
      </slot>
    </view>
    
    <!-- 内容插槽 -->
    <view class="card-content">
      <slot>
        <text>默认内容</text>
      </slot>
    </view>
    
    <!-- 作用域插槽 -->
    <view class="card-footer">
      <slot name="footer" :data="footerData" :loading="loading">
        <text>默认底部</text>
      </slot>
    </view>
  </view>
</template>

<script setup>
const footerData = ref({ count: 10 })
const loading = ref(false)
</script>
```

## 3. 状态管理规范（Pinia）

### 3.1 Store结构规范
```javascript
// sheep/store/user.js
import { defineStore } from 'pinia'
import { userApi } from '@/sheep/api/user'

export const useUserStore = defineStore('user', {
  // 状态
  state: () => ({
    // 用户信息
    userInfo: {},
    
    // 登录状态
    isLogin: false,
    
    // 认证token
    token: '',
    
    // 钱包信息
    walletInfo: {
      balance: 0,
      points: 0
    },
    
    // 加载状态
    loading: {
      userInfo: false,
      wallet: false
    }
  }),
  
  // 计算属性
  getters: {
    // 用户等级中文名
    userLevelName: (state) => {
      const levelMap = {
        'normal': '普通用户',
        'entrepreneur': '创业销售商',
        'advisor': '拓业顾问',
        'manager': '守业经理',
        'director': '享业董事',
        'wisdom_director': '智慧董事'
      }
      return levelMap[state.userInfo.user_group] || '普通用户'
    },
    
    // 是否为VIP用户
    isVip: (state) => {
      return ['director', 'wisdom_director'].includes(state.userInfo.user_group)
    },
    
    // 格式化余额
    formattedBalance: (state) => {
      return `¥${state.walletInfo.balance.toFixed(2)}`
    }
  },
  
  // 操作
  actions: {
    // 登录
    async login(loginData) {
      try {
        const { data } = await userApi.login(loginData)
        
        this.token = data.token
        this.userInfo = data.userInfo
        this.isLogin = true
        
        // 登录成功后获取钱包信息
        await this.getWalletInfo()
        
        return data
      } catch (error) {
        this.logout()
        throw error
      }
    },
    
    // 退出登录
    logout() {
      this.token = ''
      this.userInfo = {}
      this.isLogin = false
      this.walletInfo = { balance: 0, points: 0 }
      
      // 清除本地存储
      uni.removeStorageSync('token')
      
      // 跳转到登录页
      uni.reLaunch({
        url: '/pages/user/login'
      })
    },
    
    // 获取用户信息
    async getUserInfo() {
      if (this.loading.userInfo) return
      
      try {
        this.loading.userInfo = true
        const { data } = await userApi.getUserInfo()
        this.userInfo = data
      } catch (error) {
        console.error('获取用户信息失败:', error)
        throw error
      } finally {
        this.loading.userInfo = false
      }
    },
    
    // 获取钱包信息
    async getWalletInfo() {
      if (this.loading.wallet) return
      
      try {
        this.loading.wallet = true
        const { data } = await userApi.getWalletInfo()
        this.walletInfo = data
      } catch (error) {
        console.error('获取钱包信息失败:', error)
        throw error
      } finally {
        this.loading.wallet = false
      }
    },
    
    // 更新用户信息
    updateUserInfo(newInfo) {
      this.userInfo = { ...this.userInfo, ...newInfo }
    },
    
    // 更新钱包余额
    updateBalance(amount) {
      this.walletInfo.balance = amount
    }
  },
  
  // 持久化配置
  persist: {
    enabled: true,
    strategies: [
      {
        key: 'user-store',
        storage: 'localStorage',
        paths: ['userInfo', 'token', 'isLogin'] // 只持久化部分状态
      }
    ]
  }
})
```

### 3.2 Store使用规范
```vue
<script setup>
import { useUserStore } from '@/sheep/store/user'
import { storeToRefs } from 'pinia'

// 获取store实例
const userStore = useUserStore()

// 解构响应式状态（使用storeToRefs保持响应性）
const { userInfo, isLogin, walletInfo, loading } = storeToRefs(userStore)

// 解构方法（不需要storeToRefs）
const { login, logout, getUserInfo } = userStore

// 计算属性可以直接使用
const userLevelName = computed(() => userStore.userLevelName)

// 监听store状态变化
watch(() => userStore.isLogin, (newValue) => {
  if (!newValue) {
    // 用户退出登录，跳转到登录页
    uni.reLaunch({ url: '/pages/user/login' })
  }
})
</script>
```

## 4. API接口规范

### 4.1 API文件结构
```javascript
// sheep/api/user.js
import request from '@/sheep/request'

export const userApi = {
  // 用户登录
  login: (data) => request.post('/api/user/login', data),
  
  // 用户注册
  register: (data) => request.post('/api/user/register', data),
  
  // 获取用户信息
  getUserInfo: () => request.get('/api/user/info'),
  
  // 更新用户信息
  updateUserInfo: (data) => request.put('/api/user/info', data),
  
  // 获取钱包信息
  getWalletInfo: () => request.get('/api/user/wallet'),
  
  // 获取分润记录
  getBonusList: (params) => request.get('/api/user/bonus', { params }),
  
  // 获取团队信息
  getTeamInfo: (params) => request.get('/api/user/team', { params }),
  
  // 上传头像
  uploadAvatar: (filePath) => {
    return new Promise((resolve, reject) => {
      uni.uploadFile({
        url: request.baseURL + '/api/user/avatar',
        filePath: filePath,
        name: 'avatar',
        header: {
          'Authorization': 'Bearer ' + uni.getStorageSync('token')
        },
        success: (res) => {
          const data = JSON.parse(res.data)
          if (data.code === 1) {
            resolve(data)
          } else {
            reject(new Error(data.msg))
          }
        },
        fail: reject
      })
    })
  }
}
```

### 4.2 请求拦截器配置
```javascript
// sheep/request/index.js
import { useUserStore } from '@/sheep/store/user'

class Request {
  constructor() {
    this.baseURL = process.env.NODE_ENV === 'development' 
      ? 'http://localhost:8005' 
      : 'https://api.rongye.com'
    this.timeout = 10000
  }
  
  // 请求拦截器
  interceptRequest(config) {
    // 添加token
    const userStore = useUserStore()
    if (userStore.token) {
      config.header = config.header || {}
      config.header.Authorization = `Bearer ${userStore.token}`
    }
    
    // 添加时间戳防止缓存
    if (config.method === 'GET') {
      config.data = config.data || {}
      config.data._t = Date.now()
    }
    
    // 显示加载提示
    if (config.loading !== false) {
      uni.showLoading({
        title: config.loadingText || '加载中...',
        mask: true
      })
    }
    
    return config
  }
  
  // 响应拦截器
  interceptResponse(response, config) {
    // 隐藏加载提示
    if (config.loading !== false) {
      uni.hideLoading()
    }
    
    const { data } = response
    
    // 请求成功
    if (data.code === 1) {
      return Promise.resolve(data)
    }
    
    // token失效
    if (data.code === 401) {
      const userStore = useUserStore()
      userStore.logout()
      return Promise.reject(new Error('登录已过期'))
    }
    
    // 其他错误
    const error = new Error(data.msg || '请求失败')
    error.code = data.code
    error.data = data.data
    
    // 显示错误提示
    if (config.showError !== false) {
      uni.showToast({
        title: error.message,
        icon: 'none',
        duration: 2000
      })
    }
    
    return Promise.reject(error)
  }
  
  // 通用请求方法
  request(config) {
    return new Promise((resolve, reject) => {
      // 请求拦截
      config = this.interceptRequest(config)
      
      uni.request({
        url: this.baseURL + config.url,
        method: config.method || 'GET',
        data: config.data,
        header: config.header,
        timeout: config.timeout || this.timeout,
        success: (response) => {
          this.interceptResponse(response, config)
            .then(resolve)
            .catch(reject)
        },
        fail: (error) => {
          // 隐藏加载提示
          if (config.loading !== false) {
            uni.hideLoading()
          }
          
          // 网络错误处理
          let message = '网络连接失败'
          if (error.errMsg.includes('timeout')) {
            message = '请求超时'
          } else if (error.errMsg.includes('fail')) {
            message = '网络连接失败'
          }
          
          if (config.showError !== false) {
            uni.showToast({
              title: message,
              icon: 'none'
            })
          }
          
          reject(new Error(message))
        }
      })
    })
  }
  
  // GET请求
  get(url, config = {}) {
    return this.request({ ...config, url, method: 'GET' })
  }
  
  // POST请求
  post(url, data, config = {}) {
    return this.request({ ...config, url, method: 'POST', data })
  }
  
  // PUT请求
  put(url, data, config = {}) {
    return this.request({ ...config, url, method: 'PUT', data })
  }
  
  // DELETE请求
  delete(url, config = {}) {
    return this.request({ ...config, url, method: 'DELETE' })
  }
}

export default new Request()
```

## 5. 路由和页面管理

### 5.1 pages.json配置规范
```json
{
  "pages": [
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "融业港",
        "enablePullDownRefresh": true,
        "backgroundTextStyle": "dark"
      }
    },
    {
      "path": "pages/user/login",
      "style": {
        "navigationBarTitleText": "登录",
        "navigationStyle": "custom"
      }
    }
  ],
  "subPackages": [
    {
      "root": "pages/commission",
      "pages": [
        {
          "path": "team",
          "style": {
            "navigationBarTitleText": "我的团队"
          }
        },
        {
          "path": "bonus",
          "style": {
            "navigationBarTitleText": "分润记录"
          }
        }
      ]
    }
  ],
  "tabBar": {
    "color": "#999999",
    "selectedColor": "#007AFF",
    "backgroundColor": "#FFFFFF",
    "borderStyle": "black",
    "list": [
      {
        "pagePath": "pages/index/index",
        "iconPath": "static/tabbar/home.png",
        "selectedIconPath": "static/tabbar/home-active.png",
        "text": "首页"
      },
      {
        "pagePath": "pages/goods/list",
        "iconPath": "static/tabbar/goods.png",
        "selectedIconPath": "static/tabbar/goods-active.png",
        "text": "商品"
      },
      {
        "pagePath": "pages/user/index",
        "iconPath": "static/tabbar/user.png",
        "selectedIconPath": "static/tabbar/user-active.png",
        "text": "我的"
      }
    ]
  },
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "融业港",
    "navigationBarBackgroundColor": "#FFFFFF",
    "backgroundColor": "#F8F8F8"
  }
}
```

### 5.2 页面跳转规范
```javascript
// 路由工具类
class Router {
  // 页面跳转
  static navigateTo(url, params = {}) {
    const query = Object.keys(params).length > 0 
      ? '?' + Object.keys(params).map(key => `${key}=${encodeURIComponent(params[key])}`).join('&')
      : ''
    
    uni.navigateTo({
      url: url + query,
      fail: (err) => {
        console.error('页面跳转失败:', err)
      }
    })
  }
  
  // 替换页面
  static redirectTo(url, params = {}) {
    const query = Object.keys(params).length > 0 
      ? '?' + Object.keys(params).map(key => `${key}=${encodeURIComponent(params[key])}`).join('&')
      : ''
    
    uni.redirectTo({
      url: url + query
    })
  }
  
  // 返回上一页
  static navigateBack(delta = 1) {
    uni.navigateBack({
      delta
    })
  }
  
  // 重新启动到指定页面
  static reLaunch(url) {
    uni.reLaunch({
      url
    })
  }
  
  // 切换到tabBar页面
  static switchTab(url) {
    uni.switchTab({
      url
    })
  }
}

// 使用示例
Router.navigateTo('/pages/goods/detail', { id: 123 })
Router.redirectTo('/pages/user/login')
Router.switchTab('/pages/index/index')
```

## 6. 检查清单

### 组件开发检查
- [ ] 组件名称符合命名规范
- [ ] Props定义完整且有验证
- [ ] Emits事件定义清晰
- [ ] 样式使用scoped避免污染
- [ ] 添加了必要的注释

### 状态管理检查
- [ ] Store结构清晰合理
- [ ] 使用storeToRefs保持响应性
- [ ] 异步操作有错误处理
- [ ] 持久化配置正确

### API接口检查
- [ ] 接口分类清晰
- [ ] 请求和响应拦截器配置完整
- [ ] 错误处理机制完善
- [ ] 加载状态处理合理

### 路由配置检查
- [ ] pages.json配置正确
- [ ] 页面跳转使用统一方法
- [ ] 参数传递和接收正确
- [ ] 分包配置合理

---
*前端开发规范是保证代码质量和团队协作效率的基础，请严格遵守。*
