---
type: "manual"
---

# 融业港项目业务逻辑开发规范

## 适用范围
本规范适用于融业港项目的所有业务逻辑开发，包括分润系统、钱包系统、用户等级管理等核心业务功能。

## 1. 分润系统开发规范

### 1.1 分润计算规则
```javascript
// ✅ 正确的分润计算实现
const BONUS_RATES = {
  entrepreneur: 0.2,    // 创业销售商 20%
  advisor: 0.15,        // 拓业顾问 15%
  manager: 0.1,         // 守业经理 10%
  director: 0.05        // 享业董事 5%
}

/**
 * 计算分润金额
 * @param {number} orderAmount - 订单金额
 * @param {string} userLevel - 用户等级
 * @returns {number} 分润金额（保留2位小数）
 */
const calculateBonus = (orderAmount, userLevel) => {
  if (!BONUS_RATES[userLevel]) {
    throw new Error(`无效的用户等级: ${userLevel}`)
  }
  
  const bonusAmount = orderAmount * BONUS_RATES[userLevel]
  return Math.round(bonusAmount * 100) / 100
}
```

### 1.2 分润发放流程
```php
<?php
/**
 * 订单支付成功后的分润处理
 */
class BonusProcessor
{
    /**
     * 处理订单分润
     * @param int $orderId 订单ID
     * @return bool
     */
    public function processOrderBonus($orderId)
    {
        try {
            // 1. 获取订单信息
            $order = $this->getOrderInfo($orderId);
            if (!$order || $order['status'] !== 'paid') {
                throw new Exception('订单状态异常');
            }
            
            // 2. 获取用户上级关系链
            $userChain = $this->getUserChain($order['user_id']);
            
            // 3. 按层级发放分润
            foreach ($userChain as $level => $user) {
                $bonusAmount = $this->calculateLevelBonus($order['amount'], $user['level'], $level);
                if ($bonusAmount > 0) {
                    $this->distributeBonusToUser($user['id'], $bonusAmount, $orderId);
                }
            }
            
            // 4. 记录分润日志
            $this->logBonusDistribution($orderId, $userChain);
            
            return true;
        } catch (Exception $e) {
            // 记录错误日志
            Log::error('分润处理失败: ' . $e->getMessage(), ['order_id' => $orderId]);
            return false;
        }
    }
    
    /**
     * 计算层级分润
     * @param float $orderAmount 订单金额
     * @param string $userLevel 用户等级
     * @param int $level 层级（0为直接上级）
     * @return float
     */
    private function calculateLevelBonus($orderAmount, $userLevel, $level)
    {
        $bonusRates = [
            'entrepreneur' => [0.20], // 创业销售商只有直推奖励
            'advisor' => [0.15, 0.05], // 拓业顾问有2层奖励
            'manager' => [0.10, 0.05, 0.03], // 守业经理有3层奖励
            'director' => [0.05, 0.03, 0.02, 0.01] // 享业董事有4层奖励
        ];
        
        if (!isset($bonusRates[$userLevel][$level])) {
            return 0;
        }
        
        return $orderAmount * $bonusRates[$userLevel][$level];
    }
}
```

### 1.3 分润数据完整性保证
```php
<?php
/**
 * 分润事务处理
 */
class BonusTransaction
{
    public function distributeBonusWithTransaction($orderId, $bonusData)
    {
        Db::startTrans();
        try {
            foreach ($bonusData as $bonus) {
                // 1. 更新用户钱包
                $this->updateUserWallet($bonus['user_id'], $bonus['amount']);
                
                // 2. 记录钱包流水
                $this->createWalletLog($bonus['user_id'], $bonus['amount'], 'bonus', $orderId);
                
                // 3. 记录分润明细
                $this->createBonusRecord($bonus);
            }
            
            // 4. 更新订单分润状态
            $this->updateOrderBonusStatus($orderId, 'completed');
            
            Db::commit();
            return true;
        } catch (Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
}
```

## 2. 钱包系统开发规范

### 2.1 钱包操作规范
```php
<?php
/**
 * 钱包操作必须遵循的规范
 */
class WalletService
{
    /**
     * 钱包余额变更（必须使用事务）
     * @param int $userId 用户ID
     * @param float $amount 变更金额（正数为增加，负数为减少）
     * @param string $type 变更类型
     * @param string $remark 备注
     * @return bool
     */
    public function changeBalance($userId, $amount, $type, $remark = '')
    {
        if ($amount == 0) {
            return true;
        }
        
        Db::startTrans();
        try {
            // 1. 锁定用户钱包记录
            $wallet = Db::name('user_wallet')
                       ->where('user_id', $userId)
                       ->lock(true)
                       ->find();
            
            if (!$wallet) {
                throw new Exception('用户钱包不存在');
            }
            
            // 2. 检查余额是否足够（减少操作）
            if ($amount < 0 && $wallet['balance'] < abs($amount)) {
                throw new Exception('余额不足');
            }
            
            // 3. 更新钱包余额
            $newBalance = $wallet['balance'] + $amount;
            Db::name('user_wallet')
              ->where('user_id', $userId)
              ->update(['balance' => $newBalance, 'updatetime' => time()]);
            
            // 4. 记录钱包流水
            $this->createWalletLog($userId, $amount, $type, $remark, $wallet['balance'], $newBalance);
            
            Db::commit();
            return true;
        } catch (Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 创建钱包流水记录
     */
    private function createWalletLog($userId, $amount, $type, $remark, $beforeBalance, $afterBalance)
    {
        return Db::name('wallet_log')->insert([
            'user_id' => $userId,
            'amount' => $amount,
            'type' => $type,
            'remark' => $remark,
            'before_balance' => $beforeBalance,
            'after_balance' => $afterBalance,
            'createtime' => time()
        ]);
    }
}
```

### 2.2 钱包日志类型规范
```php
<?php
// 钱包变更类型常量
class WalletLogType
{
    const RECHARGE = 'recharge';        // 充值
    const WITHDRAW = 'withdraw';        // 提现
    const BONUS = 'bonus';              // 分润收入
    const CONSUME = 'consume';          // 消费支出
    const REFUND = 'refund';            // 退款
    const TRANSFER_IN = 'transfer_in';  // 转入
    const TRANSFER_OUT = 'transfer_out'; // 转出
    const SYSTEM_ADD = 'system_add';    // 系统增加
    const SYSTEM_SUB = 'system_sub';    // 系统扣除
}
```

## 3. 用户等级管理规范

### 3.1 用户等级升级规则
```php
<?php
/**
 * 用户等级升级检查
 */
class UserLevelService
{
    /**
     * 检查并升级用户等级
     * @param int $userId 用户ID
     * @return bool
     */
    public function checkAndUpgradeUserLevel($userId)
    {
        try {
            $user = $this->getUserInfo($userId);
            $currentLevel = $user['level'];
            
            // 检查是否满足升级条件
            $newLevel = $this->checkUpgradeConditions($userId, $currentLevel);
            
            if ($newLevel && $newLevel !== $currentLevel) {
                return $this->upgradeUserLevel($userId, $newLevel);
            }
            
            return false;
        } catch (Exception $e) {
            Log::error('用户等级检查失败: ' . $e->getMessage(), ['user_id' => $userId]);
            return false;
        }
    }
    
    /**
     * 检查升级条件
     */
    private function checkUpgradeConditions($userId, $currentLevel)
    {
        $conditions = [
            'entrepreneur' => function($userId) {
                // 创业销售商：购买799元产品
                return $this->hasPurchasedProduct($userId, 799);
            },
            'advisor' => function($userId) {
                // 拓业顾问：个人销售3盒产品
                return $this->getPersonalSalesCount($userId) >= 3;
            },
            'manager' => function($userId) {
                // 守业经理：团队出现3个拓业顾问
                return $this->getTeamAdvisorCount($userId) >= 3;
            },
            'director' => function($userId) {
                // 享业董事：团队出现3个守业经理
                return $this->getTeamManagerCount($userId) >= 3;
            },
            'wisdom_director' => function($userId) {
                // 智慧董事：团队出现3个享业董事
                return $this->getTeamDirectorCount($userId) >= 3;
            }
        ];
        
        $levels = ['entrepreneur', 'advisor', 'manager', 'director', 'wisdom_director'];
        $currentIndex = array_search($currentLevel, $levels);
        
        // 从当前等级的下一级开始检查
        for ($i = $currentIndex + 1; $i < count($levels); $i++) {
            $level = $levels[$i];
            if (isset($conditions[$level]) && $conditions[$level]($userId)) {
                return $level;
            }
        }
        
        return null;
    }
}
```

## 4. 复购判定规范

### 4.1 复购检查逻辑
```php
<?php
/**
 * 复购判定服务
 */
class RepurchaseService
{
    /**
     * 检查用户是否需要复购
     * @param int $userId 用户ID
     * @return array
     */
    public function checkRepurchaseRequired($userId)
    {
        try {
            $user = $this->getUserInfo($userId);
            
            // 享业董事及以上等级不需要复购
            if (in_array($user['level'], ['director', 'wisdom_director'])) {
                return ['required' => false, 'reason' => '享业董事及以上等级无需复购'];
            }
            
            // 检查十倍复购规则
            $totalBonus = $this->getUserTotalBonus($userId);
            $lastPurchaseAmount = $this->getLastPurchaseAmount($userId);
            
            if ($totalBonus >= $lastPurchaseAmount * 10) {
                return [
                    'required' => true, 
                    'reason' => '分润金额已达购买金额的10倍',
                    'total_bonus' => $totalBonus,
                    'purchase_amount' => $lastPurchaseAmount
                ];
            }
            
            // 检查无销售出局规则
            $lastSaleTime = $this->getLastSaleTime($userId);
            if ($lastSaleTime && (time() - $lastSaleTime) > 30 * 24 * 3600) {
                return [
                    'required' => true,
                    'reason' => '超过30天无销售记录',
                    'last_sale_time' => $lastSaleTime
                ];
            }
            
            return ['required' => false, 'reason' => '暂无需复购'];
        } catch (Exception $e) {
            Log::error('复购检查失败: ' . $e->getMessage(), ['user_id' => $userId]);
            return ['required' => false, 'reason' => '检查失败'];
        }
    }
}
```

## 5. 数据一致性保证

### 5.1 关键业务操作必须使用事务
```php
<?php
// ✅ 正确示例：订单支付处理
public function processOrderPayment($orderId, $paymentData)
{
    Db::startTrans();
    try {
        // 1. 更新订单状态
        $this->updateOrderStatus($orderId, 'paid');
        
        // 2. 处理分润
        $this->processOrderBonus($orderId);
        
        // 3. 更新库存
        $this->updateProductStock($orderId);
        
        // 4. 发送通知
        $this->sendOrderNotification($orderId);
        
        Db::commit();
        return true;
    } catch (Exception $e) {
        Db::rollback();
        throw $e;
    }
}
```

### 5.2 数据校验规范
```php
<?php
/**
 * 业务数据校验
 */
class BusinessValidator
{
    /**
     * 验证订单数据
     */
    public function validateOrderData($orderData)
    {
        $rules = [
            'user_id' => 'require|integer|gt:0',
            'product_id' => 'require|integer|gt:0',
            'quantity' => 'require|integer|between:1,999',
            'amount' => 'require|float|gt:0'
        ];
        
        $validate = new Validate($rules);
        if (!$validate->check($orderData)) {
            throw new ValidateException($validate->getError());
        }
        
        // 业务逻辑验证
        $this->validateBusinessLogic($orderData);
    }
    
    private function validateBusinessLogic($orderData)
    {
        // 检查用户状态
        $user = $this->getUserInfo($orderData['user_id']);
        if ($user['status'] !== 'active') {
            throw new Exception('用户状态异常，无法下单');
        }
        
        // 检查产品状态
        $product = $this->getProductInfo($orderData['product_id']);
        if ($product['status'] !== 'active') {
            throw new Exception('商品已下架');
        }
        
        // 检查库存
        if ($product['stock'] < $orderData['quantity']) {
            throw new Exception('库存不足');
        }
    }
}
```

## 6. 错误处理和日志记录

### 6.1 业务异常处理
```php
<?php
/**
 * 业务异常类
 */
class BusinessException extends Exception
{
    protected $errorCode;
    protected $errorData;
    
    public function __construct($message, $code = 0, $data = [])
    {
        parent::__construct($message, $code);
        $this->errorCode = $code;
        $this->errorData = $data;
    }
    
    public function getErrorData()
    {
        return $this->errorData;
    }
}

// 使用示例
try {
    $result = $this->processBusinessLogic();
} catch (BusinessException $e) {
    // 业务异常，返回错误信息给用户
    $this->error($e->getMessage(), $e->getErrorData(), $e->getCode());
} catch (Exception $e) {
    // 系统异常，记录日志并返回通用错误
    Log::error('系统异常: ' . $e->getMessage(), [
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ]);
    $this->error('系统繁忙，请稍后重试');
}
```

### 6.2 关键业务日志记录
```php
<?php
/**
 * 业务日志记录
 */
class BusinessLogger
{
    /**
     * 记录分润日志
     */
    public static function logBonus($orderId, $userId, $amount, $type)
    {
        Log::info('分润发放', [
            'order_id' => $orderId,
            'user_id' => $userId,
            'amount' => $amount,
            'type' => $type,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * 记录钱包操作日志
     */
    public static function logWalletOperation($userId, $amount, $type, $beforeBalance, $afterBalance)
    {
        Log::info('钱包操作', [
            'user_id' => $userId,
            'amount' => $amount,
            'type' => $type,
            'before_balance' => $beforeBalance,
            'after_balance' => $afterBalance,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
}
```

## 7. 性能优化规范

### 7.1 数据库查询优化
```php
<?php
// ✅ 正确示例：使用索引和分页
public function getUserBonusList($userId, $page = 1, $limit = 20)
{
    return Db::name('bonus_log')
             ->where('user_id', $userId)
             ->order('createtime desc')
             ->page($page, $limit)
             ->select();
}

// ❌ 错误示例：查询所有数据
public function getUserBonusList($userId)
{
    return Db::name('bonus_log')
             ->where('user_id', $userId)
             ->select(); // 可能返回大量数据
}
```

### 7.2 缓存使用规范
```php
<?php
/**
 * 缓存使用示例
 */
class CacheService
{
    /**
     * 获取用户等级信息（带缓存）
     */
    public function getUserLevel($userId)
    {
        $cacheKey = "user_level_{$userId}";
        $level = Cache::get($cacheKey);
        
        if ($level === false) {
            $level = Db::name('user')->where('id', $userId)->value('level');
            Cache::set($cacheKey, $level, 3600); // 缓存1小时
        }
        
        return $level;
    }
    
    /**
     * 清除用户相关缓存
     */
    public function clearUserCache($userId)
    {
        $cacheKeys = [
            "user_level_{$userId}",
            "user_info_{$userId}",
            "user_wallet_{$userId}"
        ];
        
        foreach ($cacheKeys as $key) {
            Cache::rm($key);
        }
    }
}
```

## 8. 检查清单

### 业务逻辑开发检查
- [ ] 关键操作使用了数据库事务
- [ ] 添加了完整的数据验证
- [ ] 实现了错误处理和日志记录
- [ ] 考虑了并发安全问题
- [ ] 添加了必要的缓存机制
- [ ] 编写了单元测试
- [ ] 更新了相关文档

### 分润系统检查
- [ ] 分润计算逻辑正确
- [ ] 分润发放使用事务保证一致性
- [ ] 记录了完整的分润日志
- [ ] 处理了异常情况

### 钱包系统检查
- [ ] 钱包操作使用了行锁
- [ ] 余额检查逻辑正确
- [ ] 记录了钱包流水
- [ ] 处理了并发操作

---
*本规范涉及核心业务逻辑，任何修改都必须经过严格测试和代码审查。*
