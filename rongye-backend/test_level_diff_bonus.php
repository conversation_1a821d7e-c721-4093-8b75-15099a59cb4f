<?php
/**
 * 级差制度分润测试脚本
 * 用于验证新的级差制度分润计算是否正确
 */

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/application/common.php';

use addons\shopro\library\Bonus;
use app\common\model\User;
use think\Db;
use think\Log;

class LevelDiffBonusTest
{
    /**
     * 测试级差制度分润计算
     */
    public function testLevelDiffBonus()
    {
        echo "=== 级差制度分润测试 ===\n";
        
        // 测试场景1：单层级差
        echo "\n--- 测试场景1：单层级差 ---\n";
        $this->testSingleLevelDiff();
        
        // 测试场景2：多层级差
        echo "\n--- 测试场景2：多层级差 ---\n";
        $this->testMultiLevelDiff();
        
        // 测试场景3：相同等级不发放
        echo "\n--- 测试场景3：相同等级不发放 ---\n";
        $this->testSameLevelNoDiff();
        
        echo "\n=== 测试完成 ===\n";
    }
    
    /**
     * 测试单层级差：创业销售商 -> 拓业顾问
     */
    private function testSingleLevelDiff()
    {
        $orderAmount = 1000.00;
        
        // 模拟用户等级配置
        $buyerLevel = ['team_bonus_rate' => 0.20]; // 创业销售商 20%
        $parentLevel = ['team_bonus_rate' => 0.25]; // 拓业顾问 25%
        
        $expectedDiff = 0.25 - 0.20; // 5%
        $expectedBonus = $orderAmount * $expectedDiff; // 50元
        
        echo "订单金额: {$orderAmount}元\n";
        echo "购买者等级: 创业销售商(20%)\n";
        echo "上级等级: 拓业顾问(25%)\n";
        echo "预期级差: {$expectedDiff} (" . ($expectedDiff * 100) . "%)\n";
        echo "预期奖励: {$expectedBonus}元\n";
        
        // 验证计算逻辑
        $actualDiff = $parentLevel['team_bonus_rate'] - $buyerLevel['team_bonus_rate'];
        $actualBonus = $orderAmount * $actualDiff;
        
        echo "实际级差: {$actualDiff} (" . ($actualDiff * 100) . "%)\n";
        echo "实际奖励: {$actualBonus}元\n";
        echo "结果: " . ($actualBonus == $expectedBonus ? "✓ 通过" : "✗ 失败") . "\n";
    }
    
    /**
     * 测试多层级差：创业销售商 -> 拓业顾问 -> 守业经理 -> 享业董事
     */
    private function testMultiLevelDiff()
    {
        $orderAmount = 1000.00;
        
        // 模拟用户等级配置
        $levels = [
            'buyer' => ['name' => '创业销售商', 'team_bonus_rate' => 0.20],
            'advisor' => ['name' => '拓业顾问', 'team_bonus_rate' => 0.25],
            'manager' => ['name' => '守业经理', 'team_bonus_rate' => 0.30],
            'director' => ['name' => '享业董事', 'team_bonus_rate' => 0.35],
        ];
        
        echo "订单金额: {$orderAmount}元\n";
        echo "用户层级链: 创业销售商 -> 拓业顾问 -> 守业经理 -> 享业董事\n";
        
        $currentRate = $levels['buyer']['team_bonus_rate'];
        $totalBonus = 0;
        
        foreach (['advisor', 'manager', 'director'] as $levelKey) {
            $level = $levels[$levelKey];
            $levelDiff = $level['team_bonus_rate'] - $currentRate;
            $levelBonus = $orderAmount * $levelDiff;
            $totalBonus += $levelBonus;
            
            echo "{$level['name']}: 级差{$levelDiff}(" . ($levelDiff * 100) . "%) = {$levelBonus}元\n";
            
            $currentRate = $level['team_bonus_rate'];
        }
        
        echo "总分润: {$totalBonus}元 (" . ($totalBonus / $orderAmount * 100) . "%)\n";
        
        // 验证总分润不超过最高等级比例
        $maxRate = $levels['director']['team_bonus_rate'];
        $maxBonus = $orderAmount * ($maxRate - $levels['buyer']['team_bonus_rate']);
        echo "最大可分润: {$maxBonus}元 (" . ($maxBonus / $orderAmount * 100) . "%)\n";
        echo "结果: " . ($totalBonus <= $maxBonus ? "✓ 通过" : "✗ 失败") . "\n";
    }
    
    /**
     * 测试相同等级不发放级差
     */
    private function testSameLevelNoDiff()
    {
        $orderAmount = 1000.00;
        
        // 模拟相同等级
        $buyerLevel = ['team_bonus_rate' => 0.25]; // 拓业顾问
        $parentLevel = ['team_bonus_rate' => 0.25]; // 拓业顾问
        
        $levelDiff = $parentLevel['team_bonus_rate'] - $buyerLevel['team_bonus_rate'];
        $bonus = $orderAmount * $levelDiff;
        
        echo "订单金额: {$orderAmount}元\n";
        echo "购买者等级: 拓业顾问(25%)\n";
        echo "上级等级: 拓业顾问(25%)\n";
        echo "级差: {$levelDiff} (" . ($levelDiff * 100) . "%)\n";
        echo "奖励: {$bonus}元\n";
        echo "结果: " . ($bonus == 0 ? "✓ 通过（相同等级不发放）" : "✗ 失败") . "\n";
    }
    
    /**
     * 测试数据库中的实际配置
     */
    public function testDatabaseConfig()
    {
        echo "\n=== 数据库配置测试 ===\n";
        
        try {
            $levels = Db::name('user_group')
                ->where('level_code', 'neq', '')
                ->where('status', 'normal')
                ->order('level_order', 'asc')
                ->select();
            
            echo "当前数据库中的等级配置:\n";
            foreach ($levels as $level) {
                echo "- {$level['name']}({$level['level_code']}): {$level['team_bonus_rate']}(" . ($level['team_bonus_rate'] * 100) . "%)\n";
            }
            
            // 验证级差递增
            $prevRate = 0;
            $isValid = true;
            foreach ($levels as $level) {
                if ($level['team_bonus_rate'] < $prevRate) {
                    $isValid = false;
                    echo "✗ 错误: {$level['name']}的比例({$level['team_bonus_rate']})小于上一级({$prevRate})\n";
                }
                $prevRate = $level['team_bonus_rate'];
            }
            
            echo "配置验证: " . ($isValid ? "✓ 通过" : "✗ 失败") . "\n";
            
        } catch (Exception $e) {
            echo "数据库连接失败: " . $e->getMessage() . "\n";
        }
    }
}

// 运行测试
$test = new LevelDiffBonusTest();
$test->testLevelDiffBonus();
$test->testDatabaseConfig();
