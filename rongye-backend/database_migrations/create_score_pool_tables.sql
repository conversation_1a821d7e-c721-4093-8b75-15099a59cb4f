-- 积分池机制数据库表创建脚本
-- 创建时间: 2025-01-08
-- 描述: 为融业港分润系统添加积分池功能

-- 积分池主表
CREATE TABLE IF NOT EXISTS `sys_shopro_score_pool` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '积分池ID',
  `pool_name` varchar(100) NOT NULL DEFAULT 'default' COMMENT '积分池名称',
  `pool_code` varchar(50) NOT NULL DEFAULT 'default' COMMENT '积分池代码',
  `total_balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '积分池总余额',
  `total_deposit` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计沉淀金额',
  `total_withdraw` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计提取金额',
  `deposit_rate` decimal(5,4) NOT NULL DEFAULT '0.0500' COMMENT '沉淀比例(默认5%)',
  `status` enum('active','inactive') NOT NULL DEFAULT 'active' COMMENT '状态:active=激活,inactive=停用',
  `description` text COMMENT '积分池描述',
  `createtime` bigint(20) NOT NULL COMMENT '创建时间',
  `updatetime` bigint(20) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_pool_code` (`pool_code`),
  KEY `idx_status` (`status`),
  KEY `idx_createtime` (`createtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分池主表';

-- 积分池变动日志表
CREATE TABLE IF NOT EXISTS `sys_shopro_score_pool_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `pool_id` int(11) NOT NULL COMMENT '积分池ID',
  `order_id` int(11) NOT NULL DEFAULT '0' COMMENT '订单ID',
  `goods_id` int(11) NOT NULL DEFAULT '0' COMMENT '商品ID',
  `goods_title` varchar(255) DEFAULT NULL COMMENT '商品标题',
  `goods_num` int(11) NOT NULL DEFAULT '0' COMMENT '商品数量',
  `sales_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '商品销售金额',
  `deposit_rate` decimal(5,4) NOT NULL DEFAULT '0.0500' COMMENT '沉淀比例',
  `deposit_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '沉淀金额',
  `before_balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '变动前余额',
  `after_balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '变动后余额',
  `type` enum('deposit','withdraw','adjust') NOT NULL DEFAULT 'deposit' COMMENT '操作类型:deposit=沉淀,withdraw=提取,adjust=调整',
  `memo` varchar(500) DEFAULT NULL COMMENT '备注说明',
  `oper_type` varchar(20) DEFAULT 'system' COMMENT '操作人类型:system=系统,admin=管理员',
  `oper_id` int(11) NOT NULL DEFAULT '0' COMMENT '操作人ID',
  `createtime` bigint(20) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_pool_id` (`pool_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_type` (`type`),
  KEY `idx_createtime` (`createtime`),
  KEY `idx_pool_order` (`pool_id`, `order_id`),
  CONSTRAINT `fk_score_pool_log_pool` FOREIGN KEY (`pool_id`) REFERENCES `sys_shopro_score_pool` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分池变动日志表';

-- 插入默认积分池
INSERT INTO `sys_shopro_score_pool` (
  `pool_name`, 
  `pool_code`, 
  `total_balance`, 
  `total_deposit`, 
  `total_withdraw`, 
  `deposit_rate`, 
  `status`, 
  `description`, 
  `createtime`, 
  `updatetime`
) VALUES (
  '默认积分池', 
  'default', 
  0.00, 
  0.00, 
  0.00, 
  0.0500, 
  'active', 
  '系统默认积分池，用于收集奖金商品销售的5%沉淀', 
  UNIX_TIMESTAMP(), 
  UNIX_TIMESTAMP()
) ON DUPLICATE KEY UPDATE 
  `updatetime` = UNIX_TIMESTAMP();

-- 创建积分池统计视图
CREATE OR REPLACE VIEW `v_score_pool_stats` AS
SELECT 
  sp.id,
  sp.pool_name,
  sp.pool_code,
  sp.total_balance,
  sp.total_deposit,
  sp.total_withdraw,
  sp.deposit_rate,
  sp.status,
  COUNT(spl.id) as total_transactions,
  COUNT(DISTINCT spl.order_id) as total_orders,
  COUNT(DISTINCT spl.goods_id) as total_goods,
  SUM(spl.goods_num) as total_goods_count,
  DATE(FROM_UNIXTIME(sp.createtime)) as create_date,
  DATE(FROM_UNIXTIME(sp.updatetime)) as update_date
FROM `sys_shopro_score_pool` sp
LEFT JOIN `sys_shopro_score_pool_log` spl ON sp.id = spl.pool_id AND spl.type = 'deposit'
GROUP BY sp.id;
