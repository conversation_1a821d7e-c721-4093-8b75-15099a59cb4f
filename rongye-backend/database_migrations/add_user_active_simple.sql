-- 融业港用户激活状态管理数据库迁移脚本（简化版）

-- 1. 添加用户表字段
ALTER TABLE `sys_user` 
ADD COLUMN `active_start_time` bigint(20) DEFAULT 0 COMMENT '激活开始时间',
ADD COLUMN `last_purchase_time` bigint(20) DEFAULT 0 COMMENT '最后购买时间',
ADD COLUMN `last_direct_push_time` bigint(20) DEFAULT 0 COMMENT '最后直推时间',
ADD COLUMN `total_active_days` int(11) DEFAULT 0 COMMENT '累计激活天数',
ADD COLUMN `active_count` int(11) DEFAULT 0 COMMENT '激活次数';

-- 2. 创建用户激活状态日志表
CREATE TABLE `sys_user_active_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `action_type` enum('purchase','direct_push','expire','manual') NOT NULL COMMENT '激活动作类型',
  `trigger_order_id` int(11) DEFAULT 0 COMMENT '触发订单ID',
  `trigger_user_id` int(11) DEFAULT 0 COMMENT '触发用户ID',
  `before_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '变更前激活状态',
  `after_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '变更后激活状态',
  `before_start_time` bigint(20) DEFAULT 0 COMMENT '变更前激活开始时间',
  `after_start_time` bigint(20) DEFAULT 0 COMMENT '变更后激活开始时间',
  `before_end_time` bigint(20) DEFAULT 0 COMMENT '变更前激活结束时间',
  `after_end_time` bigint(20) DEFAULT 0 COMMENT '变更后激活结束时间',
  `active_days` int(11) DEFAULT 30 COMMENT '激活天数',
  `memo` varchar(500) DEFAULT NULL COMMENT '备注说明',
  `oper_type` varchar(20) DEFAULT 'system' COMMENT '操作类型',
  `oper_id` int(11) DEFAULT 0 COMMENT '操作人ID',
  `createtime` bigint(20) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action_type` (`action_type`),
  KEY `idx_trigger_order` (`trigger_order_id`),
  KEY `idx_createtime` (`createtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户激活状态变更日志表';

-- 3. 添加索引
ALTER TABLE `sys_user` ADD INDEX `idx_active_status` (`is_active`, `active_expire_time`);
ALTER TABLE `sys_user` ADD INDEX `idx_last_purchase` (`last_purchase_time`);

-- 4. 初始化现有用户数据
UPDATE `sys_user` 
SET `active_start_time` = UNIX_TIMESTAMP() - 86400 * 15
WHERE `is_active` = 1 AND `active_start_time` = 0 AND `active_expire_time` > 0;

SELECT '用户激活状态管理数据库迁移完成！' as message;
