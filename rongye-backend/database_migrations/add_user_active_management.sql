-- =====================================================
-- 融业港用户激活状态管理数据库迁移脚本
-- 版本: v1.0
-- 创建时间: 2025-01-08
-- 描述: 为用户表添加激活状态管理相关字段和表
-- =====================================================

-- 1. 修改用户表，添加激活管理相关字段
-- =====================================================

-- 检查并添加字段（使用存储过程方式避免重复添加）
DELIMITER $$

CREATE PROCEDURE AddColumnIfNotExists(
    IN tableName VARCHAR(100),
    IN columnName VARCHAR(100),
    IN columnDefinition TEXT
)
BEGIN
    DECLARE columnExists INT DEFAULT 0;

    SELECT COUNT(*) INTO columnExists
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = tableName
        AND COLUMN_NAME = columnName;

    IF columnExists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE ', tableName, ' ADD COLUMN ', columnName, ' ', columnDefinition);
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END IF;
END$$

DELIMITER ;

-- 添加激活管理相关字段
CALL AddColumnIfNotExists('sys_user', 'active_start_time', 'bigint(20) DEFAULT 0 COMMENT \'激活开始时间\' AFTER `active_expire_time`');
CALL AddColumnIfNotExists('sys_user', 'last_purchase_time', 'bigint(20) DEFAULT 0 COMMENT \'最后购买时间\' AFTER `active_start_time`');
CALL AddColumnIfNotExists('sys_user', 'last_direct_push_time', 'bigint(20) DEFAULT 0 COMMENT \'最后直推时间\' AFTER `last_purchase_time`');
CALL AddColumnIfNotExists('sys_user', 'total_active_days', 'int(11) DEFAULT 0 COMMENT \'累计激活天数\' AFTER `last_direct_push_time`');
CALL AddColumnIfNotExists('sys_user', 'active_count', 'int(11) DEFAULT 0 COMMENT \'激活次数\' AFTER `total_active_days`');

-- 删除临时存储过程
DROP PROCEDURE AddColumnIfNotExists;

-- 更新现有字段注释
ALTER TABLE `sys_user` 
MODIFY COLUMN `is_active` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否激活状态:0=未激活,1=已激活',
MODIFY COLUMN `active_expire_time` bigint(20) DEFAULT NULL COMMENT '激活过期时间';

-- 2. 创建用户激活状态日志表
-- =====================================================
CREATE TABLE IF NOT EXISTS `sys_user_active_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `action_type` enum('purchase','direct_push','expire','manual') NOT NULL COMMENT '激活动作类型:purchase=购买激活,direct_push=直推延续,expire=过期,manual=手动操作',
  `trigger_order_id` int(11) DEFAULT 0 COMMENT '触发订单ID',
  `trigger_user_id` int(11) DEFAULT 0 COMMENT '触发用户ID(直推时为下级用户)',
  `before_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '变更前激活状态',
  `after_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '变更后激活状态',
  `before_start_time` bigint(20) DEFAULT 0 COMMENT '变更前激活开始时间',
  `after_start_time` bigint(20) DEFAULT 0 COMMENT '变更后激活开始时间',
  `before_end_time` bigint(20) DEFAULT 0 COMMENT '变更前激活结束时间',
  `after_end_time` bigint(20) DEFAULT 0 COMMENT '变更后激活结束时间',
  `active_days` int(11) DEFAULT 30 COMMENT '激活天数',
  `memo` varchar(500) DEFAULT NULL COMMENT '备注说明',
  `oper_type` varchar(20) DEFAULT 'system' COMMENT '操作类型:system=系统,admin=管理员',
  `oper_id` int(11) DEFAULT 0 COMMENT '操作人ID',
  `createtime` bigint(20) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action_type` (`action_type`),
  KEY `idx_trigger_order` (`trigger_order_id`),
  KEY `idx_trigger_user` (`trigger_user_id`),
  KEY `idx_createtime` (`createtime`),
  KEY `idx_user_action_time` (`user_id`, `action_type`, `createtime`),
  CONSTRAINT `fk_user_active_log_user` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户激活状态变更日志表';

-- 3. 创建激活状态统计表（用于缓存统计数据）
-- =====================================================
CREATE TABLE IF NOT EXISTS `sys_user_active_stats` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `total_activations` int(11) DEFAULT 0 COMMENT '总激活次数',
  `total_active_days` int(11) DEFAULT 0 COMMENT '累计激活天数',
  `total_extensions` int(11) DEFAULT 0 COMMENT '总延续次数',
  `last_activation_time` bigint(20) DEFAULT 0 COMMENT '最后激活时间',
  `last_extension_time` bigint(20) DEFAULT 0 COMMENT '最后延续时间',
  `current_active_start` bigint(20) DEFAULT 0 COMMENT '当前激活开始时间',
  `current_active_end` bigint(20) DEFAULT 0 COMMENT '当前激活结束时间',
  `direct_push_count` int(11) DEFAULT 0 COMMENT '直推激活次数',
  `purchase_activation_count` int(11) DEFAULT 0 COMMENT '购买激活次数',
  `updatetime` bigint(20) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_last_activation` (`last_activation_time`),
  KEY `idx_current_active` (`current_active_start`, `current_active_end`),
  CONSTRAINT `fk_user_active_stats_user` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户激活状态统计表';

-- 4. 添加用户表的激活相关索引
-- =====================================================

-- 创建索引（忽略已存在的索引错误）
SET @sql = 'ALTER TABLE `sys_user` ADD INDEX `idx_active_status` (`is_active`, `active_expire_time`)';
SET @sql_error = 0;
DECLARE CONTINUE HANDLER FOR 1061 SET @sql_error = 1;
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SET @sql = 'ALTER TABLE `sys_user` ADD INDEX `idx_active_time_range` (`active_start_time`, `active_expire_time`)';
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SET @sql = 'ALTER TABLE `sys_user` ADD INDEX `idx_last_purchase` (`last_purchase_time`)';
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SET @sql = 'ALTER TABLE `sys_user` ADD INDEX `idx_last_direct_push` (`last_direct_push_time`)';
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- 5. 创建激活状态管理视图
-- =====================================================
CREATE OR REPLACE VIEW `v_user_active_status` AS
SELECT 
  u.id,
  u.username,
  u.nickname,
  u.is_active,
  u.active_start_time,
  u.active_expire_time,
  u.last_purchase_time,
  u.last_direct_push_time,
  u.total_active_days,
  u.active_count,
  CASE 
    WHEN u.is_active = 1 AND u.active_expire_time > UNIX_TIMESTAMP() THEN 'active'
    WHEN u.is_active = 1 AND u.active_expire_time <= UNIX_TIMESTAMP() THEN 'expired'
    ELSE 'inactive'
  END as status_desc,
  CASE 
    WHEN u.active_expire_time > UNIX_TIMESTAMP() THEN u.active_expire_time - UNIX_TIMESTAMP()
    ELSE 0
  END as remaining_seconds,
  CASE 
    WHEN u.active_expire_time > UNIX_TIMESTAMP() THEN CEIL((u.active_expire_time - UNIX_TIMESTAMP()) / 86400)
    ELSE 0
  END as remaining_days,
  FROM_UNIXTIME(u.active_start_time) as active_start_date,
  FROM_UNIXTIME(u.active_expire_time) as active_expire_date,
  FROM_UNIXTIME(u.last_purchase_time) as last_purchase_date,
  FROM_UNIXTIME(u.last_direct_push_time) as last_direct_push_date
FROM `sys_user` u
WHERE u.id > 0;

-- 6. 创建激活状态日报表视图
-- =====================================================
CREATE OR REPLACE VIEW `v_user_active_daily_report` AS
SELECT 
  DATE(FROM_UNIXTIME(ual.createtime)) as report_date,
  ual.action_type,
  COUNT(*) as action_count,
  COUNT(DISTINCT ual.user_id) as affected_users,
  COUNT(DISTINCT ual.trigger_order_id) as related_orders,
  AVG(ual.active_days) as avg_active_days
FROM `sys_user_active_log` ual
GROUP BY DATE(FROM_UNIXTIME(ual.createtime)), ual.action_type
ORDER BY report_date DESC, ual.action_type;

-- 7. 创建存储过程：批量检查激活状态过期
-- =====================================================
DELIMITER $$

CREATE PROCEDURE `sp_check_expired_active_users`()
BEGIN
  DECLARE done INT DEFAULT FALSE;
  DECLARE v_user_id INT;
  DECLARE v_current_time BIGINT DEFAULT UNIX_TIMESTAMP();
  
  -- 声明游标
  DECLARE expired_cursor CURSOR FOR 
    SELECT id FROM sys_user 
    WHERE is_active = 1 
      AND active_expire_time > 0 
      AND active_expire_time <= v_current_time;
  
  DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
  
  -- 开始事务
  START TRANSACTION;
  
  -- 打开游标
  OPEN expired_cursor;
  
  read_loop: LOOP
    FETCH expired_cursor INTO v_user_id;
    IF done THEN
      LEAVE read_loop;
    END IF;
    
    -- 更新用户激活状态
    UPDATE sys_user 
    SET is_active = 0,
        total_active_days = total_active_days + CEIL((active_expire_time - active_start_time) / 86400)
    WHERE id = v_user_id;
    
    -- 记录过期日志
    INSERT INTO sys_user_active_log (
      user_id, action_type, trigger_order_id, trigger_user_id,
      before_status, after_status, before_start_time, after_start_time,
      before_end_time, after_end_time, active_days, memo,
      oper_type, oper_id, createtime
    ) VALUES (
      v_user_id, 'expire', 0, 0,
      1, 0, 0, 0,
      0, 0, 0, '激活状态自动过期',
      'system', 0, v_current_time
    );
    
  END LOOP;
  
  -- 关闭游标
  CLOSE expired_cursor;
  
  -- 提交事务
  COMMIT;
  
  -- 返回处理结果
  SELECT ROW_COUNT() as expired_users_count, v_current_time as check_time;
  
END$$

DELIMITER ;

-- 8. 创建存储过程：获取用户激活统计
-- =====================================================
DELIMITER $$

CREATE PROCEDURE `sp_get_user_active_stats`(IN p_user_id INT)
BEGIN
  SELECT 
    u.id,
    u.username,
    u.nickname,
    u.is_active,
    u.active_start_time,
    u.active_expire_time,
    u.total_active_days,
    u.active_count,
    COALESCE(stats.total_activations, 0) as total_activations,
    COALESCE(stats.total_extensions, 0) as total_extensions,
    COALESCE(stats.direct_push_count, 0) as direct_push_count,
    COALESCE(stats.purchase_activation_count, 0) as purchase_activation_count,
    CASE 
      WHEN u.is_active = 1 AND u.active_expire_time > UNIX_TIMESTAMP() THEN 'active'
      WHEN u.is_active = 1 AND u.active_expire_time <= UNIX_TIMESTAMP() THEN 'expired'
      ELSE 'inactive'
    END as current_status
  FROM sys_user u
  LEFT JOIN sys_user_active_stats stats ON u.id = stats.user_id
  WHERE u.id = p_user_id;
END$$

DELIMITER ;

-- 9. 初始化现有用户的激活状态数据
-- =====================================================

-- 为现有激活用户设置激活开始时间（如果为空）
UPDATE sys_user 
SET active_start_time = UNIX_TIMESTAMP() - 86400 * 15  -- 假设激活了15天
WHERE is_active = 1 
  AND active_start_time = 0 
  AND active_expire_time > 0;

-- 为现有用户初始化激活统计数据
INSERT INTO sys_user_active_stats (
  user_id, total_activations, total_active_days, 
  current_active_start, current_active_end, updatetime
)
SELECT 
  id, 
  CASE WHEN is_active = 1 THEN 1 ELSE 0 END,
  CASE WHEN is_active = 1 THEN CEIL((UNIX_TIMESTAMP() - active_start_time) / 86400) ELSE 0 END,
  active_start_time,
  active_expire_time,
  UNIX_TIMESTAMP()
FROM sys_user 
WHERE id NOT IN (SELECT user_id FROM sys_user_active_stats);

-- 10. 数据完整性检查
-- =====================================================

-- 检查新增字段是否创建成功
SELECT 
  COLUMN_NAME,
  COLUMN_TYPE,
  COLUMN_DEFAULT,
  COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'sys_user' 
  AND COLUMN_NAME IN ('active_start_time', 'last_purchase_time', 'last_direct_push_time', 'total_active_days', 'active_count');

-- 检查新表是否创建成功
SELECT 
  TABLE_NAME,
  TABLE_COMMENT,
  TABLE_ROWS
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME IN ('sys_user_active_log', 'sys_user_active_stats');

-- 检查视图是否创建成功
SELECT 
  TABLE_NAME,
  TABLE_TYPE
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME LIKE 'v_user_active%';

-- =====================================================
-- 脚本执行完成
-- =====================================================

SELECT '用户激活状态管理数据库迁移完成！' as message,
       NOW() as execution_time,
       DATABASE() as database_name;
