<?php
/**
 * 简化的级差制度分润测试
 * 不依赖框架，只测试计算逻辑
 */

echo "=== 级差制度分润计算测试 ===\n";

/**
 * 模拟级差制度分润计算
 */
function calculateLevelDiffBonus($orderAmount, $buyerLevelRate, $parentLevels) {
    $currentRate = $buyerLevelRate;
    $totalBonus = 0;
    $bonusDetails = [];
    
    foreach ($parentLevels as $level) {
        $levelDiff = $level['rate'] - $currentRate;
        
        if ($levelDiff > 0) {
            $bonus = $orderAmount * $levelDiff;
            $totalBonus += $bonus;
            
            $bonusDetails[] = [
                'name' => $level['name'],
                'rate' => $level['rate'],
                'diff' => $levelDiff,
                'bonus' => $bonus
            ];
            
            $currentRate = $level['rate'];
        }
    }
    
    return [
        'total' => $totalBonus,
        'details' => $bonusDetails
    ];
}

// 测试场景1：单层级差
echo "\n--- 测试场景1：单层级差 ---\n";
$orderAmount = 1000.00;
$buyerRate = 0.20; // 创业销售商 20%
$parentLevels = [
    ['name' => '拓业顾问', 'rate' => 0.25] // 25%
];

$result = calculateLevelDiffBonus($orderAmount, $buyerRate, $parentLevels);

echo "订单金额: {$orderAmount}元\n";
echo "购买者等级: 创业销售商(" . ($buyerRate * 100) . "%)\n";
foreach ($result['details'] as $detail) {
    echo "{$detail['name']}: 级差" . ($detail['diff'] * 100) . "% = {$detail['bonus']}元\n";
}
echo "总分润: {$result['total']}元\n";
echo "结果: " . ($result['total'] == 50 ? "✓ 通过" : "✗ 失败") . "\n";

// 测试场景2：多层级差
echo "\n--- 测试场景2：多层级差 ---\n";
$buyerRate = 0.20; // 创业销售商 20%
$parentLevels = [
    ['name' => '拓业顾问', 'rate' => 0.25], // 25%
    ['name' => '守业经理', 'rate' => 0.30], // 30%
    ['name' => '享业董事', 'rate' => 0.35], // 35%
];

$result = calculateLevelDiffBonus($orderAmount, $buyerRate, $parentLevels);

echo "订单金额: {$orderAmount}元\n";
echo "购买者等级: 创业销售商(" . ($buyerRate * 100) . "%)\n";
foreach ($result['details'] as $detail) {
    echo "{$detail['name']}: 级差" . ($detail['diff'] * 100) . "% = {$detail['bonus']}元\n";
}
echo "总分润: {$result['total']}元 (" . ($result['total'] / $orderAmount * 100) . "%)\n";

// 验证总分润不超过最高等级与购买者的差额
$maxDiff = 0.35 - 0.20; // 15%
$maxBonus = $orderAmount * $maxDiff; // 150元
echo "最大可分润: {$maxBonus}元 (" . ($maxDiff * 100) . "%)\n";
echo "结果: " . ($result['total'] <= $maxBonus ? "✓ 通过" : "✗ 失败") . "\n";

// 测试场景3：相同等级不发放
echo "\n--- 测试场景3：相同等级不发放 ---\n";
$buyerRate = 0.25; // 拓业顾问 25%
$parentLevels = [
    ['name' => '拓业顾问', 'rate' => 0.25], // 25%
];

$result = calculateLevelDiffBonus($orderAmount, $buyerRate, $parentLevels);

echo "订单金额: {$orderAmount}元\n";
echo "购买者等级: 拓业顾问(" . ($buyerRate * 100) . "%)\n";
echo "上级等级: 拓业顾问(25%)\n";
echo "总分润: {$result['total']}元\n";
echo "结果: " . ($result['total'] == 0 ? "✓ 通过（相同等级不发放）" : "✗ 失败") . "\n";

// 测试场景4：跳级情况
echo "\n--- 测试场景4：跳级情况 ---\n";
$buyerRate = 0.20; // 创业销售商 20%
$parentLevels = [
    ['name' => '守业经理', 'rate' => 0.30], // 跳过拓业顾问，直接是守业经理 30%
    ['name' => '享业董事', 'rate' => 0.35], // 35%
];

$result = calculateLevelDiffBonus($orderAmount, $buyerRate, $parentLevels);

echo "订单金额: {$orderAmount}元\n";
echo "购买者等级: 创业销售商(" . ($buyerRate * 100) . "%)\n";
foreach ($result['details'] as $detail) {
    echo "{$detail['name']}: 级差" . ($detail['diff'] * 100) . "% = {$detail['bonus']}元\n";
}
echo "总分润: {$result['total']}元\n";
echo "结果: " . ($result['total'] == 150 ? "✓ 通过" : "✗ 失败") . "\n";

echo "\n=== 测试完成 ===\n";

// 对比旧制度和新制度的差异
echo "\n=== 新旧制度对比 ===\n";
echo "假设订单1000元，上级链：创业销售商 -> 拓业顾问 -> 守业经理 -> 享业董事\n";

echo "\n旧制度（单层奖励）：\n";
echo "- 拓业顾问获得: 1000 × 15% = 150元\n";
echo "- 守业经理获得: 1000 × 10% = 100元\n";
echo "- 享业董事获得: 1000 × 5% = 50元\n";
echo "- 总分润: 300元 (30%)\n";

echo "\n新制度（级差奖励）：\n";
echo "- 拓业顾问获得: 1000 × (25%-20%) = 50元\n";
echo "- 守业经理获得: 1000 × (30%-25%) = 50元\n";
echo "- 享业董事获得: 1000 × (35%-30%) = 50元\n";
echo "- 总分润: 150元 (15%)\n";

echo "\n优势：\n";
echo "1. 避免重复发放，总分润控制在合理范围内\n";
echo "2. 激励用户升级到更高等级\n";
echo "3. 上级只能获得与下级的差额，更加公平\n";
