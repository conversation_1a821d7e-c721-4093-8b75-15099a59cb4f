define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'shopro/tourism_spot/index' + location.search,
                    add_url: 'shopro/tourism_spot/add',
                    edit_url: 'shopro/tourism_spot/edit',
                    del_url: 'shopro/tourism_spot/del',
                    multi_url: 'shopro/tourism_spot/multi',
                    import_url: 'shopro/tourism_spot/import',
                    table: 'shopro_tourism_spot',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'weigh',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'name', title: __('Name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'address', title: __('Address'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'images', title: __('Images'), operate: false, events: Table.api.events.image, formatter: Table.api.formatter.images},
                        {field: 'price', title: __('Price'), operate:'BETWEEN'},
                        {field: 'open_time', title: __('Open_time'), operate: 'LIKE'},
                        {field: 'latitude', title: __('Latitude'), operate:'BETWEEN'},
                        {field: 'longitude', title: __('Longitude'), operate:'BETWEEN'},
                        {field: 'status', title: __('Status'), searchList: {"0":__('Status 0'),"1":__('Status 1')}, formatter: Table.api.formatter.status},
                        {field: 'weigh', title: __('Weigh'), operate: false},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate , buttons:[
                            // 报名列表
                            {
                                name: 'registration',
                                text: '报名列表',
                                title: '报名列表',
                                icon: 'fa fa-list',
                                classname : 'btn btn-xs btn-info btn-dialog',
                                url: function(row){
                                    return 'shopro/tourism_registration/index?spot_id=' + row.id;
                                }
                            }
                        ]}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
