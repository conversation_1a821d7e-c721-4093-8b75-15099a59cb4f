define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'shopro/tourism_registration/index' + location.search,
                    // add_url: 'shopro/tourism_registration/add',
                    edit_url: 'shopro/tourism_registration/edit',
                    del_url: 'shopro/tourism_registration/del',
                    multi_url: 'shopro/tourism_registration/multi',
                    import_url: 'shopro/tourism_registration/import',
                    table: 'shopro_tourism_registration',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'spot_id', title: __('Spot_id'),visible:false},
                        {field: 'user_id', title: __('User_id'),visible:false},
                        {field: 'name', title: __('Name'), operate: 'LIKE'},
                        {field: 'mobile', title: __('Mobile'), operate: 'LIKE'},
                        {field: 'id_card', title: __('Id_card'), operate: 'LIKE'},
                        {field: 'remark', title: __('Remark'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'status', title: __('Status'), searchList: {"0":__('Status 0'),"1":__('Status 1'),"2":__('Status 2')}, formatter: Table.api.formatter.status},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate , buttons:[
                            {
                                name: 'confirm',
                                text: '确认报名',
                                title: '确认报名',
                                icon: 'fa fa-check',
                                classname : 'btn btn-xs btn-success btn-ajax',
                                url: 'shopro/tourism_registration/confirm',
                                refresh: true,
                                visible: function (row) {
                                    return row.status == 1;
                                }
                            },
                            {
                                name: 'cancel',
                                text: '取消报名',
                                title: '取消报名',
                                icon: 'fa fa-times',
                                classname : 'btn btn-xs btn-danger btn-ajax',
                                url: 'shopro/tourism_registration/cancel',
                                refresh: true,
                                visible: function (row) {
                                    return row.status == 1 || row.status == 2;
                                }
                            }
                        ]}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
