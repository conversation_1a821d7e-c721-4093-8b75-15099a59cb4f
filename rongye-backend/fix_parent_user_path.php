<?php
/**
 * 修复用户的parent_user_path字段
 * 这个字段用于优化分润系统的层级查询
 */

// 引入ThinkPHP框架
define('APP_PATH', __DIR__ . '/application/');
require_once __DIR__ . '/thinkphp/start.php';

use think\Db;

try {
    echo "=== 开始修复用户parent_user_path字段 ===\n\n";
    
    // 开始事务
    Db::startTrans();
    
    // 获取所有用户
    $users = Db::name('user')->order('id', 'asc')->select();
    
    echo "找到 " . count($users) . " 个用户\n\n";
    
    // 为每个用户计算parent_user_path
    foreach ($users as $user) {
        $parentPath = calculateParentUserPath($user['id'], $user['parent_user_id']);
        
        // 更新用户的parent_user_path
        Db::name('user')
            ->where('id', $user['id'])
            ->update(['parent_user_path' => $parentPath]);
        
        echo "用户 {$user['id']} ({$user['username']}) - parent_user_path: '{$parentPath}'\n";
    }
    
    // 提交事务
    Db::commit();
    
    echo "\n=== parent_user_path修复完成 ===\n\n";
    
    // 验证修复结果
    echo "=== 验证修复结果 ===\n";
    $updatedUsers = Db::name('user')
        ->field('id, username, nickname, parent_user_id, parent_user_path')
        ->order('id', 'asc')
        ->select();
    
    foreach ($updatedUsers as $user) {
        echo "用户 {$user['id']} ({$user['username']}) - 上级: {$user['parent_user_id']} - 路径: '{$user['parent_user_path']}'\n";
    }
    
    echo "\n=== 显示用户层级关系 ===\n";
    displayUserHierarchy();
    
} catch (Exception $e) {
    if (isset($pdo)) {
        Db::rollback();
    }
    echo "错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
}

/**
 * 计算用户的parent_user_path
 * @param int $userId 用户ID
 * @param int $parentUserId 直接上级用户ID
 * @return string parent_user_path字符串
 */
function calculateParentUserPath($userId, $parentUserId) {
    if ($parentUserId == 0) {
        return '0'; // 顶级用户
    }
    
    $path = [];
    $currentUserId = $parentUserId;
    $visited = []; // 防止循环引用
    
    // 向上追溯到根用户
    while ($currentUserId > 0 && !in_array($currentUserId, $visited)) {
        $visited[] = $currentUserId;
        $path[] = $currentUserId;
        
        // 获取当前用户的上级
        $currentUser = Db::name('user')
            ->where('id', $currentUserId)
            ->field('parent_user_id')
            ->find();
        
        if (!$currentUser) {
            break;
        }
        
        $currentUserId = $currentUser['parent_user_id'];
        
        // 防止无限循环
        if (count($visited) > 20) {
            echo "警告: 用户 {$userId} 的上级关系可能存在循环引用\n";
            break;
        }
    }
    
    if (empty($path)) {
        return '0';
    }
    
    // parent_user_path格式：从根用户到直接上级，用逗号分隔
    // 例如：100001,100002,100003 表示 100001 -> 100002 -> 100003 -> 当前用户
    return implode(',', array_reverse($path));
}

/**
 * 显示用户层级关系
 */
function displayUserHierarchy() {
    // 获取所有用户
    $users = Db::name('user')
        ->field('id, username, nickname, parent_user_id, parent_user_path')
        ->order('id', 'asc')
        ->select();
    
    // 构建用户映射
    $userMap = [];
    foreach ($users as $user) {
        $userMap[$user['id']] = $user;
    }
    
    // 找到根用户（parent_user_id = 0）
    $rootUsers = array_filter($users, function($user) {
        return $user['parent_user_id'] == 0;
    });
    
    foreach ($rootUsers as $rootUser) {
        displayUserTree($rootUser['id'], $userMap, 0);
    }
}

/**
 * 递归显示用户树
 */
function displayUserTree($userId, $userMap, $level) {
    if (!isset($userMap[$userId])) {
        return;
    }
    
    $user = $userMap[$userId];
    $indent = str_repeat('  ', $level);
    $pathInfo = $user['parent_user_path'] !== '0' ? " [路径: {$user['parent_user_path']}]" : " [根用户]";
    
    echo "{$indent}├─ {$user['nickname']} ({$user['username']}){$pathInfo}\n";
    
    // 找到所有子用户
    $children = array_filter($userMap, function($u) use ($userId) {
        return $u['parent_user_id'] == $userId;
    });
    
    foreach ($children as $child) {
        displayUserTree($child['id'], $userMap, $level + 1);
    }
}

/**
 * 验证parent_user_path的正确性
 */
function validateParentUserPath() {
    echo "\n=== 验证parent_user_path正确性 ===\n";
    
    $users = Db::name('user')->select();
    $errors = [];
    
    foreach ($users as $user) {
        if ($user['parent_user_path'] === '0') {
            // 根用户，应该parent_user_id也是0
            if ($user['parent_user_id'] != 0) {
                $errors[] = "用户 {$user['id']} parent_user_path是0但parent_user_id不是0";
            }
            continue;
        }
        
        $pathIds = explode(',', $user['parent_user_path']);
        $directParentId = end($pathIds);
        
        if ($directParentId != $user['parent_user_id']) {
            $errors[] = "用户 {$user['id']} parent_user_path最后一个ID({$directParentId})与parent_user_id({$user['parent_user_id']})不匹配";
        }
    }
    
    if (empty($errors)) {
        echo "✅ 所有用户的parent_user_path都正确\n";
    } else {
        echo "❌ 发现以下错误:\n";
        foreach ($errors as $error) {
            echo "  - {$error}\n";
        }
    }
}
