<?php
/**
 * 分润系统与积分池集成测试
 * 测试完整的订单分润流程，包括积分池沉淀
 */

// 定义应用目录
define('APP_PATH', __DIR__ . '/../application/');
// 定义根目录
define('ROOT_PATH', __DIR__ . '/../');
// 定义目录分隔符
define('DS', DIRECTORY_SEPARATOR);

// 加载框架引导文件
require __DIR__ . '/../thinkphp/start.php';

use addons\shopro\library\Bonus;
use addons\shopro\library\ScorePool;
use think\Db;
use think\Log;

class BonusScorePoolIntegrationTest
{
    /**
     * 运行集成测试
     */
    public static function runTest()
    {
        echo "=== 分润系统与积分池集成测试 ===\n";
        
        try {
            // 1. 准备测试数据
            $test_data = self::prepareTestData();
            
            // 2. 执行完整的分润处理
            self::testFullBonusProcess($test_data);
            
            // 3. 验证结果
            self::verifyResults($test_data);
            
            echo "\n=== 集成测试完成 ===\n";
            
        } catch (Exception $e) {
            echo "测试异常: " . $e->getMessage() . "\n";
            echo "堆栈跟踪: " . $e->getTraceAsString() . "\n";
        }
    }

    /**
     * 准备测试数据
     */
    private static function prepareTestData()
    {
        echo "\n--- 准备测试数据 ---\n";
        
        // 查找一个包含奖金商品的已支付订单
        $order = Db::name('shopro_order o')
            ->join('shopro_order_item oi', 'o.id = oi.order_id')
            ->join('shopro_goods g', 'oi.goods_id = g.id')
            ->where('o.status', 'paid')
            ->where('g.is_bonus', 1)
            ->where('o.id', '>', 100)
            ->field('o.*')
            ->order('o.id', 'desc')
            ->find();
            
        if (!$order) {
            throw new Exception("没有找到合适的测试订单");
        }
        
        echo "使用测试订单: {$order['id']}\n";
        echo "订单金额: {$order['pay_fee']}\n";
        echo "用户ID: {$order['user_id']}\n";
        
        // 获取订单中的奖金商品
        $bonus_items = Db::name('shopro_order_item oi')
            ->join('shopro_goods g', 'oi.goods_id = g.id')
            ->where('oi.order_id', $order['id'])
            ->where('g.is_bonus', 1)
            ->field('oi.*, g.title as goods_title, g.is_bonus')
            ->select();
            
        echo "奖金商品数量: " . count($bonus_items) . "\n";
        
        $total_bonus_amount = 0;
        foreach ($bonus_items as $item) {
            echo "  - {$item['goods_title']}: {$item['pay_fee']}元 x {$item['goods_num']}\n";
            $total_bonus_amount += $item['pay_fee'];
        }
        
        echo "奖金商品总金额: {$total_bonus_amount}元\n";
        
        return [
            'order' => $order,
            'bonus_items' => $bonus_items,
            'total_bonus_amount' => $total_bonus_amount
        ];
    }

    /**
     * 测试完整的分润处理流程
     */
    private static function testFullBonusProcess($test_data)
    {
        echo "\n--- 测试完整分润处理流程 ---\n";
        
        $order = $test_data['order'];
        $order_id = $order['id'];
        
        // 记录处理前的状态
        $before_state = self::recordSystemState($order_id);
        
        echo "处理前状态:\n";
        echo "  - 积分池余额: {$before_state['pool_balance']}\n";
        echo "  - 分润日志数: {$before_state['bonus_logs_count']}\n";
        echo "  - 积分池日志数: {$before_state['pool_logs_count']}\n";
        
        // 执行分润处理（在事务中，最后回滚）
        try {
            Db::startTrans();
            
            echo "\n开始执行分润处理...\n";
            $result = Bonus::processOrderBonus($order_id);
            
            if ($result) {
                echo "✓ 分润处理成功\n";
                
                // 记录处理后的状态
                $after_state = self::recordSystemState($order_id);
                
                echo "\n处理后状态:\n";
                echo "  - 积分池余额: {$after_state['pool_balance']}\n";
                echo "  - 分润日志数: {$after_state['bonus_logs_count']}\n";
                echo "  - 积分池日志数: {$after_state['pool_logs_count']}\n";
                
                // 计算变化
                $pool_deposit = $after_state['pool_balance'] - $before_state['pool_balance'];
                $bonus_logs_added = $after_state['bonus_logs_count'] - $before_state['bonus_logs_count'];
                $pool_logs_added = $after_state['pool_logs_count'] - $before_state['pool_logs_count'];
                
                echo "\n变化统计:\n";
                echo "  - 积分池沉淀: {$pool_deposit}元\n";
                echo "  - 新增分润日志: {$bonus_logs_added}条\n";
                echo "  - 新增积分池日志: {$pool_logs_added}条\n";
                
                // 验证积分池沉淀金额
                $expected_deposit = $test_data['total_bonus_amount'] * 0.05; // 5%
                echo "  - 预期积分池沉淀: {$expected_deposit}元\n";
                
                if (abs($pool_deposit - $expected_deposit) < 0.01) {
                    echo "✓ 积分池沉淀金额正确\n";
                } else {
                    echo "✗ 积分池沉淀金额错误\n";
                }
                
            } else {
                echo "✗ 分润处理失败\n";
            }
            
            // 回滚事务（测试不影响实际数据）
            Db::rollback();
            echo "\n测试事务已回滚，不影响实际数据\n";
            
        } catch (Exception $e) {
            Db::rollback();
            throw new Exception("分润处理异常: " . $e->getMessage());
        }
    }

    /**
     * 记录系统状态
     */
    private static function recordSystemState($order_id)
    {
        return [
            'pool_balance' => ScorePool::getPoolBalance(),
            'bonus_logs_count' => Db::name('shopro_bonus_log')->count(),
            'pool_logs_count' => Db::name('shopro_score_pool_log')->count(),
            'order_bonus_logs' => Db::name('shopro_bonus_log')->where('order_id', $order_id)->count(),
            'order_pool_logs' => Db::name('shopro_score_pool_log')->where('order_id', $order_id)->count()
        ];
    }

    /**
     * 验证测试结果
     */
    private static function verifyResults($test_data)
    {
        echo "\n--- 验证测试结果 ---\n";
        
        // 验证积分池数据一致性
        $validation = ScorePool::validatePoolData();
        if ($validation['valid']) {
            echo "✓ 积分池数据一致性验证通过\n";
        } else {
            echo "✗ 积分池数据一致性验证失败\n";
            print_r($validation);
        }
        
        // 验证积分池配置
        $pool = ScorePool::getPool();
        if ($pool && $pool['deposit_rate'] == 0.05) {
            echo "✓ 积分池沉淀比例配置正确 (5%)\n";
        } else {
            echo "✗ 积分池沉淀比例配置错误\n";
        }
        
        // 验证积分池统计
        $stats = ScorePool::getPoolStats();
        if (!empty($stats)) {
            echo "✓ 积分池统计功能正常\n";
            echo "  - 总交易数: " . ($stats['total_transactions'] ?? 0) . "\n";
            echo "  - 总订单数: " . ($stats['total_orders'] ?? 0) . "\n";
        }
        
        echo "\n所有验证完成\n";
    }

    /**
     * 测试异常情况处理
     */
    private static function testExceptionHandling()
    {
        echo "\n--- 测试异常情况处理 ---\n";
        
        // 测试不存在的订单
        $result = ScorePool::processDeposit(999999999, []);
        if (!$result) {
            echo "✓ 正确处理了空商品列表\n";
        }
        
        // 测试无效的商品数据
        $invalid_items = [
            [
                'goods_id' => 0,
                'goods_title' => '',
                'goods_num' => 0,
                'pay_fee' => 0
            ]
        ];
        
        $result = ScorePool::processDeposit(999999998, $invalid_items);
        if ($result) {
            echo "✓ 正确处理了无效商品数据\n";
        }
    }
}

// 执行测试
if (php_sapi_name() === 'cli') {
    BonusScorePoolIntegrationTest::runTest();
} else {
    echo "请在命令行环境下运行此测试脚本\n";
}
