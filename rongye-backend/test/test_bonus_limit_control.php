<?php
/**
 * 收益上限控制机制测试脚本
 */

// 定义应用目录
define('APP_PATH', dirname(__DIR__) . '/application/');
// 定义根目录
define('ROOT_PATH', dirname(__DIR__) . '/');
// 定义目录分隔符
define('DS', DIRECTORY_SEPARATOR);

// 加载框架引导文件
require dirname(__DIR__) . '/thinkphp/start.php';

use addons\shopro\library\BonusLimitManager;
use addons\shopro\library\UserActiveManager;
use addons\shopro\library\Bonus;
use think\Db;

echo "=== 收益上限控制机制测试 ===\n";

try {
    // 1. 测试收益上限管理基础功能
    echo "\n1. 测试收益上限管理基础功能\n";
    
    $test_user_id = 100001;
    
    // 获取用户当前状态
    $status = BonusLimitManager::getUserLimitStatus($test_user_id);
    if ($status) {
        echo "✓ 用户收益上限状态获取成功\n";
        echo "  - 用户ID: {$status['user_id']}\n";
        echo "  - 用户名: {$status['username']}\n";
        echo "  - 累计购买金额: {$status['total_purchase_amount']}元\n";
        echo "  - 累计收益金额: {$status['total_bonus_received']}元\n";
        echo "  - 收益上限: {$status['bonus_limit']}元\n";
        echo "  - 收益倍数: {$status['bonus_ratio']}倍\n";
        echo "  - 剩余上限: {$status['remaining_limit']}元\n";
        echo "  - 状态描述: {$status['status_desc']}\n";
        echo "  - 可获得分润: " . ($status['can_receive_bonus'] ? '是' : '否') . "\n";
        echo "  - 是否达到上限: " . ($status['bonus_limit_reached'] ? '是' : '否') . "\n";
    } else {
        echo "✗ 用户收益上限状态获取失败\n";
    }

    // 2. 测试收益上限统计
    echo "\n2. 测试收益上限统计\n";
    
    $stats = BonusLimitManager::getLimitStats();
    if (!empty($stats)) {
        echo "✓ 收益上限统计获取成功\n";
        echo "  - 总用户数: " . ($stats['total_users'] ?? 0) . "\n";
        echo "  - 有购买记录用户数: " . ($stats['users_with_purchase'] ?? 0) . "\n";
        echo "  - 达到上限用户数: " . ($stats['users_limit_reached'] ?? 0) . "\n";
        echo "  - 接近上限用户数: " . ($stats['users_near_limit'] ?? 0) . "\n";
        echo "  - 总购买金额: " . ($stats['total_purchase_amount'] ?? 0) . "元\n";
        echo "  - 总收益金额: " . ($stats['total_bonus_received'] ?? 0) . "元\n";
        echo "  - 总收益上限: " . ($stats['total_bonus_limit'] ?? 0) . "元\n";
        echo "  - 整体收益倍数: " . ($stats['overall_bonus_ratio'] ?? 0) . "倍\n";
        echo "  - 剩余总上限: " . ($stats['total_remaining_limit'] ?? 0) . "元\n";
    } else {
        echo "✗ 收益上限统计获取失败\n";
    }

    // 3. 测试购买金额记录
    echo "\n3. 测试购买金额记录\n";
    
    $test_order_id = 999;
    $test_amount = 100.00;
    
    Db::startTrans();
    try {
        // 记录购买前状态
        $before_status = BonusLimitManager::getUserLimitStatus($test_user_id);
        echo "记录前购买金额: {$before_status['total_purchase_amount']}元\n";
        
        // 记录购买金额
        $result = BonusLimitManager::recordPurchaseAmount($test_user_id, $test_amount, $test_order_id);
        
        if ($result) {
            echo "✓ 购买金额记录成功\n";
            
            // 记录购买后状态
            $after_status = BonusLimitManager::getUserLimitStatus($test_user_id);
            echo "记录后购买金额: {$after_status['total_purchase_amount']}元\n";
            echo "购买金额增加: " . ($after_status['total_purchase_amount'] - $before_status['total_purchase_amount']) . "元\n";
            echo "收益上限更新: {$after_status['bonus_limit']}元\n";
            
        } else {
            echo "✗ 购买金额记录失败\n";
        }
        
        Db::rollback();
        echo "测试事务已回滚\n";
        
    } catch (Exception $e) {
        Db::rollback();
        echo "✗ 购买金额记录测试异常: " . $e->getMessage() . "\n";
    }

    // 4. 测试收益金额记录
    echo "\n4. 测试收益金额记录\n";
    
    $test_bonus_amount = 50.00;
    
    Db::startTrans();
    try {
        // 记录收益前状态
        $before_status = BonusLimitManager::getUserLimitStatus($test_user_id);
        echo "记录前收益金额: {$before_status['total_bonus_received']}元\n";
        echo "记录前上限状态: " . ($before_status['bonus_limit_reached'] ? '已达到' : '未达到') . "\n";
        
        // 记录收益金额
        $result = BonusLimitManager::recordBonusAmount($test_user_id, $test_bonus_amount, $test_order_id, 'test_bonus');
        
        if ($result) {
            echo "✓ 收益金额记录成功\n";
            
            // 记录收益后状态
            $after_status = BonusLimitManager::getUserLimitStatus($test_user_id);
            echo "记录后收益金额: {$after_status['total_bonus_received']}元\n";
            echo "收益金额增加: " . ($after_status['total_bonus_received'] - $before_status['total_bonus_received']) . "元\n";
            echo "记录后上限状态: " . ($after_status['bonus_limit_reached'] ? '已达到' : '未达到') . "\n";
            echo "当前收益倍数: {$after_status['bonus_ratio']}倍\n";
            
        } else {
            echo "✗ 收益金额记录失败\n";
        }
        
        Db::rollback();
        echo "测试事务已回滚\n";
        
    } catch (Exception $e) {
        Db::rollback();
        echo "✗ 收益金额记录测试异常: " . $e->getMessage() . "\n";
    }

    // 5. 测试收益上限检查
    echo "\n5. 测试收益上限检查\n";
    
    // 测试正常用户
    $can_receive = BonusLimitManager::canReceiveBonus($test_user_id);
    echo "用户{$test_user_id}可以获得分润: " . ($can_receive ? '是' : '否') . "\n";
    
    // 测试批量检查
    $test_user_ids = [100001, 100002, 100003];
    $batch_result = BonusLimitManager::batchCheckLimitStatus($test_user_ids);
    echo "批量检查结果:\n";
    foreach ($batch_result as $uid => $can_receive) {
        echo "  - 用户{$uid}: " . ($can_receive ? '可以获得分润' : '不能获得分润') . "\n";
    }

    // 6. 测试与激活状态的集成
    echo "\n6. 测试与激活状态的集成\n";
    
    // 测试激活状态检查（现在包含收益上限检查）
    $is_active = UserActiveManager::isUserActive($test_user_id);
    echo "用户{$test_user_id}激活状态（含收益上限检查）: " . ($is_active ? '已激活' : '未激活') . "\n";
    
    // 获取详细状态
    $active_status = UserActiveManager::getUserActiveStatus($test_user_id);
    $limit_status = BonusLimitManager::getUserLimitStatus($test_user_id);
    
    echo "激活状态详情:\n";
    echo "  - 分润激活: " . ($active_status['is_active'] ? '是' : '否') . "\n";
    echo "  - 激活剩余天数: {$active_status['remaining_days']}天\n";
    echo "  - 收益上限检查: " . ($limit_status['can_receive_bonus'] ? '通过' : '不通过') . "\n";
    echo "  - 综合可分润: " . ($is_active ? '是' : '否') . "\n";

    // 7. 测试收益上限日志
    echo "\n7. 测试收益上限日志\n";
    
    $logs = BonusLimitManager::getUserLimitLogs($test_user_id, 5);
    echo "用户{$test_user_id}的收益上限日志数量: " . count($logs) . "\n";
    
    if (!empty($logs)) {
        echo "最近的收益上限日志:\n";
        foreach (array_slice($logs, 0, 3) as $log) {
            echo "  - [{$log['createtime_format']}] {$log['action_type']}: {$log['memo']}\n";
            echo "    购买金额: {$log['before_purchase_amount']} -> {$log['after_purchase_amount']}\n";
            echo "    收益金额: {$log['before_bonus_amount']} -> {$log['after_bonus_amount']}\n";
        }
    } else {
        echo "暂无收益上限日志记录\n";
    }

    // 8. 测试完整的分润流程（包含收益上限控制）
    echo "\n8. 测试完整的分润流程（包含收益上限控制）\n";
    
    $test_order_id = 108;
    
    Db::startTrans();
    try {
        // 获取分润前状态
        $buyer_limit_before = BonusLimitManager::getUserLimitStatus(100001);
        $parent_limit_before = BonusLimitManager::getUserLimitStatus(100002);
        
        echo "分润前状态:\n";
        echo "  - 购买用户收益: {$buyer_limit_before['total_bonus_received']}元，可分润: " . ($buyer_limit_before['can_receive_bonus'] ? '是' : '否') . "\n";
        echo "  - 上级用户收益: {$parent_limit_before['total_bonus_received']}元，可分润: " . ($parent_limit_before['can_receive_bonus'] ? '是' : '否') . "\n";
        
        // 清除之前的分润记录
        Db::name('shopro_bonus_log')->where('order_id', $test_order_id)->delete();
        
        // 执行分润处理
        $result = Bonus::processOrderBonus($test_order_id);
        
        if ($result) {
            echo "✓ 分润处理成功\n";
            
            // 获取分润后状态
            $buyer_limit_after = BonusLimitManager::getUserLimitStatus(100001);
            $parent_limit_after = BonusLimitManager::getUserLimitStatus(100002);
            
            echo "分润后状态:\n";
            echo "  - 购买用户收益: {$buyer_limit_after['total_bonus_received']}元，可分润: " . ($buyer_limit_after['can_receive_bonus'] ? '是' : '否') . "\n";
            echo "  - 上级用户收益: {$parent_limit_after['total_bonus_received']}元，可分润: " . ($parent_limit_after['can_receive_bonus'] ? '是' : '否') . "\n";
            
            $buyer_bonus_change = $buyer_limit_after['total_bonus_received'] - $buyer_limit_before['total_bonus_received'];
            $parent_bonus_change = $parent_limit_after['total_bonus_received'] - $parent_limit_before['total_bonus_received'];
            
            echo "收益变化:\n";
            echo "  - 购买用户收益增加: {$buyer_bonus_change}元\n";
            echo "  - 上级用户收益增加: {$parent_bonus_change}元\n";
            
            // 检查分润日志
            $bonus_logs = Db::name('shopro_bonus_log')
                ->where('order_id', $test_order_id)
                ->select();
                
            echo "分润日志数量: " . count($bonus_logs) . "\n";
            
            // 检查收益上限日志
            $limit_logs_buyer = BonusLimitManager::getUserLimitLogs(100001, 3);
            $limit_logs_parent = BonusLimitManager::getUserLimitLogs(100002, 3);
            
            echo "收益上限日志:\n";
            echo "  - 购买用户新增日志: " . count($limit_logs_buyer) . "条\n";
            echo "  - 上级用户新增日志: " . count($limit_logs_parent) . "条\n";
            
        } else {
            echo "✗ 分润处理失败\n";
        }
        
        Db::rollback();
        echo "测试事务已回滚\n";
        
    } catch (Exception $e) {
        Db::rollback();
        echo "✗ 完整分润流程测试异常: " . $e->getMessage() . "\n";
    }

    // 9. 测试缓存机制
    echo "\n9. 测试缓存机制\n";
    
    $start_time = microtime(true);
    
    // 第一次查询（无缓存）
    $can_receive1 = BonusLimitManager::canReceiveBonus($test_user_id, false);
    $time1 = microtime(true) - $start_time;
    
    // 第二次查询（有缓存）
    $start_time2 = microtime(true);
    $can_receive2 = BonusLimitManager::canReceiveBonus($test_user_id, true);
    $time2 = microtime(true) - $start_time2;
    
    echo "收益上限检查性能:\n";
    echo "  - 第一次查询: " . number_format($time1 * 1000, 2) . "ms\n";
    echo "  - 第二次查询: " . number_format($time2 * 1000, 2) . "ms\n";
    echo "  - 缓存效果: " . ($time2 < $time1 ? '有效' : '无效') . "\n";
    
    // 清除缓存测试
    BonusLimitManager::clearLimitCache($test_user_id);
    echo "✓ 缓存清除测试完成\n";

    echo "\n=== 收益上限控制机制测试完成 ===\n";

} catch (Exception $e) {
    echo "测试异常: " . $e->getMessage() . "\n";
    echo "堆栈跟踪: " . $e->getTraceAsString() . "\n";
}
