<?php
/**
 * 用户激活状态管理测试脚本
 */

// 定义应用目录
define('APP_PATH', dirname(__DIR__) . '/application/');
// 定义根目录
define('ROOT_PATH', dirname(__DIR__) . '/');
// 定义目录分隔符
define('DS', DIRECTORY_SEPARATOR);

// 加载框架引导文件
require dirname(__DIR__) . '/thinkphp/start.php';

use addons\shopro\library\UserActiveManager;
use addons\shopro\library\Bonus;
use think\Db;

echo "=== 用户激活状态管理测试 ===\n";

try {
    // 1. 测试用户激活状态检查
    echo "\n1. 测试用户激活状态检查\n";
    
    $test_user_id = 100001; // 使用现有用户
    $status = UserActiveManager::getUserActiveStatus($test_user_id);
    
    if (!empty($status)) {
        echo "✓ 用户激活状态获取成功\n";
        echo "  - 用户ID: {$status['user_id']}\n";
        echo "  - 用户名: {$status['username']}\n";
        echo "  - 昵称: {$status['nickname']}\n";
        echo "  - 激活状态: " . ($status['is_active'] ? '已激活' : '未激活') . "\n";
        echo "  - 状态描述: {$status['status_desc']}\n";
        echo "  - 剩余天数: {$status['remaining_days']}天\n";
        echo "  - 累计激活天数: {$status['total_active_days']}天\n";
        echo "  - 激活次数: {$status['active_count']}次\n";
    } else {
        echo "✗ 用户激活状态获取失败\n";
    }

    // 2. 测试激活状态统计
    echo "\n2. 测试激活状态统计\n";
    
    $stats = UserActiveManager::getActiveStats();
    if (!empty($stats)) {
        echo "✓ 激活状态统计获取成功\n";
        echo "  - 总用户数: " . ($stats['total_users'] ?? 0) . "\n";
        echo "  - 激活用户数: " . ($stats['active_users'] ?? 0) . "\n";
        echo "  - 过期用户数: " . ($stats['expired_users'] ?? 0) . "\n";
        echo "  - 未激活用户数: " . ($stats['inactive_users'] ?? 0) . "\n";
        echo "  - 今日激活数: " . ($stats['today_activations'] ?? 0) . "\n";
        echo "  - 今日延续数: " . ($stats['today_extensions'] ?? 0) . "\n";
        echo "  - 今日过期数: " . ($stats['today_expires'] ?? 0) . "\n";
    } else {
        echo "✗ 激活状态统计获取失败\n";
    }

    // 3. 测试手动激活用户
    echo "\n3. 测试手动激活用户\n";
    
    $test_inactive_user_id = 100002; // 使用另一个用户进行测试
    
    // 获取激活前状态
    $before_status = UserActiveManager::getUserActiveStatus($test_inactive_user_id);
    echo "激活前状态: " . ($before_status['is_active'] ? '已激活' : '未激活') . "\n";
    
    // 在事务中测试手动激活
    Db::startTrans();
    try {
        $result = UserActiveManager::manualSetActiveStatus($test_inactive_user_id, true, 30, '测试手动激活', 0);
        
        if ($result) {
            echo "✓ 手动激活用户成功\n";
            
            // 获取激活后状态
            $after_status = UserActiveManager::getUserActiveStatus($test_inactive_user_id);
            echo "激活后状态: " . ($after_status['is_active'] ? '已激活' : '未激活') . "\n";
            echo "激活结束时间: " . date('Y-m-d H:i:s', $after_status['active_expire_time']) . "\n";
            
        } else {
            echo "✗ 手动激活用户失败\n";
        }
        
        // 回滚测试事务
        Db::rollback();
        echo "测试事务已回滚\n";
        
    } catch (Exception $e) {
        Db::rollback();
        echo "✗ 手动激活测试异常: " . $e->getMessage() . "\n";
    }

    // 4. 测试激活日志查询
    echo "\n4. 测试激活日志查询\n";
    
    $logs = UserActiveManager::getUserActiveLogs($test_user_id, 5);
    echo "用户{$test_user_id}的激活日志数量: " . count($logs) . "\n";
    
    if (!empty($logs)) {
        echo "最近的激活日志:\n";
        foreach (array_slice($logs, 0, 3) as $log) {
            echo "  - [{$log['createtime_format']}] {$log['action_type']}: {$log['memo']}\n";
        }
    } else {
        echo "暂无激活日志记录\n";
    }

    // 5. 测试批量检查激活状态
    echo "\n5. 测试批量检查激活状态\n";
    
    $test_user_ids = [100001, 100002, 100003];
    $batch_status = UserActiveManager::batchCheckActiveStatus($test_user_ids);
    
    echo "批量检查结果:\n";
    foreach ($batch_status as $user_id => $is_active) {
        echo "  - 用户{$user_id}: " . ($is_active ? '已激活' : '未激活') . "\n";
    }

    // 6. 测试过期用户检查
    echo "\n6. 测试过期用户检查\n";
    
    $expired_count = UserActiveManager::checkExpiredUsers();
    echo "检查到过期用户数量: {$expired_count}\n";

    // 7. 测试与分润系统的集成
    echo "\n7. 测试与分润系统的集成\n";
    
    // 查找一个包含奖金商品的订单
    $order = Db::name('shopro_order o')
        ->join('shopro_order_item oi', 'o.id = oi.order_id')
        ->join('shopro_goods g', 'oi.goods_id = g.id')
        ->where('o.status', 'paid')
        ->where('g.is_bonus', 1)
        ->where('o.id', '>', 100)
        ->field('o.*')
        ->order('o.id', 'desc')
        ->find();
        
    if ($order) {
        echo "找到测试订单: {$order['id']}\n";
        echo "购买用户: {$order['user_id']}\n";
        
        // 获取购买用户的激活状态
        $buyer_status = UserActiveManager::getUserActiveStatus($order['user_id']);
        echo "购买用户激活状态: " . ($buyer_status['is_active'] ? '已激活' : '未激活') . "\n";
        
        // 获取上级用户信息
        $buyer = Db::name('user')->where('id', $order['user_id'])->find();
        if ($buyer && $buyer['parent_user_id'] > 0) {
            $parent_status = UserActiveManager::getUserActiveStatus($buyer['parent_user_id']);
            echo "上级用户{$buyer['parent_user_id']}激活状态: " . ($parent_status['is_active'] ? '已激活' : '未激活') . "\n";
        }
        
        // 在事务中测试完整的分润处理（包含激活状态管理）
        Db::startTrans();
        try {
            echo "开始测试分润处理（包含激活状态管理）...\n";
            
            $result = Bonus::processOrderBonus($order['id']);
            
            if ($result) {
                echo "✓ 分润处理成功（包含激活状态管理）\n";
                
                // 检查激活状态变化
                $buyer_status_after = UserActiveManager::getUserActiveStatus($order['user_id']);
                echo "处理后购买用户激活状态: " . ($buyer_status_after['is_active'] ? '已激活' : '未激活') . "\n";
                
                if ($buyer && $buyer['parent_user_id'] > 0) {
                    $parent_status_after = UserActiveManager::getUserActiveStatus($buyer['parent_user_id']);
                    echo "处理后上级用户激活状态: " . ($parent_status_after['is_active'] ? '已激活' : '未激活') . "\n";
                }
                
                // 检查激活日志
                $new_logs = UserActiveManager::getUserActiveLogs($order['user_id'], 3);
                echo "新增激活日志数量: " . count($new_logs) . "\n";
                
            } else {
                echo "✗ 分润处理失败\n";
            }
            
            // 回滚测试事务
            Db::rollback();
            echo "测试事务已回滚\n";
            
        } catch (Exception $e) {
            Db::rollback();
            echo "✗ 分润处理测试异常: " . $e->getMessage() . "\n";
        }
        
    } else {
        echo "没有找到合适的测试订单\n";
    }

    echo "\n=== 测试完成 ===\n";

} catch (Exception $e) {
    echo "测试异常: " . $e->getMessage() . "\n";
    echo "堆栈跟踪: " . $e->getTraceAsString() . "\n";
}
