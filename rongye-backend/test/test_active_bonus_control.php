<?php
/**
 * 激活状态对分润影响的专项测试
 */

// 定义应用目录
define('APP_PATH', dirname(__DIR__) . '/application/');
// 定义根目录
define('ROOT_PATH', dirname(__DIR__) . '/');
// 定义目录分隔符
define('DS', DIRECTORY_SEPARATOR);

// 加载框架引导文件
require dirname(__DIR__) . '/thinkphp/start.php';

use addons\shopro\library\UserActiveManager;
use addons\shopro\library\Bonus;
use think\Db;

echo "=== 激活状态对分润影响的专项测试 ===\n";

try {
    // 1. 准备测试数据
    echo "\n1. 准备测试数据\n";
    
    $test_order_id = 108;
    $buyer_user_id = 100001;
    $parent_user_id = 100002;
    
    // 获取测试用户信息
    $buyer = Db::name('user')->where('id', $buyer_user_id)->find();
    $parent = Db::name('user')->where('id', $parent_user_id)->find();
    
    if (!$buyer || !$parent) {
        echo "✗ 测试用户不存在\n";
        exit;
    }
    
    echo "购买用户: {$buyer['username']} (ID: {$buyer_user_id})\n";
    echo "上级用户: {$parent['username']} (ID: {$parent_user_id})\n";
    
    // 2. 测试场景1：上级用户未激活时的分润情况
    echo "\n2. 测试场景1：上级用户未激活时的分润情况\n";
    
    Db::startTrans();
    try {
        // 确保上级用户未激活
        UserActiveManager::manualSetActiveStatus($parent_user_id, false, 0, '测试：设置为未激活');
        
        // 记录分润前的余额
        $buyer_wallet = Db::name('user')->where('id', $buyer_user_id)->value('money');
        $parent_wallet = Db::name('user')->where('id', $parent_user_id)->value('money');
        $before_buyer_balance = floatval($buyer_wallet);
        $before_parent_balance = floatval($parent_wallet);
        
        echo "分润前余额:\n";
        echo "  - 购买用户: {$before_buyer_balance}元\n";
        echo "  - 上级用户: {$before_parent_balance}元\n";
        
        // 清除之前的分润记录
        Db::name('shopro_bonus_log')->where('order_id', $test_order_id)->delete();
        
        // 执行分润处理
        $result = Bonus::processOrderBonus($test_order_id);
        
        if ($result) {
            echo "✓ 分润处理成功\n";
            
            // 记录分润后的余额
            $buyer_wallet_after = Db::name('user')->where('id', $buyer_user_id)->value('money');
            $parent_wallet_after = Db::name('user')->where('id', $parent_user_id)->value('money');
            $after_buyer_balance = floatval($buyer_wallet_after);
            $after_parent_balance = floatval($parent_wallet_after);
            
            echo "分润后余额:\n";
            echo "  - 购买用户: {$after_buyer_balance}元\n";
            echo "  - 上级用户: {$after_parent_balance}元\n";
            
            $buyer_bonus = $after_buyer_balance - $before_buyer_balance;
            $parent_bonus = $after_parent_balance - $before_parent_balance;
            
            echo "分润金额:\n";
            echo "  - 购买用户获得: {$buyer_bonus}元\n";
            echo "  - 上级用户获得: {$parent_bonus}元\n";
            
            // 检查分润日志
            $bonus_logs = Db::name('shopro_bonus_log')
                ->where('order_id', $test_order_id)
                ->select();
                
            echo "分润日志数量: " . count($bonus_logs) . "\n";
            foreach ($bonus_logs as $log) {
                $amount = isset($log['bonus_amount']) ? $log['bonus_amount'] : $log['amount'];
                $type = isset($log['bonus_type']) ? $log['bonus_type'] : $log['type'];
                echo "  - 用户{$log['user_id']}: {$amount}元 ({$type})\n";
            }
            
            if ($parent_bonus == 0) {
                echo "✓ 验证通过：未激活用户没有获得分润\n";
            } else {
                echo "✗ 验证失败：未激活用户仍然获得了分润\n";
            }
            
        } else {
            echo "✗ 分润处理失败\n";
        }
        
        Db::rollback();
        echo "场景1测试事务已回滚\n";
        
    } catch (Exception $e) {
        Db::rollback();
        echo "✗ 场景1测试异常: " . $e->getMessage() . "\n";
    }

    // 3. 测试场景2：上级用户激活时的分润情况
    echo "\n3. 测试场景2：上级用户激活时的分润情况\n";
    
    Db::startTrans();
    try {
        // 确保上级用户已激活
        UserActiveManager::manualSetActiveStatus($parent_user_id, true, 30, '测试：设置为激活');
        
        // 记录分润前的余额
        $buyer_wallet = Db::name('user')->where('id', $buyer_user_id)->value('money');
        $parent_wallet = Db::name('user')->where('id', $parent_user_id)->value('money');
        $before_buyer_balance = floatval($buyer_wallet);
        $before_parent_balance = floatval($parent_wallet);
        
        echo "分润前余额:\n";
        echo "  - 购买用户: {$before_buyer_balance}元\n";
        echo "  - 上级用户: {$before_parent_balance}元\n";
        
        // 清除之前的分润记录
        Db::name('shopro_bonus_log')->where('order_id', $test_order_id)->delete();
        
        // 执行分润处理
        $result = Bonus::processOrderBonus($test_order_id);
        
        if ($result) {
            echo "✓ 分润处理成功\n";
            
            // 记录分润后的余额
            $buyer_wallet_after = Db::name('user')->where('id', $buyer_user_id)->value('money');
            $parent_wallet_after = Db::name('user')->where('id', $parent_user_id)->value('money');
            $after_buyer_balance = floatval($buyer_wallet_after);
            $after_parent_balance = floatval($parent_wallet_after);
            
            echo "分润后余额:\n";
            echo "  - 购买用户: {$after_buyer_balance}元\n";
            echo "  - 上级用户: {$after_parent_balance}元\n";
            
            $buyer_bonus = $after_buyer_balance - $before_buyer_balance;
            $parent_bonus = $after_parent_balance - $before_parent_balance;
            
            echo "分润金额:\n";
            echo "  - 购买用户获得: {$buyer_bonus}元\n";
            echo "  - 上级用户获得: {$parent_bonus}元\n";
            
            // 检查分润日志
            $bonus_logs = Db::name('shopro_bonus_log')
                ->where('order_id', $test_order_id)
                ->select();
                
            echo "分润日志数量: " . count($bonus_logs) . "\n";
            foreach ($bonus_logs as $log) {
                $amount = isset($log['bonus_amount']) ? $log['bonus_amount'] : $log['amount'];
                $type = isset($log['bonus_type']) ? $log['bonus_type'] : $log['type'];
                echo "  - 用户{$log['user_id']}: {$amount}元 ({$type})\n";
            }
            
            if ($parent_bonus > 0) {
                echo "✓ 验证通过：激活用户正常获得分润\n";
            } else {
                echo "✗ 验证失败：激活用户没有获得分润\n";
            }
            
        } else {
            echo "✗ 分润处理失败\n";
        }
        
        Db::rollback();
        echo "场景2测试事务已回滚\n";
        
    } catch (Exception $e) {
        Db::rollback();
        echo "✗ 场景2测试异常: " . $e->getMessage() . "\n";
    }

    // 4. 测试激活状态的自动管理
    echo "\n4. 测试激活状态的自动管理\n";
    
    Db::startTrans();
    try {
        // 获取购买前的激活状态
        $buyer_status_before = UserActiveManager::getUserActiveStatus($buyer_user_id);
        $parent_status_before = UserActiveManager::getUserActiveStatus($parent_user_id);
        
        echo "购买前激活状态:\n";
        echo "  - 购买用户: " . ($buyer_status_before['is_active'] ? '已激活' : '未激活') . "\n";
        echo "  - 上级用户: " . ($parent_status_before['is_active'] ? '已激活' : '未激活') . "\n";
        
        // 执行分润处理（包含激活状态管理）
        $result = Bonus::processOrderBonus($test_order_id);
        
        if ($result) {
            echo "✓ 分润处理成功\n";
            
            // 获取购买后的激活状态
            $buyer_status_after = UserActiveManager::getUserActiveStatus($buyer_user_id);
            $parent_status_after = UserActiveManager::getUserActiveStatus($parent_user_id);
            
            echo "购买后激活状态:\n";
            echo "  - 购买用户: " . ($buyer_status_after['is_active'] ? '已激活' : '未激活') . "\n";
            echo "  - 上级用户: " . ($parent_status_after['is_active'] ? '已激活' : '未激活') . "\n";
            
            // 检查激活日志
            $buyer_logs = UserActiveManager::getUserActiveLogs($buyer_user_id, 3);
            $parent_logs = UserActiveManager::getUserActiveLogs($parent_user_id, 3);
            
            echo "激活日志:\n";
            echo "  - 购买用户新增日志: " . count($buyer_logs) . "条\n";
            echo "  - 上级用户新增日志: " . count($parent_logs) . "条\n";
            
            if ($buyer_status_after['is_active']) {
                echo "✓ 验证通过：购买奖金商品后用户自动激活\n";
            } else {
                echo "✗ 验证失败：购买奖金商品后用户未激活\n";
            }
            
        } else {
            echo "✗ 分润处理失败\n";
        }
        
        Db::rollback();
        echo "激活状态管理测试事务已回滚\n";
        
    } catch (Exception $e) {
        Db::rollback();
        echo "✗ 激活状态管理测试异常: " . $e->getMessage() . "\n";
    }

    // 5. 测试激活状态缓存
    echo "\n5. 测试激活状态缓存\n";
    
    $start_time = microtime(true);
    
    // 第一次查询（无缓存）
    $status1 = UserActiveManager::isUserActive($buyer_user_id, true);
    $time1 = microtime(true) - $start_time;
    
    // 第二次查询（有缓存）
    $start_time2 = microtime(true);
    $status2 = UserActiveManager::isUserActive($buyer_user_id, true);
    $time2 = microtime(true) - $start_time2;
    
    echo "激活状态查询性能:\n";
    echo "  - 第一次查询: " . number_format($time1 * 1000, 2) . "ms\n";
    echo "  - 第二次查询: " . number_format($time2 * 1000, 2) . "ms\n";
    echo "  - 缓存效果: " . ($time2 < $time1 ? '有效' : '无效') . "\n";
    
    // 清除缓存测试
    UserActiveManager::clearActiveCache($buyer_user_id);
    echo "✓ 缓存清除测试完成\n";

    echo "\n=== 专项测试完成 ===\n";

} catch (Exception $e) {
    echo "测试异常: " . $e->getMessage() . "\n";
    echo "堆栈跟踪: " . $e->getTraceAsString() . "\n";
}
