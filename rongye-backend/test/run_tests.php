<?php
/**
 * 融业港分润系统测试运行脚本
 * 用于批量运行所有测试或指定类型的测试
 */

// 定义应用目录
define('APP_PATH', dirname(__DIR__) . '/application/');
// 定义根目录
define('ROOT_PATH', dirname(__DIR__) . '/');
// 定义目录分隔符
define('DS', DIRECTORY_SEPARATOR);

// 测试文件配置
$test_files = [
    'user_active' => [
        'name' => '用户激活状态管理测试',
        'files' => [
            'test_user_active_management.php' => '基础功能测试',
            'test_active_bonus_control.php' => '分润控制测试',
            'test_buyer_activation_fix.php' => '购买者激活修正测试'
        ]
    ],
    'bonus_limit' => [
        'name' => '收益上限控制测试',
        'files' => [
            'test_bonus_limit_control.php' => '基础功能测试',
            'test_bonus_limit_scenarios.php' => '场景测试'
        ]
    ]
];

/**
 * 显示帮助信息
 */
function showHelp() {
    echo "融业港分润系统测试运行脚本\n";
    echo "用法: php run_tests.php [选项]\n\n";
    echo "选项:\n";
    echo "  all              运行所有测试\n";
    echo "  user_active      运行用户激活状态管理相关测试\n";
    echo "  bonus_limit      运行收益上限控制相关测试\n";
    echo "  list             列出所有可用的测试\n";
    echo "  help             显示此帮助信息\n\n";
    echo "示例:\n";
    echo "  php test/run_tests.php all\n";
    echo "  php test/run_tests.php user_active\n";
    echo "  php test/run_tests.php bonus_limit\n";
}

/**
 * 列出所有测试
 */
function listTests($test_files) {
    echo "可用的测试:\n\n";
    foreach ($test_files as $category => $info) {
        echo "【{$info['name']}】 (运行: php test/run_tests.php {$category})\n";
        foreach ($info['files'] as $file => $desc) {
            echo "  - {$file}: {$desc}\n";
        }
        echo "\n";
    }
}

/**
 * 运行单个测试文件
 */
function runTestFile($file_path, $file_name, $description) {
    echo "\n" . str_repeat("=", 80) . "\n";
    echo "运行测试: {$file_name}\n";
    echo "描述: {$description}\n";
    echo str_repeat("=", 80) . "\n";
    
    $start_time = microtime(true);
    
    // 执行测试文件
    $output = [];
    $return_code = 0;
    exec("cd " . dirname(__DIR__) . " && php {$file_path} 2>&1", $output, $return_code);
    
    $end_time = microtime(true);
    $execution_time = round(($end_time - $start_time) * 1000, 2);
    
    // 输出测试结果
    foreach ($output as $line) {
        echo $line . "\n";
    }
    
    echo "\n" . str_repeat("-", 80) . "\n";
    echo "测试文件: {$file_name}\n";
    echo "执行时间: {$execution_time}ms\n";
    echo "返回码: {$return_code}\n";
    
    if ($return_code === 0) {
        echo "状态: ✅ 成功\n";
    } else {
        echo "状态: ❌ 失败\n";
    }
    echo str_repeat("-", 80) . "\n";
    
    return $return_code === 0;
}

/**
 * 运行测试类别
 */
function runTestCategory($category, $test_files) {
    if (!isset($test_files[$category])) {
        echo "错误: 未知的测试类别 '{$category}'\n";
        return false;
    }
    
    $category_info = $test_files[$category];
    echo "\n开始运行: {$category_info['name']}\n";
    
    $total_tests = count($category_info['files']);
    $passed_tests = 0;
    $failed_tests = 0;
    
    foreach ($category_info['files'] as $file => $description) {
        $file_path = "test/{$file}";
        $success = runTestFile($file_path, $file, $description);
        
        if ($success) {
            $passed_tests++;
        } else {
            $failed_tests++;
        }
    }
    
    // 输出类别测试总结
    echo "\n" . str_repeat("=", 80) . "\n";
    echo "{$category_info['name']} 测试总结\n";
    echo str_repeat("=", 80) . "\n";
    echo "总测试数: {$total_tests}\n";
    echo "通过: {$passed_tests}\n";
    echo "失败: {$failed_tests}\n";
    echo "成功率: " . round(($passed_tests / $total_tests) * 100, 2) . "%\n";
    echo str_repeat("=", 80) . "\n";
    
    return $failed_tests === 0;
}

// 主程序
if ($argc < 2) {
    showHelp();
    exit(1);
}

$command = $argv[1];

switch ($command) {
    case 'help':
    case '--help':
    case '-h':
        showHelp();
        break;
        
    case 'list':
        listTests($test_files);
        break;
        
    case 'all':
        echo "\n开始运行所有测试...\n";
        $success = true;
        foreach ($test_files as $category => $info) {
            $result = runTestCategory($category, $test_files);
            if (!$result) {
                $success = false;
            }
        }
        exit($success ? 0 : 1);
        
    case 'user_active':
    case 'bonus_limit':
        $success = runTestCategory($command, $test_files);
        exit($success ? 0 : 1);
        
    default:
        echo "错误: 未知的命令 '{$command}'\n";
        echo "使用 'php test/run_tests.php help' 查看帮助信息\n";
        exit(1);
}
