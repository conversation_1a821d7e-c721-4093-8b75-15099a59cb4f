<?php
/**
 * 用户状态监控控制台命令测试脚本
 */

// 定义应用目录
define('APP_PATH', dirname(__DIR__) . '/application/');
// 定义根目录
define('ROOT_PATH', dirname(__DIR__) . '/');
// 定义目录分隔符
define('DS', DIRECTORY_SEPARATOR);

// 加载框架引导文件
require dirname(__DIR__) . '/thinkphp/start.php';

use think\Db;
use addons\shopro\library\UserActiveManager;
use addons\shopro\library\BonusLimitManager;

echo "=== 用户状态监控控制台命令测试 ===\n";

try {
    // 1. 准备测试数据
    echo "\n1. 准备测试数据\n";
    
    Db::startTrans();
    try {
        // 创建一个即将过期的测试用户
        $test_user_id = 100005;
        $current_time = time();
        $expire_time = $current_time - 10; // 10秒前过期
        
        // 检查用户是否存在，不存在则创建
        $test_user = Db::name('user')->where('id', $test_user_id)->find();
        if (!$test_user) {
            Db::name('user')->insert([
                'id' => $test_user_id,
                'username' => 'test_expire_user',
                'nickname' => '测试过期用户',
                'mobile' => '13800000005',
                'password' => md5('123456'),
                'salt' => 'test',
                'email' => '<EMAIL>',
                'avatar' => '',
                'level' => 1,
                'gender' => 0,
                'birthday' => null,
                'bio' => '',
                'money' => 0.00,
                'score' => 0,
                'successions' => 0,
                'maxsuccessions' => 0,
                'prevtime' => 0,
                'logintime' => 0,
                'loginip' => '',
                'loginfailure' => 0,
                'joinip' => '127.0.0.1',
                'jointime' => $current_time,
                'createtime' => $current_time,
                'updatetime' => $current_time,
                'token' => '',
                'status' => 'normal',
                'verification' => '',
                'is_active' => 1,
                'active_start_time' => $current_time - 86400 * 5, // 5天前开始激活
                'active_expire_time' => $expire_time, // 已过期
                'total_active_days' => 0,
                'active_count' => 1,
                'total_purchase_amount' => 100.00,
                'total_bonus_received' => 50.00,
                'bonus_limit_reached' => 0
            ]);
            echo "  - 创建测试用户: {$test_user_id}\n";
        } else {
            // 更新现有用户为过期状态
            Db::name('user')->where('id', $test_user_id)->update([
                'is_active' => 1,
                'active_start_time' => $current_time - 86400 * 5,
                'active_expire_time' => $expire_time,
                'total_purchase_amount' => 100.00,
                'total_bonus_received' => 50.00,
                'bonus_limit_reached' => 0
            ]);
            echo "  - 更新测试用户: {$test_user_id} 为过期状态\n";
        }
        
        // 确保资金池有足够余额
        $pool = Db::name('shopro_score_pool')->where('pool_code', 'default')->find();
        if ($pool && $pool['total_balance'] < 1000) {
            Db::name('shopro_score_pool')->where('pool_code', 'default')->update([
                'total_balance' => 2000.00,
                'total_deposit' => 2000.00,
                'updatetime' => $current_time
            ]);
            echo "  - 更新资金池余额为2000元\n";
        }
        
        Db::commit();
        echo "✓ 测试数据准备完成\n";
        
    } catch (Exception $e) {
        Db::rollback();
        throw $e;
    }
    
    // 2. 测试控制台命令执行
    echo "\n2. 执行用户状态监控命令\n";
    
    // 清除缓存
    UserActiveManager::clearActiveCache();
    BonusLimitManager::clearLimitCache();
    
    // 执行控制台命令
    $command = 'cd ' . dirname(__DIR__) . ' && php think shopro:user-check';
    echo "执行命令: {$command}\n";
    
    $output = [];
    $return_code = 0;
    exec($command . ' 2>&1', $output, $return_code);
    
    echo "命令输出:\n";
    foreach ($output as $line) {
        echo "  " . $line . "\n";
    }
    echo "返回码: {$return_code}\n";
    
    // 3. 验证执行结果
    echo "\n3. 验证执行结果\n";
    
    // 检查过期用户状态
    $expired_user = Db::name('user')->where('id', $test_user_id)->find();
    echo "过期用户状态验证:\n";
    echo "  - 用户ID: {$expired_user['id']}\n";
    echo "  - 激活状态: " . ($expired_user['is_active'] ? '已激活' : '未激活') . "\n";
    echo "  - 累计激活天数: {$expired_user['total_active_days']}天\n";
    
    if ($expired_user['is_active'] == 0) {
        echo "✓ 过期用户状态更新正确\n";
    } else {
        echo "✗ 过期用户状态更新失败\n";
    }
    
    echo "\n=== 测试完成 ===\n";

} catch (Exception $e) {
    echo "测试异常: " . $e->getMessage() . "\n";
    echo "堆栈跟踪: " . $e->getTraceAsString() . "\n";
}
