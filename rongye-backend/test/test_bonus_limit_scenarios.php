<?php
/**
 * 收益上限控制场景测试脚本
 */

// 定义应用目录
define('APP_PATH', dirname(__DIR__) . '/application/');
// 定义根目录
define('ROOT_PATH', dirname(__DIR__) . '/');
// 定义目录分隔符
define('DS', DIRECTORY_SEPARATOR);

// 加载框架引导文件
require dirname(__DIR__) . '/thinkphp/start.php';

use addons\shopro\library\BonusLimitManager;
use addons\shopro\library\UserActiveManager;
use addons\shopro\library\Bonus;
use think\Db;

echo "=== 收益上限控制场景测试 ===\n";

try {
    // 1. 测试场景：正常用户分润
    echo "\n1. 测试场景：正常用户分润\n";
    
    $normal_user_id = 100002;
    $test_order_id = 108;
    
    Db::startTrans();
    try {
        // 重置用户收益状态（模拟正常用户）
        Db::name('user')->where('id', $normal_user_id)->update([
            'total_purchase_amount' => 100.00,
            'total_bonus_received' => 50.00,
            'bonus_limit_reached' => 0
        ]);
        
        // 清除缓存
        BonusLimitManager::clearLimitCache();
        UserActiveManager::clearActiveCache();
        
        // 检查状态
        $status = BonusLimitManager::getUserLimitStatus($normal_user_id);
        echo "正常用户状态:\n";
        echo "  - 购买金额: {$status['total_purchase_amount']}元\n";
        echo "  - 收益金额: {$status['total_bonus_received']}元\n";
        echo "  - 收益倍数: {$status['bonus_ratio']}倍\n";
        echo "  - 可获得分润: " . ($status['can_receive_bonus'] ? '是' : '否') . "\n";
        
        // 激活用户
        UserActiveManager::manualSetActiveStatus($normal_user_id, true, 30, '测试激活');
        
        // 检查综合激活状态
        $is_active = UserActiveManager::isUserActive($normal_user_id);
        echo "  - 综合激活状态: " . ($is_active ? '已激活' : '未激活') . "\n";
        
        if ($is_active) {
            echo "✓ 正常用户可以获得分润\n";
        } else {
            echo "✗ 正常用户无法获得分润\n";
        }
        
        Db::rollback();
        echo "场景1测试事务已回滚\n";
        
    } catch (Exception $e) {
        Db::rollback();
        echo "✗ 场景1测试异常: " . $e->getMessage() . "\n";
    }

    // 2. 测试场景：接近上限用户分润
    echo "\n2. 测试场景：接近上限用户分润\n";
    
    $near_limit_user_id = 100003;
    
    Db::startTrans();
    try {
        // 设置用户接近上限（9.5倍）
        Db::name('user')->where('id', $near_limit_user_id)->update([
            'total_purchase_amount' => 100.00,
            'total_bonus_received' => 950.00,
            'bonus_limit_reached' => 0
        ]);
        
        // 清除缓存
        BonusLimitManager::clearLimitCache();
        UserActiveManager::clearActiveCache();
        
        // 检查状态
        $status = BonusLimitManager::getUserLimitStatus($near_limit_user_id);
        echo "接近上限用户状态:\n";
        echo "  - 购买金额: {$status['total_purchase_amount']}元\n";
        echo "  - 收益金额: {$status['total_bonus_received']}元\n";
        echo "  - 收益倍数: {$status['bonus_ratio']}倍\n";
        echo "  - 剩余上限: {$status['remaining_limit']}元\n";
        echo "  - 状态描述: {$status['status_desc']}\n";
        echo "  - 可获得分润: " . ($status['can_receive_bonus'] ? '是' : '否') . "\n";
        
        // 激活用户
        UserActiveManager::manualSetActiveStatus($near_limit_user_id, true, 30, '测试激活');
        
        // 检查综合激活状态
        $is_active = UserActiveManager::isUserActive($near_limit_user_id);
        echo "  - 综合激活状态: " . ($is_active ? '已激活' : '未激活') . "\n";
        
        // 模拟获得一笔收益（可能导致超限）
        echo "\n模拟获得100元收益:\n";
        $result = BonusLimitManager::recordBonusAmount($near_limit_user_id, 100.00, 999, 'test_bonus');
        
        if ($result) {
            $new_status = BonusLimitManager::getUserLimitStatus($near_limit_user_id);
            echo "  - 新收益金额: {$new_status['total_bonus_received']}元\n";
            echo "  - 新收益倍数: {$new_status['bonus_ratio']}倍\n";
            echo "  - 是否达到上限: " . ($new_status['bonus_limit_reached'] ? '是' : '否') . "\n";
            echo "  - 可获得分润: " . ($new_status['can_receive_bonus'] ? '是' : '否') . "\n";
            
            if ($new_status['bonus_limit_reached']) {
                echo "✓ 用户达到收益上限，系统正确标记\n";
            } else {
                echo "✗ 用户应该达到上限但未标记\n";
            }
        }
        
        Db::rollback();
        echo "场景2测试事务已回滚\n";
        
    } catch (Exception $e) {
        Db::rollback();
        echo "✗ 场景2测试异常: " . $e->getMessage() . "\n";
    }

    // 3. 测试场景：已达上限用户尝试获得分润
    echo "\n3. 测试场景：已达上限用户尝试获得分润\n";
    
    $limit_reached_user_id = 100001; // 这个用户已经达到上限
    
    Db::startTrans();
    try {
        // 检查当前状态
        $status = BonusLimitManager::getUserLimitStatus($limit_reached_user_id);
        echo "已达上限用户状态:\n";
        echo "  - 购买金额: {$status['total_purchase_amount']}元\n";
        echo "  - 收益金额: {$status['total_bonus_received']}元\n";
        echo "  - 收益倍数: {$status['bonus_ratio']}倍\n";
        echo "  - 是否达到上限: " . ($status['bonus_limit_reached'] ? '是' : '否') . "\n";
        echo "  - 可获得分润: " . ($status['can_receive_bonus'] ? '是' : '否') . "\n";
        
        // 激活用户（分润激活状态）
        UserActiveManager::manualSetActiveStatus($limit_reached_user_id, true, 30, '测试激活');
        
        // 检查综合激活状态
        $is_active = UserActiveManager::isUserActive($limit_reached_user_id);
        echo "  - 分润激活状态: 是\n";
        echo "  - 综合激活状态: " . ($is_active ? '已激活' : '未激活') . "\n";
        
        if (!$is_active) {
            echo "✓ 已达上限用户被正确阻止获得分润\n";
        } else {
            echo "✗ 已达上限用户仍然可以获得分润（错误）\n";
        }
        
        // 尝试记录新收益（应该被记录但不影响分润判断）
        echo "\n尝试记录新收益:\n";
        $before_bonus = $status['total_bonus_received'];
        $result = BonusLimitManager::recordBonusAmount($limit_reached_user_id, 50.00, 999, 'test_bonus');
        
        if ($result) {
            $new_status = BonusLimitManager::getUserLimitStatus($limit_reached_user_id);
            echo "  - 收益记录成功: {$before_bonus} -> {$new_status['total_bonus_received']}元\n";
            echo "  - 仍然不可分润: " . ($new_status['can_receive_bonus'] ? '否（错误）' : '是（正确）') . "\n";
        }
        
        Db::rollback();
        echo "场景3测试事务已回滚\n";
        
    } catch (Exception $e) {
        Db::rollback();
        echo "✗ 场景3测试异常: " . $e->getMessage() . "\n";
    }

    // 4. 测试场景：无购买记录用户
    echo "\n4. 测试场景：无购买记录用户\n";
    
    $no_purchase_user_id = 100004;
    
    Db::startTrans();
    try {
        // 确保用户无购买记录
        Db::name('user')->where('id', $no_purchase_user_id)->update([
            'total_purchase_amount' => 0.00,
            'total_bonus_received' => 0.00,
            'bonus_limit_reached' => 0
        ]);
        
        // 清除缓存
        BonusLimitManager::clearLimitCache();
        
        // 检查状态
        $status = BonusLimitManager::getUserLimitStatus($no_purchase_user_id);
        if ($status) {
            echo "无购买记录用户状态:\n";
            echo "  - 购买金额: {$status['total_purchase_amount']}元\n";
            echo "  - 收益金额: {$status['total_bonus_received']}元\n";
            echo "  - 状态描述: {$status['status_desc']}\n";
            echo "  - 可获得分润: " . ($status['can_receive_bonus'] ? '是' : '否') . "\n";
            
            if (!$status['can_receive_bonus'] && $status['status_desc'] == 'no_purchase') {
                echo "✓ 无购买记录用户被正确阻止获得分润\n";
            } else {
                echo "✗ 无购买记录用户状态判断错误\n";
            }
        }
        
        Db::rollback();
        echo "场景4测试事务已回滚\n";
        
    } catch (Exception $e) {
        Db::rollback();
        echo "✗ 场景4测试异常: " . $e->getMessage() . "\n";
    }

    // 5. 测试场景：重置收益上限状态
    echo "\n5. 测试场景：重置收益上限状态\n";
    
    $reset_user_id = 100001;
    
    Db::startTrans();
    try {
        // 检查重置前状态
        $before_status = BonusLimitManager::getUserLimitStatus($reset_user_id);
        echo "重置前状态:\n";
        echo "  - 是否达到上限: " . ($before_status['bonus_limit_reached'] ? '是' : '否') . "\n";
        echo "  - 可获得分润: " . ($before_status['can_receive_bonus'] ? '是' : '否') . "\n";
        
        // 重置收益上限状态
        $result = BonusLimitManager::resetUserLimitStatus($reset_user_id, '测试重置收益上限状态', 0);
        
        if ($result) {
            echo "✓ 收益上限状态重置成功\n";
            
            // 检查重置后状态
            $after_status = BonusLimitManager::getUserLimitStatus($reset_user_id);
            echo "重置后状态:\n";
            echo "  - 是否达到上限: " . ($after_status['bonus_limit_reached'] ? '是' : '否') . "\n";
            echo "  - 可获得分润: " . ($after_status['can_receive_bonus'] ? '是' : '否') . "\n";
            
            // 检查重置日志
            $logs = BonusLimitManager::getUserLimitLogs($reset_user_id, 3);
            echo "重置日志记录: " . count($logs) . "条\n";
            if (!empty($logs)) {
                $latest_log = $logs[0];
                echo "  - 最新日志: {$latest_log['action_type']} - {$latest_log['memo']}\n";
            }
            
        } else {
            echo "✗ 收益上限状态重置失败\n";
        }
        
        Db::rollback();
        echo "场景5测试事务已回滚\n";
        
    } catch (Exception $e) {
        Db::rollback();
        echo "✗ 场景5测试异常: " . $e->getMessage() . "\n";
    }

    // 6. 测试场景：批量用户状态检查
    echo "\n6. 测试场景：批量用户状态检查\n";
    
    $batch_user_ids = [100001, 100002, 100003, 100004];
    
    // 设置不同的测试状态
    Db::startTrans();
    try {
        // 用户1：已达上限
        Db::name('user')->where('id', 100001)->update([
            'total_purchase_amount' => 100.00,
            'total_bonus_received' => 1000.00,
            'bonus_limit_reached' => 1
        ]);
        
        // 用户2：正常状态
        Db::name('user')->where('id', 100002)->update([
            'total_purchase_amount' => 100.00,
            'total_bonus_received' => 50.00,
            'bonus_limit_reached' => 0
        ]);
        
        // 用户3：接近上限
        Db::name('user')->where('id', 100003)->update([
            'total_purchase_amount' => 100.00,
            'total_bonus_received' => 950.00,
            'bonus_limit_reached' => 0
        ]);
        
        // 用户4：无购买记录
        Db::name('user')->where('id', 100004)->update([
            'total_purchase_amount' => 0.00,
            'total_bonus_received' => 0.00,
            'bonus_limit_reached' => 0
        ]);
        
        // 清除缓存
        BonusLimitManager::clearLimitCache();
        
        // 批量检查
        $batch_result = BonusLimitManager::batchCheckLimitStatus($batch_user_ids);
        
        echo "批量状态检查结果:\n";
        foreach ($batch_user_ids as $uid) {
            $status = BonusLimitManager::getUserLimitStatus($uid);
            $can_receive = $batch_result[$uid] ?? false;
            echo "  - 用户{$uid}: {$status['status_desc']}, 可分润: " . ($can_receive ? '是' : '否') . "\n";
        }
        
        // 验证结果
        $expected_results = [
            100001 => false, // 已达上限
            100002 => true,  // 正常状态
            100003 => true,  // 接近上限但未达到
            100004 => false  // 无购买记录
        ];
        
        $all_correct = true;
        foreach ($expected_results as $uid => $expected) {
            if ($batch_result[$uid] !== $expected) {
                echo "✗ 用户{$uid}状态检查错误，期望: " . ($expected ? '可分润' : '不可分润') . "，实际: " . ($batch_result[$uid] ? '可分润' : '不可分润') . "\n";
                $all_correct = false;
            }
        }
        
        if ($all_correct) {
            echo "✓ 批量状态检查结果正确\n";
        }
        
        Db::rollback();
        echo "场景6测试事务已回滚\n";
        
    } catch (Exception $e) {
        Db::rollback();
        echo "✗ 场景6测试异常: " . $e->getMessage() . "\n";
    }

    echo "\n=== 收益上限控制场景测试完成 ===\n";

} catch (Exception $e) {
    echo "测试异常: " . $e->getMessage() . "\n";
    echo "堆栈跟踪: " . $e->getTraceAsString() . "\n";
}
