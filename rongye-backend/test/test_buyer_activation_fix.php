<?php
/**
 * 测试购买者激活状态修正
 */

// 定义应用目录
define('APP_PATH', dirname(__DIR__) . '/application/');
// 定义根目录
define('ROOT_PATH', dirname(__DIR__) . '/');
// 定义目录分隔符
define('DS', DIRECTORY_SEPARATOR);

// 加载框架引导文件
require dirname(__DIR__) . '/thinkphp/start.php';

use addons\shopro\library\UserActiveManager;
use addons\shopro\library\Bonus;
use think\Db;

echo "=== 购买者激活状态修正测试 ===\n";

try {
    $test_order_id = 108;
    $buyer_user_id = 100001;
    $parent_user_id = 100002;
    
    // 1. 确认用户当前状态
    echo "\n1. 确认用户当前状态\n";
    
    $buyer = Db::name('user')->where('id', $buyer_user_id)->find();
    $parent = Db::name('user')->where('id', $parent_user_id)->find();
    
    echo "购买用户 {$buyer['username']} (ID: {$buyer_user_id}):\n";
    echo "  - is_active: {$buyer['is_active']}\n";
    echo "  - active_expire_time: {$buyer['active_expire_time']}\n";
    
    echo "上级用户 {$parent['username']} (ID: {$parent_user_id}):\n";
    echo "  - is_active: {$parent['is_active']}\n";
    echo "  - active_expire_time: {$parent['active_expire_time']}\n";
    
    // 2. 清除激活状态缓存
    echo "\n2. 清除激活状态缓存\n";
    UserActiveManager::clearActiveCache();
    echo "✓ 缓存已清除\n";
    
    // 3. 验证激活状态检查
    echo "\n3. 验证激活状态检查\n";
    $buyer_active = UserActiveManager::isUserActive($buyer_user_id);
    $parent_active = UserActiveManager::isUserActive($parent_user_id);
    
    echo "购买用户激活状态: " . ($buyer_active ? '已激活' : '未激活') . "\n";
    echo "上级用户激活状态: " . ($parent_active ? '已激活' : '未激活') . "\n";
    
    // 4. 测试分润处理（未激活购买者）
    echo "\n4. 测试分润处理（未激活购买者）\n";
    
    Db::startTrans();
    try {
        // 记录分润前的余额
        $buyer_wallet_before = Db::name('user')->where('id', $buyer_user_id)->value('money');
        $parent_wallet_before = Db::name('user')->where('id', $parent_user_id)->value('money');
        
        echo "分润前余额:\n";
        echo "  - 购买用户: {$buyer_wallet_before}元\n";
        echo "  - 上级用户: {$parent_wallet_before}元\n";
        
        // 清除之前的分润记录
        Db::name('shopro_bonus_log')->where('order_id', $test_order_id)->delete();
        
        // 执行分润处理
        $result = Bonus::processOrderBonus($test_order_id);
        
        if ($result) {
            echo "✓ 分润处理成功\n";
            
            // 记录分润后的余额
            $buyer_wallet_after = Db::name('user')->where('id', $buyer_user_id)->value('money');
            $parent_wallet_after = Db::name('user')->where('id', $parent_user_id)->value('money');
            
            echo "分润后余额:\n";
            echo "  - 购买用户: {$buyer_wallet_after}元\n";
            echo "  - 上级用户: {$parent_wallet_after}元\n";
            
            $buyer_bonus = $buyer_wallet_after - $buyer_wallet_before;
            $parent_bonus = $parent_wallet_after - $parent_wallet_before;
            
            echo "分润金额:\n";
            echo "  - 购买用户获得: {$buyer_bonus}元\n";
            echo "  - 上级用户获得: {$parent_bonus}元\n";
            
            // 检查分润日志
            $bonus_logs = Db::name('shopro_bonus_log')
                ->where('order_id', $test_order_id)
                ->select();
                
            echo "分润日志数量: " . count($bonus_logs) . "\n";
            foreach ($bonus_logs as $log) {
                $amount = isset($log['bonus_amount']) ? $log['bonus_amount'] : $log['amount'];
                $type = isset($log['bonus_type']) ? $log['bonus_type'] : $log['type'];
                echo "  - 用户{$log['user_id']}: {$amount}元 ({$type})\n";
            }
            
            // 检查购买者激活状态变化
            $buyer_active_after = UserActiveManager::isUserActive($buyer_user_id);
            echo "处理后购买用户激活状态: " . ($buyer_active_after ? '已激活' : '未激活') . "\n";
            
            // 验证结果
            if ($buyer_bonus == 0) {
                echo "✓ 验证通过：未激活购买者没有获得团队奖励\n";
            } else {
                echo "✗ 验证失败：未激活购买者仍然获得了团队奖励\n";
            }
            
            if ($parent_bonus == 0) {
                echo "✓ 验证通过：未激活上级用户没有获得分润\n";
            } else {
                echo "✗ 验证失败：未激活上级用户仍然获得了分润\n";
            }
            
        } else {
            echo "✗ 分润处理失败\n";
        }
        
        Db::rollback();
        echo "测试事务已回滚\n";
        
    } catch (Exception $e) {
        Db::rollback();
        echo "✗ 分润处理测试异常: " . $e->getMessage() . "\n";
    }
    
    // 5. 测试激活后的分润处理
    echo "\n5. 测试激活后的分润处理\n";
    
    Db::startTrans();
    try {
        // 手动激活购买用户
        UserActiveManager::manualSetActiveStatus($buyer_user_id, true, 30, '测试激活');
        echo "✓ 购买用户已手动激活\n";
        
        // 记录分润前的余额
        $buyer_wallet_before = Db::name('user')->where('id', $buyer_user_id)->value('money');
        $parent_wallet_before = Db::name('user')->where('id', $parent_user_id)->value('money');
        
        echo "分润前余额:\n";
        echo "  - 购买用户: {$buyer_wallet_before}元\n";
        echo "  - 上级用户: {$parent_wallet_before}元\n";
        
        // 清除之前的分润记录
        Db::name('shopro_bonus_log')->where('order_id', $test_order_id)->delete();
        
        // 执行分润处理
        $result = Bonus::processOrderBonus($test_order_id);
        
        if ($result) {
            echo "✓ 分润处理成功\n";
            
            // 记录分润后的余额
            $buyer_wallet_after = Db::name('user')->where('id', $buyer_user_id)->value('money');
            $parent_wallet_after = Db::name('user')->where('id', $parent_user_id)->value('money');
            
            echo "分润后余额:\n";
            echo "  - 购买用户: {$buyer_wallet_after}元\n";
            echo "  - 上级用户: {$parent_wallet_after}元\n";
            
            $buyer_bonus = $buyer_wallet_after - $buyer_wallet_before;
            $parent_bonus = $parent_wallet_after - $parent_wallet_before;
            
            echo "分润金额:\n";
            echo "  - 购买用户获得: {$buyer_bonus}元\n";
            echo "  - 上级用户获得: {$parent_bonus}元\n";
            
            // 检查分润日志
            $bonus_logs = Db::name('shopro_bonus_log')
                ->where('order_id', $test_order_id)
                ->select();
                
            echo "分润日志数量: " . count($bonus_logs) . "\n";
            foreach ($bonus_logs as $log) {
                $amount = isset($log['bonus_amount']) ? $log['bonus_amount'] : $log['amount'];
                $type = isset($log['bonus_type']) ? $log['bonus_type'] : $log['type'];
                echo "  - 用户{$log['user_id']}: {$amount}元 ({$type})\n";
            }
            
            // 验证结果
            if ($buyer_bonus > 0) {
                echo "✓ 验证通过：激活购买者正常获得团队奖励\n";
            } else {
                echo "✗ 验证失败：激活购买者没有获得团队奖励\n";
            }
            
        } else {
            echo "✗ 分润处理失败\n";
        }
        
        Db::rollback();
        echo "测试事务已回滚\n";
        
    } catch (Exception $e) {
        Db::rollback();
        echo "✗ 激活后分润处理测试异常: " . $e->getMessage() . "\n";
    }

    echo "\n=== 测试完成 ===\n";

} catch (Exception $e) {
    echo "测试异常: " . $e->getMessage() . "\n";
    echo "堆栈跟踪: " . $e->getTraceAsString() . "\n";
}
