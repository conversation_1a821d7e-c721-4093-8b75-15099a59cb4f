<?php
/**
 * 积分池功能测试脚本
 * 测试积分池的创建、沉淀、查询等功能
 */

// 定义应用目录
define('APP_PATH', __DIR__ . '/../application/');
// 定义根目录
define('ROOT_PATH', __DIR__ . '/../');
// 定义目录分隔符
define('DS', DIRECTORY_SEPARATOR);

// 加载框架引导文件
require __DIR__ . '/../thinkphp/start.php';

use addons\shopro\library\ScorePool;
use addons\shopro\library\Bonus;
use think\Db;
use think\Log;

class ScorePoolTest
{
    /**
     * 运行所有测试
     */
    public static function runAllTests()
    {
        echo "=== 积分池功能测试开始 ===\n";
        
        try {
            // 1. 测试积分池基础功能
            self::testPoolBasicFunctions();
            
            // 2. 测试积分池沉淀功能
            self::testPoolDeposit();
            
            // 3. 测试与分润系统集成
            self::testBonusIntegration();
            
            // 4. 测试数据一致性
            self::testDataConsistency();
            
            echo "\n=== 所有测试完成 ===\n";
            
        } catch (Exception $e) {
            echo "测试异常: " . $e->getMessage() . "\n";
        }
    }

    /**
     * 测试积分池基础功能
     */
    private static function testPoolBasicFunctions()
    {
        echo "\n--- 测试积分池基础功能 ---\n";
        
        // 测试获取积分池信息
        $pool = ScorePool::getPool();
        if ($pool) {
            echo "✓ 获取积分池成功: {$pool['pool_name']}\n";
            echo "  - 积分池余额: {$pool['total_balance']}\n";
            echo "  - 累计沉淀: {$pool['total_deposit']}\n";
            echo "  - 沉淀比例: " . ($pool['deposit_rate'] * 100) . "%\n";
        } else {
            echo "✗ 获取积分池失败\n";
        }
        
        // 测试获取积分池统计
        $stats = ScorePool::getPoolStats();
        if (!empty($stats)) {
            echo "✓ 获取积分池统计成功\n";
            echo "  - 总交易数: " . ($stats['total_transactions'] ?? 0) . "\n";
            echo "  - 总订单数: " . ($stats['total_orders'] ?? 0) . "\n";
            echo "  - 涉及商品数: " . ($stats['total_goods'] ?? 0) . "\n";
        }
    }

    /**
     * 测试积分池沉淀功能
     */
    private static function testPoolDeposit()
    {
        echo "\n--- 测试积分池沉淀功能 ---\n";
        
        // 模拟奖金商品数据
        $test_order_id = 999999; // 测试订单ID
        $bonus_items = [
            [
                'goods_id' => 17,
                'goods_title' => '测试奖金商品1',
                'goods_num' => 1,
                'pay_fee' => 799.00
            ],
            [
                'goods_id' => 32,
                'goods_title' => '测试奖金商品2',
                'goods_num' => 2,
                'pay_fee' => 1598.00
            ]
        ];
        
        // 获取沉淀前的积分池余额
        $before_balance = ScorePool::getPoolBalance();
        echo "沉淀前积分池余额: {$before_balance}\n";
        
        // 执行积分池沉淀
        $result = ScorePool::processDeposit($test_order_id, $bonus_items);
        
        if ($result) {
            echo "✓ 积分池沉淀成功\n";
            
            // 获取沉淀后的积分池余额
            $after_balance = ScorePool::getPoolBalance();
            echo "沉淀后积分池余额: {$after_balance}\n";
            
            // 计算预期沉淀金额
            $expected_deposit = 0;
            foreach ($bonus_items as $item) {
                $expected_deposit += $item['pay_fee'] * 0.05; // 5%沉淀
            }
            echo "预期沉淀金额: {$expected_deposit}\n";
            
            $actual_deposit = $after_balance - $before_balance;
            echo "实际沉淀金额: {$actual_deposit}\n";
            
            if (abs($actual_deposit - $expected_deposit) < 0.01) {
                echo "✓ 沉淀金额计算正确\n";
            } else {
                echo "✗ 沉淀金额计算错误\n";
            }
            
        } else {
            echo "✗ 积分池沉淀失败\n";
        }
    }

    /**
     * 测试与分润系统集成
     */
    private static function testBonusIntegration()
    {
        echo "\n--- 测试与分润系统集成 ---\n";
        
        // 查找一个真实的已支付订单进行测试
        $order = Db::name('shopro_order')
            ->where('status', 'paid')
            ->where('id', '>', 100) // 避免测试数据
            ->order('id', 'desc')
            ->find();
            
        if (!$order) {
            echo "✗ 没有找到合适的测试订单\n";
            return;
        }
        
        echo "使用订单ID: {$order['id']} 进行集成测试\n";
        
        // 检查订单是否包含奖金商品
        $bonus_items = Db::name('shopro_order_item oi')
            ->join('shopro_goods g', 'oi.goods_id = g.id')
            ->where('oi.order_id', $order['id'])
            ->where('g.is_bonus', 1)
            ->field('oi.*, g.title as goods_title, g.is_bonus')
            ->select();
            
        if (empty($bonus_items)) {
            echo "订单{$order['id']}不包含奖金商品，跳过集成测试\n";
            return;
        }
        
        echo "订单包含 " . count($bonus_items) . " 个奖金商品\n";
        
        // 获取积分池沉淀前余额
        $before_balance = ScorePool::getPoolBalance();
        
        // 模拟调用分润处理（只测试积分池部分）
        try {
            Db::startTrans();
            
            $result = ScorePool::processDeposit($order['id'], $bonus_items);
            
            if ($result) {
                echo "✓ 分润系统集成测试成功\n";
                
                $after_balance = ScorePool::getPoolBalance();
                $deposit_amount = $after_balance - $before_balance;
                echo "本次沉淀金额: {$deposit_amount}\n";
                
            } else {
                echo "✗ 分润系统集成测试失败\n";
            }
            
            // 回滚测试事务
            Db::rollback();
            echo "测试事务已回滚\n";
            
        } catch (Exception $e) {
            Db::rollback();
            echo "✗ 集成测试异常: " . $e->getMessage() . "\n";
        }
    }

    /**
     * 测试数据一致性
     */
    private static function testDataConsistency()
    {
        echo "\n--- 测试数据一致性 ---\n";
        
        $validation = ScorePool::validatePoolData();
        
        if ($validation['valid']) {
            echo "✓ 积分池数据一致性验证通过\n";
        } else {
            echo "✗ 积分池数据一致性验证失败\n";
            echo "积分池数据: " . json_encode($validation['pool_data']) . "\n";
            echo "实际数据: " . json_encode($validation['actual_data']) . "\n";
            echo "差异: " . json_encode($validation['differences']) . "\n";
        }
    }

    /**
     * 测试积分池日志查询
     */
    private static function testPoolLogs()
    {
        echo "\n--- 测试积分池日志查询 ---\n";
        
        $logs = ScorePool::getPoolLogs('default', [], 1, 5);
        
        echo "积分池日志总数: {$logs['total']}\n";
        echo "当前页记录数: " . count($logs['list']) . "\n";
        
        if (!empty($logs['list'])) {
            echo "最近的日志记录:\n";
            foreach (array_slice($logs['list'], 0, 3) as $log) {
                echo "  - 订单{$log['order_id']}: {$log['type']} {$log['deposit_amount']}元 ({$log['memo']})\n";
            }
        }
    }
}

// 执行测试
if (php_sapi_name() === 'cli') {
    ScorePoolTest::runAllTests();
} else {
    echo "请在命令行环境下运行此测试脚本\n";
}
