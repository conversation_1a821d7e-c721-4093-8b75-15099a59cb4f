<?php
/**
 * 积分池功能简化测试
 */

// 定义应用目录
define('APP_PATH', __DIR__ . '/application/');
// 定义根目录
define('ROOT_PATH', __DIR__ . '/');
// 定义目录分隔符
define('DS', DIRECTORY_SEPARATOR);

// 加载框架引导文件
require __DIR__ . '/thinkphp/start.php';

use addons\shopro\library\ScorePool;
use think\Db;

echo "=== 积分池功能测试 ===\n";

try {
    // 1. 测试获取积分池信息
    echo "\n1. 测试获取积分池信息\n";
    $pool = ScorePool::getPool();
    if ($pool) {
        echo "✓ 积分池信息获取成功\n";
        echo "  - 积分池名称: {$pool['pool_name']}\n";
        echo "  - 积分池余额: {$pool['total_balance']}\n";
        echo "  - 累计沉淀: {$pool['total_deposit']}\n";
        echo "  - 沉淀比例: " . ($pool['deposit_rate'] * 100) . "%\n";
    } else {
        echo "✗ 积分池信息获取失败\n";
    }

    // 2. 测试积分池沉淀功能
    echo "\n2. 测试积分池沉淀功能\n";
    
    // 模拟奖金商品数据
    $test_order_id = 999999;
    $bonus_items = [
        [
            'goods_id' => 17,
            'goods_title' => '测试奖金商品',
            'goods_num' => 1,
            'pay_fee' => 799.00
        ]
    ];
    
    // 获取沉淀前余额
    $before_balance = ScorePool::getPoolBalance();
    echo "沉淀前积分池余额: {$before_balance}\n";
    
    // 在事务中测试沉淀
    Db::startTrans();
    try {
        $result = ScorePool::processDeposit($test_order_id, $bonus_items);

        if ($result) {
            echo "✓ 积分池沉淀测试成功\n";

            // 在事务内查询余额变化
            $pool_data = Db::name('shopro_score_pool')->where('pool_code', 'default')->find();
            $after_balance = floatval($pool_data['total_balance']);
            $deposit_amount = $after_balance - $before_balance;
            $expected_deposit = 799.00 * 0.05; // 5%

            echo "沉淀后积分池余额: {$after_balance}\n";
            echo "实际沉淀金额: {$deposit_amount}\n";
            echo "预期沉淀金额: {$expected_deposit}\n";

            if (abs($deposit_amount - $expected_deposit) < 0.01) {
                echo "✓ 沉淀金额计算正确\n";
            } else {
                echo "✗ 沉淀金额计算错误\n";
            }

            // 检查日志记录
            $log_count = Db::name('shopro_score_pool_log')->where('order_id', $test_order_id)->count();
            echo "插入日志记录数: {$log_count}\n";

        } else {
            echo "✗ 积分池沉淀测试失败\n";
        }

        // 回滚测试事务
        Db::rollback();
        echo "测试事务已回滚\n";

    } catch (Exception $e) {
        Db::rollback();
        echo "✗ 沉淀测试异常: " . $e->getMessage() . "\n";
    }

    // 3. 测试积分池统计
    echo "\n3. 测试积分池统计\n";
    $stats = ScorePool::getPoolStats();
    if (!empty($stats)) {
        echo "✓ 积分池统计获取成功\n";
        echo "  - 总交易数: " . ($stats['total_transactions'] ?? 0) . "\n";
        echo "  - 总订单数: " . ($stats['total_orders'] ?? 0) . "\n";
        echo "  - 涉及商品数: " . ($stats['total_goods'] ?? 0) . "\n";
    } else {
        echo "✗ 积分池统计获取失败\n";
    }

    // 4. 测试数据一致性验证
    echo "\n4. 测试数据一致性验证\n";
    $validation = ScorePool::validatePoolData();
    if ($validation['valid']) {
        echo "✓ 积分池数据一致性验证通过\n";
    } else {
        echo "✗ 积分池数据一致性验证失败\n";
        echo "差异信息: " . json_encode($validation['differences']) . "\n";
    }

    // 5. 查看积分池日志
    echo "\n5. 查看积分池日志\n";
    $logs = ScorePool::getPoolLogs('default', [], 1, 5);
    echo "积分池日志总数: {$logs['total']}\n";
    
    if (!empty($logs['list'])) {
        echo "最近的日志记录:\n";
        foreach (array_slice($logs['list'], 0, 3) as $log) {
            $time = date('Y-m-d H:i:s', $log['createtime']);
            echo "  - [{$time}] 订单{$log['order_id']}: {$log['type']} {$log['deposit_amount']}元\n";
        }
    } else {
        echo "暂无积分池日志记录\n";
    }

    echo "\n=== 测试完成 ===\n";

} catch (Exception $e) {
    echo "测试异常: " . $e->getMessage() . "\n";
    echo "堆栈跟踪: " . $e->getTraceAsString() . "\n";
}
