<?php
/**
 * 测试首次购买升级逻辑
 */

// 引入ThinkPHP框架
define('APP_PATH', __DIR__ . '/application/');
require_once __DIR__ . '/thinkphp/start.php';

use think\Db;
use addons\shopro\library\Bonus;

try {
    echo "=== 测试首次购买升级逻辑 ===\n\n";
    
    // 确保用户100001是默认组
    Db::name('user')->where('id', 100001)->update(['group_id' => 1]);
    
    // 查看升级前的用户信息
    $user_before = Db::name('user')
        ->alias('u')
        ->join('user_group ug', 'u.group_id = ug.id', 'LEFT')
        ->where('u.id', 100001)
        ->field('u.id, u.username, u.nickname, u.group_id, ug.name as group_name')
        ->find();
    
    echo "升级前用户信息:\n";
    echo "- 用户ID: {$user_before['id']}\n";
    echo "- 用户名: {$user_before['username']}\n";
    echo "- 昵称: {$user_before['nickname']}\n";
    echo "- 等级组ID: {$user_before['group_id']}\n";
    echo "- 等级名称: {$user_before['group_name']}\n\n";
    
    // 执行分润处理（这会触发升级逻辑）
    echo "执行分润处理...\n";
    $result = Bonus::processOrderBonus(108, null);
    
    if ($result) {
        echo "分润处理成功！\n\n";
    } else {
        echo "分润处理失败！\n\n";
    }
    
    // 查看升级后的用户信息
    $user_after = Db::name('user')
        ->alias('u')
        ->join('user_group ug', 'u.group_id = ug.id', 'LEFT')
        ->where('u.id', 100001)
        ->field('u.id, u.username, u.nickname, u.group_id, ug.name as group_name')
        ->find();
    
    echo "升级后用户信息:\n";
    echo "- 用户ID: {$user_after['id']}\n";
    echo "- 用户名: {$user_after['username']}\n";
    echo "- 昵称: {$user_after['nickname']}\n";
    echo "- 等级组ID: {$user_after['group_id']}\n";
    echo "- 等级名称: {$user_after['group_name']}\n\n";
    
    // 验证升级结果
    if ($user_before['group_id'] == 1 && $user_after['group_id'] == 2) {
        echo "✅ 升级逻辑正确：默认组 → 创业销售商\n";
    } else {
        echo "❌ 升级逻辑错误：{$user_before['group_name']} → {$user_after['group_name']}\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
}
