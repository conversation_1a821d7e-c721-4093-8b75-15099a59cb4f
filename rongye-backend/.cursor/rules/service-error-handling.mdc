---
description:
globs:
alwaysApply: false
---
# 服务层错误处理规范

## 使用error_stop返回错误

在服务层中，如果需要返回错误信息，请使用`error_stop`方法而不是直接抛出异常或返回错误代码。

### 错误示例❌:

```php
public function doSomething($params)
{
    if (!$params['id']) {
        throw new \Exception('ID不能为空');
        // 或者
        // return ['code' => 0, 'msg' => 'ID不能为空'];
    }
    
    // 业务逻辑...
    return ['code' => 1, 'data' => $result];
}
```

### 正确示例✅:

```php
public function doSomething($params)
{
    if (!$params['id']) {
        return $this->error_stop('ID不能为空');
    }
    
    // 业务逻辑...
    return $result;
}
```

使用`error_stop`方法可以统一错误处理格式，简化代码并提高可维护性。
