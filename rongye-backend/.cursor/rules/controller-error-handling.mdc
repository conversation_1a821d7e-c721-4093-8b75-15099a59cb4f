---
description: 
globs: 
alwaysApply: false
---
# 控制器错误处理规范

## 禁止在控制器中使用try-catch

控制器不应包含try-catch块进行错误处理。应让异常向上传播到更高级别的错误处理机制。

## 使用标准响应方法

控制器中应使用`$this->success`和`$this->error`方法来返回响应，而不是直接使用json或其他方式。

### 错误示例❌:
```php
public function index()
{
    try {
        $result = $this->someService->doSomething();
        return json(['code' => 1, 'msg' => '操作成功', 'data' => $result]);
    } catch (\Exception $e) {
        return json(['code' => 0, 'msg' => $e->getMessage()]);
    }
}
```

或

```php
public function index()
{
    $result = $this->someService->doSomething();
    return json(['code' => 1, 'msg' => '操作成功', 'data' => $result]);
}
```

### 正确示例✅:
```php
public function index()
{
    $result = $this->someService->doSomething();
    return $this->success('操作成功', $result);
}
```

错误处理：

```php
public function index()
{
    if (!$this->request->isPost()) {
        return $this->error('请求方式错误');
    }
    
    $result = $this->someService->doSomething();
    return $this->success('操作成功', $result);
}
```

控制器应专注于处理请求和返回标准格式的响应，使用`$this->success`和`$this->error`方法可以保证响应格式的统一性。
