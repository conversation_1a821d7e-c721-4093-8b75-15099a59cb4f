<?php

// 使用ThinkPHP命令行模式
define('APP_PATH', __DIR__ . '/application/');
define('ROOT_PATH', __DIR__ . '/');
define('EXTEND_PATH', __DIR__ . '/extend/');
define('VENDOR_PATH', __DIR__ . '/vendor/');
define('RUNTIME_PATH', __DIR__ . '/runtime/');
define('LOG_PATH', __DIR__ . '/runtime/log/');
define('CACHE_PATH', __DIR__ . '/runtime/cache/');
define('TEMP_PATH', __DIR__ . '/runtime/temp/');
define('CONF_PATH', __DIR__ . '/application/');
define('CONF_EXT', '.php');
define('ENV_PREFIX', 'PHP_');
define('IS_CLI', true);

require_once __DIR__ . '/thinkphp/start.php';

echo "=== 测试分润功能修复 ===\n\n";

try {
    // 查看用户100001当前状态
    $user = Db::name('user')->where('id', 100001)->find();
    echo "修复前用户100001状态:\n";
    echo "- total_bonus_received: {$user['total_bonus_received']}\n";
    echo "- total_purchase_amount: {$user['total_purchase_amount']}\n";
    echo "- money: {$user['money']}\n";
    echo "- commission: {$user['commission']}\n\n";
    
    // 测试getUserBonusStats方法
    $bonusStats = Bonus::getUserBonusStats(100001);
    echo "getUserBonusStats结果:\n";
    echo "- total_bonus: {$bonusStats['total_bonus']}\n";
    echo "- total_score: {$bonusStats['total_score']}\n\n";
    
    // 手动调用checkRepurchaseRequirement方法
    echo "调用checkRepurchaseRequirement方法...\n";
    Bonus::checkRepurchaseRequirement(100001);
    
    // 查看修复后的状态
    $user_after = Db::name('user')->where('id', 100001)->find();
    echo "\n修复后用户100001状态:\n";
    echo "- total_bonus_received: {$user_after['total_bonus_received']}\n";
    echo "- total_purchase_amount: {$user_after['total_purchase_amount']}\n";
    echo "- money: {$user_after['money']}\n";
    echo "- commission: {$user_after['commission']}\n\n";
    
    // 检查钱包日志
    echo "用户100001的分润相关钱包日志:\n";
    $wallet_logs = Db::name('shopro_user_wallet_log')
        ->where('user_id', 100001)
        ->where('type', 'money')
        ->whereIn('event', ['order_buy_bonus', 'order_invite_bonus', 'order_team_bonus', 'order_level_bonus', 'order_ping_bonus', 'order_subsidy'])
        ->where('amount', '>', 0)
        ->order('createtime', 'desc')
        ->select();
    
    foreach ($wallet_logs as $log) {
        echo "- {$log['event']}: {$log['amount']}元 (订单ID: " . json_decode($log['ext'], true)['order_id'] . ")\n";
    }
    
    echo "\n=== 测试完成 ===\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
