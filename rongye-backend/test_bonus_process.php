<?php
/**
 * 测试订单106的分润处理
 */

// 引入ThinkPHP框架
define('APP_PATH', __DIR__ . '/application/');
require_once __DIR__ . '/thinkphp/start.php';

use think\Db;
use think\Log;
use addons\shopro\library\Bonus;

try {
    echo "=== 开始测试订单108的分润处理 ===\n\n";

    // 1. 检查订单108是否存在
    $order = Db::name('shopro_order')->where('id', 108)->find();
    if (!$order) {
        echo "错误：订单108不存在\n";
        exit(1);
    }
    
    echo "订单信息：\n";
    echo "- 订单ID: {$order['id']}\n";
    echo "- 用户ID: {$order['user_id']}\n";
    echo "- 订单金额: {$order['order_amount']}元\n";
    echo "- 订单状态: {$order['status']}\n\n";
    
    // 2. 获取购买用户信息
    $buyer = Db::name('user')->alias('u')
        ->join('user_group ug', 'u.group_id = ug.id', 'LEFT')
        ->where('u.id', $order['user_id'])
        ->field('u.*, ug.name as group_name')
        ->find();
    
    if (!$buyer) {
        echo "错误：购买用户不存在\n";
        exit(1);
    }
    
    echo "购买用户信息：\n";
    echo "- 用户ID: {$buyer['id']}\n";
    echo "- 用户名: {$buyer['username']}\n";
    echo "- 昵称: {$buyer['nickname']}\n";
    echo "- 等级: {$buyer['group_name']}\n";
    echo "- 上级用户ID: {$buyer['parent_user_id']}\n\n";
    
    // 3. 创建奖金商品和订单项（如果不存在）
    echo "检查并创建奖金商品...\n";
    
    // 检查是否已有奖金商品
    $bonusGoods = Db::name('shopro_goods')->where('is_bonus', 1)->find();
    if (!$bonusGoods) {
        // 创建奖金商品
        $goodsData = [
            'title' => '测试奖金商品',
            'image' => '/static/images/default.jpg',
            'price' => 799.00,
            'original_price' => 799.00,
            'is_bonus' => 1,
            'status' => 'up',
            'createtime' => time(),
            'updatetime' => time()
        ];
        
        $goodsId = Db::name('shopro_goods')->insertGetId($goodsData);
        echo "创建奖金商品成功，商品ID: {$goodsId}\n";
    } else {
        $goodsId = $bonusGoods['id'];
        echo "使用现有奖金商品，商品ID: {$goodsId}\n";
    }
    
    // 检查订单项是否存在
    $orderItem = Db::name('shopro_order_item')->where('order_id', 108)->find();
    if (!$orderItem) {
        // 创建订单项
        $itemData = [
            'order_id' => 108,
            'user_id' => $order['user_id'],
            'goods_id' => $goodsId,
            'goods_type' => 'normal',
            'goods_sku_price_id' => 0,
            'goods_sku_text' => '默认规格',
            'goods_title' => '测试奖金商品',
            'goods_image' => '/static/images/default.jpg',
            'goods_price' => 799.00,
            'goods_original_price' => 799.00,
            'goods_num' => 1,
            'goods_pay_price' => 799.00,
            'createtime' => time(),
            'updatetime' => time()
        ];
        
        Db::name('shopro_order_item')->insert($itemData);
        echo "创建订单项成功\n";
    } else {
        echo "订单项已存在\n";
    }
    
    echo "\n";
    
    // 4. 显示分润链
    echo "=== 分润链分析 ===\n";
    displayBonusChain($buyer['id']);
    echo "\n";
    
    // 5. 显示等级配置
    echo "=== 等级配置 ===\n";
    $userGroups = Db::name('user_group')->where('status', 'normal')->order('level_order', 'asc')->select();
    foreach ($userGroups as $group) {
        $directRate = $group['direct_sales_bonus_rate'] * 100;
        $teamRate = $group['team_bonus_rate'] * 100;
        echo "- {$group['name']} (等级{$group['level_order']}): 直销奖励{$directRate}%, 团队奖励{$teamRate}%\n";
    }
    echo "\n";
    
    // 6. 执行分润处理
    echo "=== 开始执行分润处理 ===\n";
    
    // 清除之前的分润记录（如果有）
    Db::name('shopro_user_wallet_log')
        ->where('ext', 'like', '%"order_id":108%')
        ->delete();

    echo "已清除之前的分润记录\n";

    // 调用分润处理
    try {
        $result = Bonus::processOrderBonus(108, null);
    } catch (Exception $e) {
        echo "分润处理异常: " . $e->getMessage() . "\n";
        echo "文件: " . $e->getFile() . "\n";
        echo "行号: " . $e->getLine() . "\n";
        $result = false;
    }
    
    if ($result) {
        echo "分润处理成功！\n\n";
        
        // 7. 显示分润结果
        echo "=== 分润结果 ===\n";
        displayBonusResults(108);
        
    } else {
        echo "分润处理失败！\n";

        // 显示错误日志
        $logs = Log::getLog();
        if (!empty($logs)) {
            echo "错误日志：\n";
            foreach ($logs as $level => $messages) {
                foreach ($messages as $message) {
                    echo "[$level] $message\n";
                }
            }
        }

        // 检查是否有异常
        echo "\n检查分润失败原因：\n";

        // 检查用户是否有上级
        if ($buyer['parent_user_id'] == 0) {
            echo "- 购买用户没有上级，无法发放直接销售奖励\n";
        }

        // 检查奖金商品
        $bonusAmount = Db::name('shopro_order_item')
            ->alias('oi')
            ->join('shopro_goods g', 'oi.goods_id = g.id', 'LEFT')
            ->where('oi.order_id', 108)
            ->where('g.is_bonus', 1)
            ->sum('oi.goods_price');

        echo "- 奖金商品总金额: {$bonusAmount}元\n";

        if ($bonusAmount <= 0) {
            echo "- 订单中没有奖金商品或奖金商品金额为0\n";
        }
    }
    
} catch (Exception $e) {
    echo "异常: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
}

/**
 * 显示分润链
 */
function displayBonusChain($userId) {
    $level = 0;
    $currentUserId = $userId;
    
    while ($currentUserId && $level < 10) {
        $user = Db::name('user')->alias('u')
            ->join('user_group ug', 'u.group_id = ug.id', 'LEFT')
            ->where('u.id', $currentUserId)
            ->field('u.*, ug.name as group_name, ug.direct_sales_bonus_rate, ug.team_bonus_rate')
            ->find();
        
        if (!$user) break;
        
        if ($level == 0) {
            echo "购买者: {$user['nickname']} ({$user['username']}) - {$user['group_name']}\n";
        } else {
            $directRate = $user['direct_sales_bonus_rate'] * 100;
            $teamRate = $user['team_bonus_rate'] * 100;
            echo "第{$level}级上级: {$user['nickname']} ({$user['username']}) - {$user['group_name']} (直销:{$directRate}%, 团队:{$teamRate}%)\n";
        }
        
        $currentUserId = $user['parent_user_id'];
        $level++;
    }
}

/**
 * 显示分润结果
 */
function displayBonusResults($orderId) {
    $bonusLogs = Db::name('shopro_user_wallet_log')
        ->alias('wl')
        ->join('user u', 'wl.user_id = u.id', 'LEFT')
        ->where('wl.ext', 'like', '%"order_id":' . $orderId . '%')
        ->field('wl.*, u.username, u.nickname')
        ->order('wl.id', 'asc')
        ->select();
    
    if (empty($bonusLogs)) {
        echo "没有找到分润记录\n";
        return;
    }
    
    $totalBonus = 0;
    foreach ($bonusLogs as $log) {
        $ext = json_decode($log['ext'], true);
        echo "- {$log['nickname']} ({$log['username']}): ";

        if ($log['type'] == 'money' && $log['amount'] > 0) {
            echo "现金 +{$log['amount']}元 ";
            $totalBonus += $log['amount'];
        } elseif ($log['type'] == 'score' && $log['amount'] > 0) {
            echo "积分 +{$log['amount']} ";
        } else {
            echo "金额 +{$log['amount']} ";
            $totalBonus += $log['amount'];
        }

        echo "({$log['memo']})\n";
    }
    
    echo "\n总分润金额: {$totalBonus}元\n";
    
    // 显示用户余额变化
    echo "\n=== 用户余额变化 ===\n";
    $users = Db::name('user')
        ->whereIn('id', array_column($bonusLogs, 'user_id'))
        ->field('id, username, nickname, money, score')
        ->select();
    
    foreach ($users as $user) {
        echo "- {$user['nickname']} ({$user['username']}): 余额 {$user['money']}元, 积分 {$user['score']}\n";
    }
}
