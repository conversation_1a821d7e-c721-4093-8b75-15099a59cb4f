<?php

namespace app\admin\controller\shopro;

use app\common\controller\Backend;

/**
 * 文旅景点报名记录
 *
 * @icon fa fa-circle-o
 */
class TourismRegistration extends Backend
{

    /**
     * TourismRegistration模型对象
     * @var \app\admin\model\shopro\TourismRegistration
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\shopro\TourismRegistration;
        $this->view->assign("statusList", $this->model->getStatusList());
    }



    /**
     * 确认报名
     * 管理员确认用户的文旅景点报名申请
     */
    public function confirm($ids = '')
    {
        // 获取报名记录ID
        $id = 0;
        if ($ids) {
            // 如果是多个ID，取第一个
            $idArray = explode(',', $ids);
            $id = intval($idArray[0]);
        }

        if (!$id) {
            $this->error('参数错误：报名记录ID不能为空');
        }

        // 获取当前操作员信息
        $operatorId = $this->auth->id;
        $operatorName = $this->auth->username;

        try {
            // 开启事务
            \think\Db::startTrans();

            // 查询报名记录
            $registration = $this->model->get($id);

            if (!$registration) {
                \think\Db::rollback();
                $this->error('报名记录不存在');
            }

            // 验证报名状态
            if ($registration['status'] != 1) {
                \think\Db::rollback();
                $statusText = $this->getStatusText($registration['status']);
                $this->error("当前报名状态为：{$statusText}，无法确认");
            }

            // 确保confirmtime字段存在
            $this->ensureConfirmtimeField();

            // 更新报名状态为已确认
            $updateData = [
                'status' => 2,
                'updatetime' => time(),
                'confirmtime' => time()
            ];

            $result = $registration->save($updateData);

            if (!$result) {
                \think\Db::rollback();
                $this->error('确认失败，请重试');
            }

            // 记录操作日志
            $this->logOperation($id, $registration, $operatorId, $operatorName);

            // 提交事务
            \think\Db::commit();

        } catch (\Exception $e) {
            // 回滚事务
            \think\Db::rollback();

            // 记录错误日志
            \think\Log::error('确认报名失败：' . $e->getMessage());

            $this->error('确认失败：' . $e->getMessage());
        }

        // 返回成功结果（放在try-catch外面）
        $this->success('确认成功');
    }

    /**
     * 取消报名
     * 管理员取消用户的文旅景点报名申请
     */
    public function cancel($ids = '')
    {
        // 获取报名记录ID
        $id = 0;
        if ($ids) {
            // 如果是多个ID，取第一个
            $idArray = explode(',', $ids);
            $id = intval($idArray[0]);
        }

        if (!$id) {
            $this->error('参数错误：报名记录ID不能为空');
        }

        // 获取当前操作员信息
        $operatorId = $this->auth->id;
        $operatorName = $this->auth->username;

        try {
            // 开启事务
            \think\Db::startTrans();

            // 查询报名记录
            $registration = $this->model->get($id);

            if (!$registration) {
                \think\Db::rollback();
                $this->error('报名记录不存在');
            }

            // 验证报名状态（只能取消待确认或已确认的记录）
            if (!in_array($registration['status'], [1, 2])) {
                \think\Db::rollback();
                $statusText = $this->getStatusText($registration['status']);
                $this->error("当前报名状态为：{$statusText}，无法取消");
            }

            // 确保canceltime字段存在
            $this->ensureCanceltimeField();

            // 更新报名状态为已取消
            $updateData = [
                'status' => 0,
                'updatetime' => time(),
                'canceltime' => time()
            ];

            $result = $registration->save($updateData);

            if (!$result) {
                \think\Db::rollback();
                $this->error('取消失败，请重试');
            }

            // 记录操作日志
            $this->logCancelOperation($id, $registration, $operatorId, $operatorName);

            // 提交事务
            \think\Db::commit();

        } catch (\Exception $e) {
            // 回滚事务
            \think\Db::rollback();

            // 记录错误日志
            \think\Log::error('取消报名失败：' . $e->getMessage());

            $this->error('取消失败：' . $e->getMessage());
        }

        // 返回成功结果（放在try-catch外面）
        $this->success('取消成功');
    }

    /**
     * 获取报名状态文本
     */
    private function getStatusText($status)
    {
        $statusMap = [
            0 => '已取消',
            1 => '待确认',
            2 => '已确认'
        ];

        return isset($statusMap[$status]) ? $statusMap[$status] : '未知状态';
    }

    /**
     * 确保confirmtime字段存在
     */
    private function ensureConfirmtimeField()
    {
        try {
            // 检查字段是否存在
            $columns = \think\Db::query("SHOW COLUMNS FROM " . config('database.prefix') . "shopro_tourism_registration LIKE 'confirmtime'");

            if (empty($columns)) {
                // 字段不存在，添加字段
                $sql = "ALTER TABLE " . config('database.prefix') . "shopro_tourism_registration ADD COLUMN confirmtime bigint(16) DEFAULT NULL COMMENT '确认时间'";
                \think\Db::execute($sql);
                \think\Log::info('已自动添加confirmtime字段');
            }
        } catch (\Exception $e) {
            \think\Log::error('检查/添加confirmtime字段失败：' . $e->getMessage());
            // 不抛出异常，继续执行
        }
    }

    /**
     * 确保canceltime字段存在
     */
    private function ensureCanceltimeField()
    {
        try {
            // 检查字段是否存在
            $columns = \think\Db::query("SHOW COLUMNS FROM " . config('database.prefix') . "shopro_tourism_registration LIKE 'canceltime'");

            if (empty($columns)) {
                // 字段不存在，添加字段
                $sql = "ALTER TABLE " . config('database.prefix') . "shopro_tourism_registration ADD COLUMN canceltime bigint(16) DEFAULT NULL COMMENT '取消时间'";
                \think\Db::execute($sql);
                \think\Log::info('已自动添加canceltime字段');
            }
        } catch (\Exception $e) {
            \think\Log::error('检查/添加canceltime字段失败：' . $e->getMessage());
            // 不抛出异常，继续执行
        }
    }

    /**
     * 记录操作日志
     */
    private function logOperation($registrationId, $registration, $operatorId, $operatorName)
    {
        try {
            $logData = [
                'type' => 'tourism_registration_confirm',
                'target_id' => $registrationId,
                'operator_id' => $operatorId,
                'operator_name' => $operatorName,
                'content' => "确认了报名申请：{$registration['name']} ({$registration['mobile']}) 报名景点ID：{$registration['spot_id']}",
                'ip' => $this->request->ip(),
                'user_agent' => $this->request->header('User-Agent'),
                'createtime' => time()
            ];

            \think\Log::info('报名确认操作日志：' . json_encode($logData));
        } catch (\Exception $e) {
            \think\Log::error('记录操作日志失败：' . $e->getMessage());
            // 不抛出异常，不影响主流程
        }
    }

    /**
     * 记录取消操作日志
     */
    private function logCancelOperation($registrationId, $registration, $operatorId, $operatorName)
    {
        try {
            $logData = [
                'type' => 'tourism_registration_cancel',
                'target_id' => $registrationId,
                'operator_id' => $operatorId,
                'operator_name' => $operatorName,
                'content' => "取消了报名申请：{$registration['name']} ({$registration['mobile']}) 报名景点ID：{$registration['spot_id']}",
                'ip' => $this->request->ip(),
                'user_agent' => $this->request->header('User-Agent'),
                'createtime' => time()
            ];

            \think\Log::info('报名取消操作日志：' . json_encode($logData));
        } catch (\Exception $e) {
            \think\Log::error('记录取消操作日志失败：' . $e->getMessage());
            // 不抛出异常，不影响主流程
        }
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


}
