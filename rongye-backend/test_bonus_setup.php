<?php
/**
 * 分润测试数据设置脚本
 * 1. 删除除了100001以外的所有用户
 * 2. 创建20个随机用户建立推荐关系
 * 3. 创建订单106测试分润
 */

// 数据库配置
$config = [
    'host' => '127.0.0.1',
    'port' => 3306,
    'username' => 'root',
    'password' => 'root',
    'database' => 'rongye',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "数据库连接成功\n";
    
    // 开始事务
    $pdo->beginTransaction();
    
    // 1. 删除除了100001以外的所有用户
    echo "正在删除除了100001以外的所有用户...\n";
    
    // 删除相关的钱包记录
    $pdo->exec("DELETE FROM sys_shopro_user_wallet_log WHERE user_id != 100001");
    $pdo->exec("DELETE FROM sys_user_money_log WHERE user_id != 100001");
    $pdo->exec("DELETE FROM sys_user_score_log WHERE user_id != 100001");
    
    // 删除用户相关记录
    $pdo->exec("DELETE FROM sys_shopro_user_address WHERE user_id != 100001");
    $pdo->exec("DELETE FROM sys_shopro_user_coupon WHERE user_id != 100001");
    $pdo->exec("DELETE FROM sys_shopro_user_goods_log WHERE user_id != 100001");
    $pdo->exec("DELETE FROM sys_shopro_user_level_log WHERE user_id != 100001");
    $pdo->exec("DELETE FROM sys_shopro_user_sales_stats WHERE user_id != 100001");
    
    // 删除订单相关记录
    $pdo->exec("DELETE FROM sys_shopro_order WHERE user_id != 100001");
    $pdo->exec("DELETE FROM sys_shopro_order_item WHERE user_id != 100001");
    
    // 删除用户
    $pdo->exec("DELETE FROM sys_user WHERE id != 100001");
    
    // 重置100001用户的parent_user_id
    $pdo->exec("UPDATE sys_user SET parent_user_id = 0 WHERE id = 100001");
    
    echo "用户删除完成\n";
    
    // 2. 创建20个随机用户
    echo "正在创建20个随机用户...\n";
    
    $userGroups = [
        2 => '创业销售商',  // entrepreneur
        3 => '拓业顾问',    // advisor  
        4 => '守业经理',    // manager
        5 => '享业董事',    // director
        6 => '智慧董事'     // wisdom_director
    ];
    
    $names = [
        '张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十',
        '郑一', '王二', '冯三', '陈四', '褚五', '卫六', '蒋七', '沈八',
        '韩九', '杨十', '朱一', '秦二', '尤三', '许四', '何五', '吕六'
    ];
    
    $users = [];
    $currentTime = time();
    
    // 创建用户数据
    for ($i = 1; $i <= 20; $i++) {
        $userId = 100001 + $i;
        $username = 'test' . str_pad($i, 3, '0', STR_PAD_LEFT);
        $nickname = $names[($i - 1) % count($names)] . '-' . $i;
        
        // 随机分配用户等级
        $groupId = array_rand($userGroups);
        
        // 建立推荐关系 - 让每个用户都有上级
        $parentUserId = 0;
        if ($i > 1) {
            // 随机选择一个已创建的用户作为上级
            $availableParents = array_keys($users);
            $availableParents[] = 100001; // 包括原始用户
            $parentUserId = $availableParents[array_rand($availableParents)];
        }
        
        $users[$userId] = [
            'id' => $userId,
            'username' => $username,
            'nickname' => $nickname,
            'group_id' => $groupId,
            'parent_user_id' => $parentUserId,
            'password' => md5('123456'),
            'salt' => substr(md5(uniqid()), 0, 6),
            'mobile' => '1' . str_pad($i, 10, '0', STR_PAD_LEFT),
            'status' => 'normal',
            'createtime' => $currentTime,
            'updatetime' => $currentTime,
            'jointime' => $currentTime
        ];
    }
    
    // 插入用户数据
    $insertSql = "INSERT INTO sys_user (
        id, group_id, username, nickname, password, salt, mobile, status, 
        parent_user_id, createtime, updatetime, jointime, money, score, commission
    ) VALUES (
        :id, :group_id, :username, :nickname, :password, :salt, :mobile, :status,
        :parent_user_id, :createtime, :updatetime, :jointime, 0.00, 0.00, 0.00
    )";
    
    $stmt = $pdo->prepare($insertSql);
    
    foreach ($users as $user) {
        $stmt->execute($user);
        echo "创建用户: {$user['username']} ({$user['nickname']}) - 等级: {$userGroups[$user['group_id']]} - 上级: {$user['parent_user_id']}\n";
    }
    
    echo "用户创建完成\n";
    
    // 3. 创建订单106测试分润
    echo "正在创建测试订单106...\n";
    
    // 随机选择一个用户作为购买者
    $buyerUserId = array_rand($users);
    $orderAmount = 799.00; // 测试金额
    
    $orderSql = "INSERT INTO sys_shopro_order (
        id, type, order_sn, user_id, status, order_amount, goods_amount, 
        goods_original_amount, createtime, updatetime, paid_time
    ) VALUES (
        106, 'goods', '202510160824609010000106', :user_id, 'paid', :order_amount, 
        :goods_amount, :goods_original_amount, :createtime, :updatetime, :paid_time
    )";
    
    $orderData = [
        'user_id' => $buyerUserId,
        'order_amount' => $orderAmount,
        'goods_amount' => $orderAmount,
        'goods_original_amount' => $orderAmount,
        'createtime' => $currentTime,
        'updatetime' => $currentTime,
        'paid_time' => $currentTime
    ];
    
    $stmt = $pdo->prepare($orderSql);
    $stmt->execute($orderData);
    
    echo "创建订单106成功 - 购买用户: {$buyerUserId} ({$users[$buyerUserId]['nickname']}) - 金额: {$orderAmount}元\n";
    
    // 提交事务
    $pdo->commit();
    echo "所有操作完成！\n\n";
    
    // 显示用户关系树
    echo "=== 用户关系树 ===\n";
    displayUserTree($pdo, 100001, 0);
    
    echo "\n=== 订单信息 ===\n";
    echo "订单ID: 106\n";
    echo "购买用户: {$users[$buyerUserId]['nickname']} (ID: {$buyerUserId})\n";
    echo "订单金额: {$orderAmount}元\n";
    echo "购买用户等级: {$userGroups[$users[$buyerUserId]['group_id']]}\n";
    
    // 显示分润链
    echo "\n=== 分润链 ===\n";
    displayBonusChain($pdo, $buyerUserId);
    
} catch (Exception $e) {
    if (isset($pdo)) {
        $pdo->rollback();
    }
    echo "错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
}

/**
 * 显示用户关系树
 */
function displayUserTree($pdo, $parentId, $level) {
    $stmt = $pdo->prepare("
        SELECT u.id, u.username, u.nickname, ug.name as group_name 
        FROM sys_user u 
        LEFT JOIN sys_user_group ug ON u.group_id = ug.id 
        WHERE u.parent_user_id = ? 
        ORDER BY u.id
    ");
    $stmt->execute([$parentId]);
    $children = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($children as $child) {
        echo str_repeat('  ', $level) . "├─ {$child['nickname']} ({$child['username']}) - {$child['group_name']}\n";
        displayUserTree($pdo, $child['id'], $level + 1);
    }
}

/**
 * 显示分润链
 */
function displayBonusChain($pdo, $userId) {
    $level = 0;
    $currentUserId = $userId;
    
    while ($currentUserId && $level < 10) {
        $stmt = $pdo->prepare("
            SELECT u.id, u.username, u.nickname, u.parent_user_id, ug.name as group_name 
            FROM sys_user u 
            LEFT JOIN sys_user_group ug ON u.group_id = ug.id 
            WHERE u.id = ?
        ");
        $stmt->execute([$currentUserId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) break;
        
        if ($level == 0) {
            echo "购买者: {$user['nickname']} ({$user['username']}) - {$user['group_name']}\n";
        } else {
            echo "第{$level}级上级: {$user['nickname']} ({$user['username']}) - {$user['group_name']}\n";
        }
        
        $currentUserId = $user['parent_user_id'];
        $level++;
    }
}
