<?php
/**
 * 积分池调试脚本
 */

// 定义应用目录
define('APP_PATH', __DIR__ . '/application/');
// 定义根目录
define('ROOT_PATH', __DIR__ . '/');
// 定义目录分隔符
define('DS', DIRECTORY_SEPARATOR);

// 加载框架引导文件
require __DIR__ . '/thinkphp/start.php';

use addons\shopro\library\ScorePool;
use think\Db;

echo "=== 积分池调试 ===\n";

try {
    // 1. 检查积分池表是否存在
    echo "\n1. 检查积分池表\n";
    $tables = Db::query("SHOW TABLES LIKE '%score_pool%'");
    foreach ($tables as $table) {
        echo "✓ 表存在: " . current($table) . "\n";
    }

    // 2. 检查积分池数据
    echo "\n2. 检查积分池数据\n";
    $pool = Db::name('shopro_score_pool')->where('pool_code', 'default')->find();
    if ($pool) {
        echo "✓ 积分池数据存在\n";
        echo "  - ID: {$pool['id']}\n";
        echo "  - 名称: {$pool['pool_name']}\n";
        echo "  - 余额: {$pool['total_balance']}\n";
        echo "  - 沉淀比例: {$pool['deposit_rate']}\n";
    } else {
        echo "✗ 积分池数据不存在\n";
    }

    // 3. 测试沉淀计算
    echo "\n3. 测试沉淀计算\n";
    $test_order_id = 999999;
    $bonus_items = [
        [
            'goods_id' => 17,
            'goods_title' => '测试奖金商品',
            'goods_num' => 1,
            'pay_fee' => 799.00
        ]
    ];

    echo "测试数据:\n";
    foreach ($bonus_items as $item) {
        echo "  - 商品ID: {$item['goods_id']}\n";
        echo "  - 商品名称: {$item['goods_title']}\n";
        echo "  - 商品数量: {$item['goods_num']}\n";
        echo "  - 支付金额: {$item['pay_fee']}\n";
        
        $expected_deposit = $item['pay_fee'] * 0.05;
        echo "  - 预期沉淀: {$expected_deposit}\n";
    }

    // 4. 详细测试沉淀过程
    echo "\n4. 详细测试沉淀过程\n";
    
    // 获取积分池信息
    $pool = ScorePool::getPool();
    if (!$pool) {
        echo "✗ 无法获取积分池信息\n";
        exit;
    }
    
    echo "积分池信息:\n";
    echo "  - ID: {$pool['id']}\n";
    echo "  - 沉淀比例: {$pool['deposit_rate']}\n";
    
    // 手动计算沉淀
    $total_deposit = 0;
    $deposit_logs = [];
    
    foreach ($bonus_items as $item) {
        $sales_amount = floatval($item['pay_fee']);
        $goods_num = intval($item['goods_num']);
        $deposit_amount = bcmul($sales_amount, $pool['deposit_rate'], 2);
        
        echo "计算过程:\n";
        echo "  - 销售金额: {$sales_amount}\n";
        echo "  - 沉淀比例: {$pool['deposit_rate']}\n";
        echo "  - 沉淀金额: {$deposit_amount}\n";
        
        if ($deposit_amount > 0) {
            $deposit_logs[] = [
                'pool_id' => $pool['id'],
                'order_id' => $test_order_id,
                'goods_id' => $item['goods_id'],
                'goods_title' => $item['goods_title'] ?? '',
                'goods_num' => $goods_num,
                'sales_amount' => $sales_amount,
                'deposit_rate' => $pool['deposit_rate'],
                'deposit_amount' => $deposit_amount,
                'type' => 'deposit',
                'memo' => "订单{$test_order_id}奖金商品沉淀",
                'oper_type' => 'system',
                'oper_id' => 0,
                'createtime' => time()
            ];
            
            $total_deposit = bcadd($total_deposit, $deposit_amount, 2);
        }
    }
    
    echo "总沉淀金额: {$total_deposit}\n";
    echo "沉淀日志数量: " . count($deposit_logs) . "\n";

    // 5. 在事务中测试实际沉淀
    echo "\n5. 在事务中测试实际沉淀\n";
    
    $before_balance = floatval($pool['total_balance']);
    echo "沉淀前余额: {$before_balance}\n";
    
    Db::startTrans();
    try {
        // 直接调用executeDeposit方法进行测试
        $reflection = new ReflectionClass('addons\shopro\library\ScorePool');
        $method = $reflection->getMethod('executeDeposit');
        $method->setAccessible(true);
        
        $result = $method->invokeArgs(null, [$pool['id'], $deposit_logs, $total_deposit]);
        
        if ($result) {
            echo "✓ executeDeposit 执行成功\n";
            
            // 检查数据库中的变化
            $updated_pool = Db::name('shopro_score_pool')->where('id', $pool['id'])->find();
            $after_balance = floatval($updated_pool['total_balance']);
            
            echo "沉淀后余额: {$after_balance}\n";
            echo "实际沉淀: " . ($after_balance - $before_balance) . "\n";
            
            // 检查日志记录
            $log_count = Db::name('shopro_score_pool_log')->where('order_id', $test_order_id)->count();
            echo "插入日志数量: {$log_count}\n";
            
        } else {
            echo "✗ executeDeposit 执行失败\n";
        }
        
        // 回滚事务
        Db::rollback();
        echo "事务已回滚\n";
        
    } catch (Exception $e) {
        Db::rollback();
        echo "✗ 测试异常: " . $e->getMessage() . "\n";
    }

    echo "\n=== 调试完成 ===\n";

} catch (Exception $e) {
    echo "调试异常: " . $e->getMessage() . "\n";
    echo "堆栈跟踪: " . $e->getTraceAsString() . "\n";
}
