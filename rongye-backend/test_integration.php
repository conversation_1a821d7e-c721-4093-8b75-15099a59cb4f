<?php
/**
 * 测试分润系统与积分池集成
 */

// 定义应用目录
define("APP_PATH", __DIR__ . "/application/");
// 定义根目录
define("ROOT_PATH", __DIR__ . "/");
// 定义目录分隔符
define("DS", DIRECTORY_SEPARATOR);

// 加载框架引导文件
require __DIR__ . "/thinkphp/start.php";

use addons\shopro\library\Bonus;
use addons\shopro\library\ScorePool;
use think\Db;

echo "=== 分润系统与积分池集成测试 ===\n";

try {
    // 查找一个包含奖金商品的订单
    echo "\n1. 查找测试订单\n";
    
    $order = Db::name("shopro_order o")
        ->join("shopro_order_item oi", "o.id = oi.order_id")
        ->join("shopro_goods g", "oi.goods_id = g.id")
        ->where("o.status", "paid")
        ->where("g.is_bonus", 1)
        ->where("o.id", ">", 100)
        ->field("o.*")
        ->order("o.id", "desc")
        ->find();
        
    if (!$order) {
        echo "✗ 没有找到合适的测试订单\n";
        exit;
    }
    
    echo "✓ 找到测试订单: {$order[\"id\"]}\n";
    echo "  - 订单金额: {$order[\"pay_fee\"]}\n";
    echo "  - 用户ID: {$order[\"user_id\"]}\n";
    
    // 获取订单中的奖金商品
    $bonus_items = Db::name("shopro_order_item oi")
        ->join("shopro_goods g", "oi.goods_id = g.id")
        ->where("oi.order_id", $order["id"])
        ->where("g.is_bonus", 1)
        ->field("oi.*, g.title as goods_title, g.is_bonus")
        ->select();
        
    echo "奖金商品数量: " . count($bonus_items) . "\n";
    
    $total_bonus_amount = 0;
    foreach ($bonus_items as $item) {
        $total_bonus_amount += $item["pay_fee"];
    }
    
    echo "奖金商品总金额: {$total_bonus_amount}元\n";
    echo "预期积分池沉淀: " . ($total_bonus_amount * 0.05) . "元\n";

    // 在事务中测试完整的分润处理
    echo "\n2. 测试完整分润处理\n";
    
    $before_pool_balance = ScorePool::getPoolBalance();
    echo "处理前积分池余额: {$before_pool_balance}\n";
    
    Db::startTrans();
    try {
        echo "开始执行分润处理...\n";
        
        $result = Bonus::processOrderBonus($order["id"]);
        
        if ($result) {
            echo "✓ 分润处理成功\n";
            
            // 检查积分池变化
            $pool_data = Db::name("shopro_score_pool")->where("pool_code", "default")->find();
            $after_pool_balance = floatval($pool_data["total_balance"]);
            $pool_deposit = $after_pool_balance - $before_pool_balance;
            
            echo "处理后积分池余额: {$after_pool_balance}\n";
            echo "积分池沉淀: {$pool_deposit}元\n";
            
            // 验证积分池沉淀
            $expected_deposit = $total_bonus_amount * 0.05;
            if (abs($pool_deposit - $expected_deposit) < 0.01) {
                echo "✓ 积分池沉淀金额正确\n";
            } else {
                echo "✗ 积分池沉淀金额错误 (预期: {$expected_deposit}, 实际: {$pool_deposit})\n";
            }
            
            // 检查积分池日志
            $pool_logs = Db::name("shopro_score_pool_log")
                ->where("order_id", $order["id"])
                ->count();
            echo "积分池日志记录数: {$pool_logs}\n";
            
        } else {
            echo "✗ 分润处理失败\n";
        }
        
        // 回滚事务
        Db::rollback();
        echo "测试事务已回滚\n";
        
    } catch (Exception $e) {
        Db::rollback();
        echo "✗ 分润处理异常: " . $e->getMessage() . "\n";
    }

    echo "\n=== 集成测试完成 ===\n";

} catch (Exception $e) {
    echo "测试异常: " . $e->getMessage() . "\n";
}
