<?php

namespace Yansongda\Pay\Gateways\Alipay;

class WapGateway extends WebGateway
{
    /**
     * Get method config.
     *
     * <AUTHOR> <<EMAIL>>
     */
    protected function getMethod(): string
    {
        return 'alipay.trade.wap.pay';
    }

    /**
     * Get productCode config.
     *
     * <AUTHOR> <<EMAIL>>
     */
    protected function getProductCode(): string
    {
        return 'QUICK_WAP_WAY';
    }
}
