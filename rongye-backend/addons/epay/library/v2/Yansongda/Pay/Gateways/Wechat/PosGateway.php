<?php

namespace Yansongda\Pay\Gateways\Wechat;

use Yansongda\Pay\Events;
use Yan<PERSON>gda\Pay\Exceptions\GatewayException;
use Yan<PERSON>gda\Pay\Exceptions\InvalidArgumentException;
use Yansongda\Pay\Exceptions\InvalidSignException;
use <PERSON>songda\Supports\Collection;

class PosGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * <AUTHOR> <<EMAIL>>
     *
     * @param string $endpoint
     *
     * @throws GatewayException
     * @throws InvalidArgumentException
     * @throws InvalidSignException
     */
    public function pay($endpoint, array $payload): Collection
    {
        unset($payload['trade_type'], $payload['notify_url']);

        $payload['sign'] = Support::generateSign($payload);

        Events::dispatch(new Events\PayStarted('Wechat', 'Pos', $endpoint, $payload));

        return Support::requestApi('pay/micropay', $payload);
    }

    /**
     * Get trade type config.
     *
     * <AUTHOR> <<EMAIL>>
     */
    protected function getTradeType(): string
    {
        return 'MICROPAY';
    }
}
