<?php

declare(strict_types=1);

namespace <PERSON>songda\Pay\Plugin\Alipay\Shortcut;

use <PERSON>songda\Pay\Contract\ShortcutInterface;
use <PERSON>songda\Pay\Plugin\Alipay\HtmlResponsePlugin;
use Yansongda\Pay\Plugin\Alipay\Trade\WapPayPlugin;

class WapShortcut implements ShortcutInterface
{
    public function getPlugins(array $params): array
    {
        return [
            WapPayPlugin::class,
            HtmlResponsePlugin::class,
        ];
    }
}
