{"name": "hyperf/context", "description": "A coroutine context library.", "license": "MIT", "keywords": ["php", "swoole", "hyperf", "context"], "homepage": "https://hyperf.io", "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "require": {"php": ">=7.2", "hyperf/engine": "^1.1"}, "autoload": {"psr-4": {"Hyperf\\Context\\": "src/"}}, "autoload-dev": {"psr-4": {"HyperfTest\\Context\\": "tests/"}}, "config": {"sort-packages": true}, "extra": {"branch-alias": {"dev-master": "2.2-dev"}}}