{"name": "hyperf/utils", "description": "A tools package that could help developer solved the problem quickly.", "license": "MIT", "keywords": ["php", "swoole", "hyperf", "utils"], "homepage": "https://hyperf.io", "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "require": {"php": ">=7.2", "doctrine/inflector": "^2.0", "hyperf/context": "~2.2.0", "hyperf/contract": "~2.2.0", "hyperf/engine": "^1.1", "hyperf/macroable": "~2.2.0"}, "suggest": {"ext-swoole": "Required to use methods related to swoole (>=4.5).", "symfony/var-dumper": "Required to use the dd function (^5.0).", "symfony/serializer": "Required to use SymfonyNormalizer (^5.0)", "symfony/property-access": "Required to use SymfonyNormalizer (^5.0)", "hyperf/di": "Required to use ExceptionNormalizer", "nikic/php-parser": "Required to use PhpParser. (^4.0)"}, "autoload": {"files": ["src/Functions.php"], "psr-4": {"Hyperf\\Utils\\": "src/"}}, "autoload-dev": {"psr-4": {"HyperfTest\\Utils\\": "tests/"}}, "config": {"sort-packages": true}, "extra": {"hyperf": {"config": "Hyperf\\Utils\\ConfigProvider"}, "branch-alias": {"dev-master": "2.2-dev"}}}