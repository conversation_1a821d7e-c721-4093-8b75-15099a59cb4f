{"name": "hyperf/engine", "type": "library", "license": "MIT", "keywords": ["php", "hyperf"], "description": "", "autoload": {"psr-4": {"Hyperf\\Engine\\": "src/"}}, "autoload-dev": {"psr-4": {"HyperfTest\\": "tests"}}, "require": {"php": ">=7.4"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.0", "hyperf/guzzle": "^2.2", "phpstan/phpstan": "^1.0", "phpunit/phpunit": "^9.4", "swoole/ide-helper": "dev-master"}, "suggest": {"ext-swoole": ">=4.5"}, "minimum-stability": "dev", "prefer-stable": true, "config": {"optimize-autoloader": true, "sort-packages": true}, "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "scripts": {"test": "phpunit -c phpunit.xml --colors=always", "analyse": "phpstan analyse --memory-limit 1024M -l 0 ./src", "cs-fix": "php-cs-fixer fix $1"}}