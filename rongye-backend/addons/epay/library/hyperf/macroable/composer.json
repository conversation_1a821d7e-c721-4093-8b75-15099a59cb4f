{"name": "hyperf/macroable", "description": "Hyperf Macroable package which come from illuminate/macroable", "license": "MIT", "keywords": ["php", "swoole", "hyperf", "macroable"], "homepage": "https://hyperf.io", "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "require": {"php": ">=7.3"}, "autoload": {"psr-4": {"Hyperf\\Macroable\\": "src/"}}, "autoload-dev": {"psr-4": {"HyperfTest\\Macroable\\": "tests/"}}, "config": {"sort-packages": true}, "extra": {"branch-alias": {"dev-master": "2.2-dev"}}}