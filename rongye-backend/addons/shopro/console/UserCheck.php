<?php

namespace addons\shopro\console;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Db;
use think\Log;
use addons\shopro\library\UserActiveManager;
use addons\shopro\library\BonusLimitManager;
use addons\shopro\service\Wallet;

/**
 * 用户状态监控控制台命令
 * 
 * 功能说明：
 * 1. 监控用户激活状态是否到期
 * 2. 监控用户是否达到收益上限
 * 3. 处理过期用户的激活状态
 * 4. 处理达到收益上限的用户
 * 5. 从资金池发放过期补偿金
 * 
 * <AUTHOR>
 * @version 1.0
 */
class UserCheck extends Command
{
    // 过期补偿金额
    const EXPIRE_COMPENSATION = 799;
    
    // 批量处理数量
    const BATCH_SIZE = 100;
    
    protected function configure()
    {
        $this->setName('shopro:user-check')
             ->setDescription('用户状态监控任务 - 检查激活状态和收益上限');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始执行用户状态监控任务...');
        
        $start_time = microtime(true);
        $total_processed = 0;
        $total_expired = 0;
        $total_limit_reached = 0;
        $total_compensated = 0;
        
        try {
            // 1. 检查并处理激活状态过期用户
            $output->writeln('1. 检查激活状态过期用户...');
            $expired_result = $this->processExpiredUsers($output);
            $total_expired = $expired_result['expired_count'];
            $total_compensated = $expired_result['compensated_count'];
            
            // 2. 检查并处理达到收益上限用户
            $output->writeln('2. 检查收益上限达到用户...');
            $limit_result = $this->processLimitReachedUsers($output);
            $total_limit_reached = $limit_result['limit_reached_count'];
            
            // 3. 处理过期补偿发放队列
            $output->writeln('3. 处理过期补偿发放队列...');
            $queue_result = $this->processCompensationQueue($output);
            
            $total_processed = $total_expired + $total_limit_reached;
            
        } catch (\Exception $e) {
            $output->writeln('<error>监控任务执行异常: ' . $e->getMessage() . '</error>');
            Log::error('用户状态监控任务异常: ' . $e->getMessage());
            return 1;
        }
        
        $end_time = microtime(true);
        $execution_time = round(($end_time - $start_time) * 1000, 2);
        
        // 输出执行结果
        $output->writeln('');
        $output->writeln('=== 监控任务执行完成 ===');
        $output->writeln("执行时间: {$execution_time}ms");
        $output->writeln("总处理用户数: {$total_processed}");
        $output->writeln("激活过期用户数: {$total_expired}");
        $output->writeln("收益上限达到用户数: {$total_limit_reached}");
        $output->writeln("过期补偿发放数: {$total_compensated}");
        $output->writeln("队列处理结果: {$queue_result['processed_count']}个补偿发放");
        
        // 记录监控日志
        Log::info('用户状态监控任务完成 - 执行时间:' . $execution_time . 'ms, 处理用户:' . $total_processed . ', 过期:' . $total_expired . ', 上限:' . $total_limit_reached);
        
        return 0;
    }
    
    /**
     * 处理激活状态过期用户
     * @param Output $output
     * @return array
     */
    private function processExpiredUsers(Output $output)
    {
        $current_time = time();
        $expired_count = 0;
        $compensated_count = 0;
        
        // 查找激活状态过期的用户
        $expired_users = Db::name('user')
            ->where('is_active', 1)
            ->where('active_expire_time', '>', 0)
            ->where('active_expire_time', '<=', $current_time)
            ->limit(self::BATCH_SIZE)
            ->select();
            
        if (empty($expired_users)) {
            $output->writeln('  没有发现激活状态过期的用户');
            return ['expired_count' => 0, 'compensated_count' => 0];
        }
        
        $output->writeln('  发现 ' . count($expired_users) . ' 个激活状态过期用户');
        
        foreach ($expired_users as $user) {
            Db::startTrans();
            try {
                // 计算本次激活的天数
                $active_days = 0;
                if ($user['active_start_time'] > 0 && $user['active_expire_time'] > $user['active_start_time']) {
                    $active_days = ceil(($user['active_expire_time'] - $user['active_start_time']) / 86400);
                }
                
                // 更新用户激活状态
                Db::name('user')->where('id', $user['id'])->update([
                    'is_active' => 0,
                    'total_active_days' => $user['total_active_days'] + $active_days
                ]);
                
                // 记录激活状态变更日志
                Db::name('user_active_log')->insert([
                    'user_id' => $user['id'],
                    'action_type' => 'expire',
                    'trigger_order_id' => 0,
                    'trigger_user_id' => 0,
                    'before_status' => 1,
                    'after_status' => 0,
                    'before_start_time' => $user['active_start_time'],
                    'after_start_time' => 0,
                    'before_end_time' => $user['active_expire_time'],
                    'after_end_time' => 0,
                    'active_days' => $active_days,
                    'memo' => "激活状态自动过期，本次激活{$active_days}天",
                    'oper_type' => 'system',
                    'oper_id' => 0,
                    'createtime' => time()
                ]);
                
                // 添加到过期补偿队列
                $compensation_added = $this->addToCompensationQueue($user['id'], self::EXPIRE_COMPENSATION);
                if ($compensation_added) {
                    $compensated_count++;
                }
                
                Db::commit();
                $expired_count++;
                
                $output->writeln("  - 用户{$user['id']}({$user['username']})激活状态已过期，激活{$active_days}天");
                
                // 清除缓存
                UserActiveManager::clearActiveCache($user['id']);
                
            } catch (\Exception $e) {
                Db::rollback();
                $output->writeln("  - 用户{$user['id']}处理失败: " . $e->getMessage());
                Log::error("处理过期用户{$user['id']}异常: " . $e->getMessage());
            }
        }
        
        return ['expired_count' => $expired_count, 'compensated_count' => $compensated_count];
    }
    
    /**
     * 处理达到收益上限用户
     * @param Output $output
     * @return array
     */
    private function processLimitReachedUsers(Output $output)
    {
        $limit_reached_count = 0;
        
        // 查找达到收益上限但未标记的用户
        $limit_users = Db::name('user')
            ->where('total_purchase_amount', '>', 0)
            ->where('bonus_limit_reached', 0)
            ->whereRaw('total_bonus_received >= total_purchase_amount * 10')
            ->limit(self::BATCH_SIZE)
            ->select();
            
        if (empty($limit_users)) {
            $output->writeln('  没有发现达到收益上限的用户');
            return ['limit_reached_count' => 0];
        }
        
        $output->writeln('  发现 ' . count($limit_users) . ' 个达到收益上限用户');
        
        foreach ($limit_users as $user) {
            Db::startTrans();
            try {
                $bonus_limit = $user['total_purchase_amount'] * BonusLimitManager::BONUS_LIMIT_RATIO;
                
                // 更新用户收益上限状态
                Db::name('user')->where('id', $user['id'])->update([
                    'bonus_limit_reached' => 1
                ]);
                
                // 记录收益上限变更日志
                Db::name('user_bonus_limit_log')->insert([
                    'user_id' => $user['id'],
                    'action_type' => BonusLimitManager::ACTION_LIMIT_REACHED,
                    'order_id' => 0,
                    'amount' => 0,
                    'before_purchase_amount' => $user['total_purchase_amount'],
                    'after_purchase_amount' => $user['total_purchase_amount'],
                    'before_bonus_amount' => $user['total_bonus_received'],
                    'after_bonus_amount' => $user['total_bonus_received'],
                    'bonus_limit' => $bonus_limit,
                    'limit_ratio' => BonusLimitManager::BONUS_LIMIT_RATIO,
                    'before_limit_status' => 0,
                    'after_limit_status' => 1,
                    'memo' => '系统监控：用户达到收益上限，停止分润',
                    'oper_type' => 'system',
                    'oper_id' => 0,
                    'createtime' => time()
                ]);
                
                Db::commit();
                $limit_reached_count++;
                
                $bonus_ratio = round($user['total_bonus_received'] / $user['total_purchase_amount'], 2);
                $output->writeln("  - 用户{$user['id']}({$user['username']})达到收益上限，收益倍数: {$bonus_ratio}倍");
                
                // 清除缓存
                BonusLimitManager::clearLimitCache($user['id']);
                UserActiveManager::clearActiveCache($user['id']);
                
            } catch (\Exception $e) {
                Db::rollback();
                $output->writeln("  - 用户{$user['id']}处理失败: " . $e->getMessage());
                Log::error("处理收益上限用户{$user['id']}异常: " . $e->getMessage());
            }
        }
        
        return ['limit_reached_count' => $limit_reached_count];
    }
    
    /**
     * 添加到过期补偿队列
     * @param int $user_id 用户ID
     * @param float $amount 补偿金额
     * @return bool
     */
    private function addToCompensationQueue($user_id, $amount)
    {
        try {
            // 检查是否已经在队列中
            $exists = Db::name('shopro_compensation_queue')
                ->where('user_id', $user_id)
                ->where('status', 'pending')
                ->find();
                
            if ($exists) {
                return false; // 已在队列中
            }
            
            // 添加到补偿队列
            Db::name('shopro_compensation_queue')->insert([
                'user_id' => $user_id,
                'amount' => $amount,
                'type' => 'expire_compensation',
                'status' => 'pending',
                'memo' => '激活过期补偿',
                'createtime' => time(),
                'updatetime' => time()
            ]);
            
            return true;
            
        } catch (\Exception $e) {
            Log::error("添加过期补偿队列异常: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 处理过期补偿发放队列
     * @param Output $output
     * @return array
     */
    private function processCompensationQueue(Output $output)
    {
        $processed_count = 0;
        
        // 获取资金池余额
        $score_pool = Db::name('shopro_score_pool')->where('pool_code', 'default')->find();
        if (!$score_pool || $score_pool['total_balance'] <= 0) {
            $output->writeln('  资金池余额不足，跳过补偿发放');
            return ['processed_count' => 0];
        }

        $available_balance = floatval($score_pool['total_balance']);
        $output->writeln("  资金池可用余额: {$available_balance}元");
        
        // 获取待处理的补偿队列（按创建时间排序）
        $queue_items = Db::name('shopro_compensation_queue')
            ->where('status', 'pending')
            ->where('amount', '<=', $available_balance)
            ->order('createtime', 'asc')
            ->limit(self::BATCH_SIZE)
            ->select();
            
        if (empty($queue_items)) {
            $output->writeln('  没有可处理的补偿队列项目');
            return ['processed_count' => 0];
        }
        
        $output->writeln('  发现 ' . count($queue_items) . ' 个可处理的补偿项目');
        
        foreach ($queue_items as $item) {
            // 检查资金池余额是否足够
            if ($available_balance < $item['amount']) {
                $output->writeln("  资金池余额不足，停止处理补偿队列");
                break;
            }
            
            Db::startTrans();
            try {
                // 发放补偿金
                Wallet::change($item['user_id'], 'money', $item['amount'], 'expire_compensation',
                    ['queue_id' => $item['id']], $item['memo']);
                
                // 从资金池扣除
                Db::name('shopro_score_pool')->where('pool_code', 'default')->setDec('total_balance', $item['amount']);
                
                // 更新队列状态
                Db::name('shopro_compensation_queue')->where('id', $item['id'])->update([
                    'status' => 'completed',
                    'processed_time' => time(),
                    'updatetime' => time()
                ]);
                
                Db::commit();
                $processed_count++;
                $available_balance -= $item['amount'];
                
                $output->writeln("  - 用户{$item['user_id']}补偿发放成功: {$item['amount']}元");
                
            } catch (\Exception $e) {
                Db::rollback();
                $output->writeln("  - 用户{$item['user_id']}补偿发放失败: " . $e->getMessage());
                Log::error("处理补偿队列{$item['id']}异常: " . $e->getMessage());
                
                // 标记为失败
                Db::name('shopro_compensation_queue')->where('id', $item['id'])->update([
                    'status' => 'failed',
                    'error_msg' => $e->getMessage(),
                    'updatetime' => time()
                ]);
            }
        }
        
        return ['processed_count' => $processed_count];
    }
}
