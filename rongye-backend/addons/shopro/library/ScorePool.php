<?php

namespace addons\shopro\library;

use think\Db;
use think\Log;
use think\Exception;

/**
 * 积分池管理类
 * 负责积分池的创建、沉淀、提取和管理功能
 */
class ScorePool
{
    // 积分池状态常量
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';
    
    // 操作类型常量
    const TYPE_DEPOSIT = 'deposit';      // 沉淀
    const TYPE_WITHDRAW = 'withdraw';    // 提取
    const TYPE_ADJUST = 'adjust';        // 调整
    
    // 操作人类型常量
    const OPER_SYSTEM = 'system';        // 系统操作
    const OPER_ADMIN = 'admin';          // 管理员操作
    
    // 默认积分池代码
    const DEFAULT_POOL_CODE = 'default';
    
    // 默认沉淀比例 5%
    const DEFAULT_DEPOSIT_RATE = 0.05;

    /**
     * 获取积分池信息
     * @param string $pool_code 积分池代码
     * @return array|null
     */
    public static function getPool($pool_code = self::DEFAULT_POOL_CODE)
    {
        static $pools = [];
        
        if (!isset($pools[$pool_code])) {
            $pool = Db::name('shopro_score_pool')
                ->where('pool_code', $pool_code)
                ->where('status', self::STATUS_ACTIVE)
                ->find();
            
            $pools[$pool_code] = $pool ?: null;
        }
        
        return $pools[$pool_code];
    }

    /**
     * 创建积分池
     * @param array $data 积分池数据
     * @return int 积分池ID
     */
    public static function createPool($data)
    {
        $defaultData = [
            'pool_name' => '默认积分池',
            'pool_code' => self::DEFAULT_POOL_CODE,
            'total_balance' => 0.00,
            'total_deposit' => 0.00,
            'total_withdraw' => 0.00,
            'deposit_rate' => self::DEFAULT_DEPOSIT_RATE,
            'status' => self::STATUS_ACTIVE,
            'description' => '',
            'createtime' => time(),
            'updatetime' => time()
        ];
        
        $data = array_merge($defaultData, $data);
        
        return Db::name('shopro_score_pool')->insertGetId($data);
    }

    /**
     * 积分池沉淀处理（主要方法）
     * @param int $order_id 订单ID
     * @param array $bonus_items 奖金商品列表
     * @param string $pool_code 积分池代码
     * @return bool
     */
    public static function processDeposit($order_id, $bonus_items, $pool_code = self::DEFAULT_POOL_CODE)
    {
        try {
            // 获取积分池信息
            $pool = self::getPool($pool_code);
            if (!$pool) {
                Log::error("积分池不存在或已停用: {$pool_code}");
                return false;
            }

            $total_deposit = 0;
            $deposit_logs = [];

            // 计算每个奖金商品的沉淀金额
            foreach ($bonus_items as $item) {
                $sales_amount = floatval($item['pay_fee']);
                $goods_num = intval($item['goods_num']);
                $deposit_amount = bcmul($sales_amount, $pool['deposit_rate'], 2);
                
                if ($deposit_amount > 0) {
                    $deposit_logs[] = [
                        'pool_id' => $pool['id'],
                        'order_id' => $order_id,
                        'goods_id' => $item['goods_id'],
                        'goods_title' => $item['goods_title'] ?? '',
                        'goods_num' => $goods_num,
                        'sales_amount' => $sales_amount,
                        'deposit_rate' => $pool['deposit_rate'],
                        'deposit_amount' => $deposit_amount,
                        'type' => self::TYPE_DEPOSIT,
                        'memo' => "订单{$order_id}奖金商品沉淀",
                        'oper_type' => self::OPER_SYSTEM,
                        'oper_id' => 0,
                        'createtime' => time()
                    ];
                    
                    $total_deposit = bcadd($total_deposit, $deposit_amount, 2);
                }
            }

            if ($total_deposit <= 0) {
                Log::info("订单{$order_id}无需积分池沉淀");
                return true;
            }

            // 执行积分池沉淀
            return self::executeDeposit($pool['id'], $deposit_logs, $total_deposit);

        } catch (\Exception $e) {
            Log::error("积分池沉淀处理失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 执行积分池沉淀
     * @param int $pool_id 积分池ID
     * @param array $deposit_logs 沉淀日志数组
     * @param float $total_deposit 总沉淀金额
     * @return bool
     */
    private static function executeDeposit($pool_id, $deposit_logs, $total_deposit)
    {
        try {
            // 获取当前积分池余额
            $pool = Db::name('shopro_score_pool')->where('id', $pool_id)->find();
            if (!$pool) {
                throw new Exception("积分池不存在: {$pool_id}");
            }

            $before_balance = floatval($pool['total_balance']);
            $after_balance = bcadd($before_balance, $total_deposit, 2);

            // 更新每条日志的余额信息
            $current_balance = $before_balance;
            foreach ($deposit_logs as &$log) {
                $log['before_balance'] = $current_balance;
                $current_balance = bcadd($current_balance, $log['deposit_amount'], 2);
                $log['after_balance'] = $current_balance;
            }

            // 批量插入沉淀日志
            Db::name('shopro_score_pool_log')->insertAll($deposit_logs);

            // 更新积分池余额
            $update_data = [
                'total_balance' => $after_balance,
                'total_deposit' => bcadd($pool['total_deposit'], $total_deposit, 2),
                'updatetime' => time()
            ];
            
            Db::name('shopro_score_pool')->where('id', $pool_id)->update($update_data);

            Log::info("积分池沉淀成功: 积分池ID={$pool_id}, 沉淀金额={$total_deposit}, 余额={$before_balance}->{$after_balance}");
            return true;

        } catch (\Exception $e) {
            Log::error("执行积分池沉淀失败: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 获取积分池余额
     * @param string $pool_code 积分池代码
     * @return float
     */
    public static function getPoolBalance($pool_code = self::DEFAULT_POOL_CODE)
    {
        $pool = self::getPool($pool_code);
        return $pool ? floatval($pool['total_balance']) : 0.00;
    }

    /**
     * 获取积分池统计信息
     * @param string $pool_code 积分池代码
     * @return array
     */
    public static function getPoolStats($pool_code = self::DEFAULT_POOL_CODE)
    {
        $pool = self::getPool($pool_code);
        if (!$pool) {
            return [];
        }

        // 获取统计信息
        $stats = Db::name('shopro_score_pool_log')
            ->where('pool_id', $pool['id'])
            ->where('type', self::TYPE_DEPOSIT)
            ->field([
                'COUNT(*) as total_transactions',
                'COUNT(DISTINCT order_id) as total_orders',
                'COUNT(DISTINCT goods_id) as total_goods',
                'SUM(goods_num) as total_goods_count',
                'SUM(deposit_amount) as total_deposit_amount'
            ])
            ->find();

        return array_merge($pool, $stats ?: []);
    }

    /**
     * 获取积分池日志
     * @param string $pool_code 积分池代码
     * @param array $where 查询条件
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array
     */
    public static function getPoolLogs($pool_code = self::DEFAULT_POOL_CODE, $where = [], $page = 1, $limit = 20)
    {
        $pool = self::getPool($pool_code);
        if (!$pool) {
            return ['list' => [], 'total' => 0];
        }

        $query = Db::name('shopro_score_pool_log')
            ->where('pool_id', $pool['id']);

        // 添加查询条件
        if (!empty($where['order_id'])) {
            $query->where('order_id', $where['order_id']);
        }
        if (!empty($where['type'])) {
            $query->where('type', $where['type']);
        }
        if (!empty($where['start_time'])) {
            $query->where('createtime', '>=', $where['start_time']);
        }
        if (!empty($where['end_time'])) {
            $query->where('createtime', '<=', $where['end_time']);
        }

        $total = $query->count();
        $list = $query->order('createtime', 'desc')
            ->page($page, $limit)
            ->select();

        return [
            'list' => $list ?: [],
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];
    }

    /**
     * 积分池提取（预留接口）
     * @param float $amount 提取金额
     * @param string $memo 备注
     * @param string $pool_code 积分池代码
     * @param int $oper_id 操作人ID
     * @return bool
     */
    public static function withdraw($amount, $memo = '', $pool_code = self::DEFAULT_POOL_CODE, $oper_id = 0)
    {
        try {
            $pool = self::getPool($pool_code);
            if (!$pool) {
                throw new Exception("积分池不存在: {$pool_code}");
            }

            $amount = floatval($amount);
            if ($amount <= 0) {
                throw new Exception("提取金额必须大于0");
            }

            if ($amount > $pool['total_balance']) {
                throw new Exception("提取金额超过积分池余额");
            }

            $before_balance = floatval($pool['total_balance']);
            $after_balance = bcsub($before_balance, $amount, 2);

            // 记录提取日志
            $log_data = [
                'pool_id' => $pool['id'],
                'order_id' => 0,
                'goods_id' => 0,
                'goods_title' => '',
                'goods_num' => 0,
                'sales_amount' => 0.00,
                'deposit_rate' => 0.0000,
                'deposit_amount' => -$amount, // 负数表示提取
                'before_balance' => $before_balance,
                'after_balance' => $after_balance,
                'type' => self::TYPE_WITHDRAW,
                'memo' => $memo ?: '积分池提取',
                'oper_type' => self::OPER_ADMIN,
                'oper_id' => $oper_id,
                'createtime' => time()
            ];

            Db::name('shopro_score_pool_log')->insert($log_data);

            // 更新积分池余额
            $update_data = [
                'total_balance' => $after_balance,
                'total_withdraw' => bcadd($pool['total_withdraw'], $amount, 2),
                'updatetime' => time()
            ];

            Db::name('shopro_score_pool')->where('id', $pool['id'])->update($update_data);

            Log::info("积分池提取成功: 积分池ID={$pool['id']}, 提取金额={$amount}, 余额={$before_balance}->{$after_balance}");
            return true;

        } catch (\Exception $e) {
            Log::error("积分池提取失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 验证积分池数据一致性
     * @param string $pool_code 积分池代码
     * @return array 验证结果
     */
    public static function validatePoolData($pool_code = self::DEFAULT_POOL_CODE)
    {
        $pool = self::getPool($pool_code);
        if (!$pool) {
            return ['valid' => false, 'message' => '积分池不存在'];
        }

        // 计算日志中的实际金额
        $log_stats = Db::name('shopro_score_pool_log')
            ->where('pool_id', $pool['id'])
            ->field([
                'SUM(CASE WHEN type = "deposit" THEN deposit_amount ELSE 0 END) as actual_deposit',
                'SUM(CASE WHEN type = "withdraw" THEN ABS(deposit_amount) ELSE 0 END) as actual_withdraw'
            ])
            ->find();

        $actual_deposit = floatval($log_stats['actual_deposit'] ?? 0);
        $actual_withdraw = floatval($log_stats['actual_withdraw'] ?? 0);
        $actual_balance = bcsub($actual_deposit, $actual_withdraw, 2);

        $pool_deposit = floatval($pool['total_deposit']);
        $pool_withdraw = floatval($pool['total_withdraw']);
        $pool_balance = floatval($pool['total_balance']);

        $deposit_diff = bcsub($actual_deposit, $pool_deposit, 2);
        $withdraw_diff = bcsub($actual_withdraw, $pool_withdraw, 2);
        $balance_diff = bcsub($actual_balance, $pool_balance, 2);

        $is_valid = (abs($deposit_diff) < 0.01 && abs($withdraw_diff) < 0.01 && abs($balance_diff) < 0.01);

        return [
            'valid' => $is_valid,
            'pool_data' => [
                'deposit' => $pool_deposit,
                'withdraw' => $pool_withdraw,
                'balance' => $pool_balance
            ],
            'actual_data' => [
                'deposit' => $actual_deposit,
                'withdraw' => $actual_withdraw,
                'balance' => $actual_balance
            ],
            'differences' => [
                'deposit' => $deposit_diff,
                'withdraw' => $withdraw_diff,
                'balance' => $balance_diff
            ]
        ];
    }
}
