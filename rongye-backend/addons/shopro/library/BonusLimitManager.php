<?php

namespace addons\shopro\library;

use think\Db;
use think\Log;

/**
 * 收益上限控制管理类
 * 
 * 功能说明：
 * 1. 管理用户的收益上限控制
 * 2. 防止用户获得超过购买金额10倍的收益
 * 3. 记录购买金额和收益金额的变更
 * 4. 提供收益上限状态查询和统计
 * 
 * <AUTHOR>
 * @version 1.0
 */
class BonusLimitManager
{
    // 收益上限倍数
    const BONUS_LIMIT_RATIO = 10;
    
    // 操作类型常量
    const ACTION_PURCHASE = 'purchase';           // 购买记录
    const ACTION_BONUS_RECEIVED = 'bonus_received'; // 收益记录
    const ACTION_LIMIT_REACHED = 'limit_reached';   // 达到上限
    const ACTION_LIMIT_RESET = 'limit_reset';       // 重置上限
    
    // 上限状态描述
    const STATUS_NORMAL = 'normal';           // 正常状态
    const STATUS_HALF_LIMIT = 'half_limit';   // 达到一半上限
    const STATUS_NEAR_LIMIT = 'near_limit';   // 接近上限
    const STATUS_LIMIT_REACHED = 'limit_reached'; // 达到上限
    const STATUS_NO_PURCHASE = 'no_purchase'; // 无购买记录
    
    // 缓存
    private static $limitStatusCache = [];
    
    /**
     * 检查用户是否可以获得分润（收益上限检查）
     * @param int $user_id 用户ID
     * @param bool $use_cache 是否使用缓存
     * @return bool
     */
    public static function canReceiveBonus($user_id, $use_cache = true)
    {
        if ($use_cache && isset(self::$limitStatusCache[$user_id])) {
            return self::$limitStatusCache[$user_id];
        }
        
        $user = Db::name('user')->where('id', $user_id)->find();
        if (!$user) {
            return false;
        }
        
        // 如果已标记达到上限，直接返回false
        if ($user['bonus_limit_reached'] == 1) {
            self::$limitStatusCache[$user_id] = false;
            return false;
        }
        
        // 如果没有购买记录，不能获得分润
        if ($user['total_purchase_amount'] <= 0) {
            self::$limitStatusCache[$user_id] = false;
            return false;
        }
        
        // 检查是否超过收益上限
        $bonus_limit = $user['total_purchase_amount'] * self::BONUS_LIMIT_RATIO;
        $can_receive = $user['total_bonus_received'] < $bonus_limit;
        
        self::$limitStatusCache[$user_id] = $can_receive;
        return $can_receive;
    }
    
    /**
     * 记录用户购买金额
     * @param int $user_id 用户ID
     * @param float $amount 购买金额
     * @param int $order_id 订单ID
     * @return bool
     */
    public static function recordPurchaseAmount($user_id, $amount, $order_id = 0)
    {
        try {
            $user = Db::name('user')->where('id', $user_id)->find();
            if (!$user) {
                return false;
            }
            
            $before_purchase = floatval($user['total_purchase_amount']);
            $before_bonus = floatval($user['total_bonus_received']);
            $before_limit_status = intval($user['bonus_limit_reached']);
            
            $after_purchase = $before_purchase + $amount;
            $after_bonus = $before_bonus;
            
            // 更新购买金额
            Db::name('user')->where('id', $user_id)->update([
                'total_purchase_amount' => $after_purchase
            ]);
            
            // 重新检查收益上限状态
            $new_bonus_limit = $after_purchase * self::BONUS_LIMIT_RATIO;
            $after_limit_status = ($after_bonus >= $new_bonus_limit) ? 1 : 0;
            
            // 如果上限状态发生变化，更新状态
            if ($before_limit_status != $after_limit_status) {
                Db::name('user')->where('id', $user_id)->update([
                    'bonus_limit_reached' => $after_limit_status
                ]);
            }
            
            // 记录日志
            self::logLimitChange(
                $user_id,
                self::ACTION_PURCHASE,
                $order_id,
                $amount,
                $before_purchase,
                $after_purchase,
                $before_bonus,
                $after_bonus,
                $new_bonus_limit,
                $before_limit_status,
                $after_limit_status,
                "记录购买金额：{$amount}元"
            );
            
            // 清除缓存
            unset(self::$limitStatusCache[$user_id]);
            
            return true;
            
        } catch (\Exception $e) {
            Log::error("记录购买金额异常: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 记录用户收益金额
     * @param int $user_id 用户ID
     * @param float $amount 收益金额
     * @param int $order_id 订单ID
     * @param string $bonus_type 分润类型
     * @return bool
     */
    public static function recordBonusAmount($user_id, $amount, $order_id = 0, $bonus_type = '')
    {
        try {
            $user = Db::name('user')->where('id', $user_id)->find();
            if (!$user) {
                return false;
            }
            
            $before_purchase = floatval($user['total_purchase_amount']);
            $before_bonus = floatval($user['total_bonus_received']);
            $before_limit_status = intval($user['bonus_limit_reached']);
            
            $after_purchase = $before_purchase;
            $after_bonus = $before_bonus + $amount;
            
            // 更新收益金额
            Db::name('user')->where('id', $user_id)->update([
                'total_bonus_received' => $after_bonus
            ]);
            
            // 检查是否达到收益上限
            $bonus_limit = $before_purchase * self::BONUS_LIMIT_RATIO;
            $after_limit_status = ($after_bonus >= $bonus_limit) ? 1 : 0;
            
            // 如果达到上限，更新状态
            if ($before_limit_status != $after_limit_status && $after_limit_status == 1) {
                Db::name('user')->where('id', $user_id)->update([
                    'bonus_limit_reached' => 1
                ]);
                
                Log::info("用户{$user_id}达到收益上限，累计购买：{$before_purchase}元，累计收益：{$after_bonus}元");
            }
            
            // 记录日志
            $memo = "记录收益金额：{$amount}元";
            if ($bonus_type) {
                $memo .= "（{$bonus_type}）";
            }
            
            self::logLimitChange(
                $user_id,
                self::ACTION_BONUS_RECEIVED,
                $order_id,
                $amount,
                $before_purchase,
                $after_purchase,
                $before_bonus,
                $after_bonus,
                $bonus_limit,
                $before_limit_status,
                $after_limit_status,
                $memo
            );
            
            // 如果达到上限，记录达到上限的日志
            if ($before_limit_status != $after_limit_status && $after_limit_status == 1) {
                self::logLimitChange(
                    $user_id,
                    self::ACTION_LIMIT_REACHED,
                    $order_id,
                    0,
                    $before_purchase,
                    $after_purchase,
                    $before_bonus,
                    $after_bonus,
                    $bonus_limit,
                    $before_limit_status,
                    $after_limit_status,
                    "用户达到收益上限，停止分润"
                );
            }
            
            // 清除缓存
            unset(self::$limitStatusCache[$user_id]);
            
            return true;
            
        } catch (\Exception $e) {
            Log::error("记录收益金额异常: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 获取用户收益上限状态详情
     * @param int $user_id 用户ID
     * @return array|null
     */
    public static function getUserLimitStatus($user_id)
    {
        $user = Db::name('user')->where('id', $user_id)->find();
        if (!$user) {
            return null;
        }
        
        $total_purchase = floatval($user['total_purchase_amount']);
        $total_bonus = floatval($user['total_bonus_received']);
        $limit_reached = intval($user['bonus_limit_reached']);
        
        $bonus_limit = $total_purchase * self::BONUS_LIMIT_RATIO;
        $bonus_ratio = $total_purchase > 0 ? round($total_bonus / $total_purchase, 2) : 0;
        $remaining_limit = max(0, $bonus_limit - $total_bonus);
        
        // 确定状态描述
        $status_desc = self::STATUS_NORMAL;
        if ($limit_reached == 1) {
            $status_desc = self::STATUS_LIMIT_REACHED;
        } elseif ($total_purchase == 0) {
            $status_desc = self::STATUS_NO_PURCHASE;
        } elseif ($total_bonus >= $total_purchase * 9) {
            $status_desc = self::STATUS_NEAR_LIMIT;
        } elseif ($total_bonus >= $total_purchase * 5) {
            $status_desc = self::STATUS_HALF_LIMIT;
        }
        
        return [
            'user_id' => $user_id,
            'username' => $user['username'],
            'nickname' => $user['nickname'],
            'total_purchase_amount' => $total_purchase,
            'total_bonus_received' => $total_bonus,
            'bonus_limit_reached' => $limit_reached,
            'bonus_limit' => $bonus_limit,
            'bonus_ratio' => $bonus_ratio,
            'remaining_limit' => $remaining_limit,
            'status_desc' => $status_desc,
            'can_receive_bonus' => ($total_purchase > 0 && $total_bonus < $bonus_limit)
        ];
    }
    
    /**
     * 获取收益上限统计信息
     * @return array
     */
    public static function getLimitStats()
    {
        $stats = Db::name('user')
            ->field([
                'COUNT(*) as total_users',
                'SUM(CASE WHEN total_purchase_amount > 0 THEN 1 ELSE 0 END) as users_with_purchase',
                'SUM(CASE WHEN bonus_limit_reached = 1 THEN 1 ELSE 0 END) as users_limit_reached',
                'SUM(CASE WHEN total_bonus_received >= total_purchase_amount * 9 AND bonus_limit_reached = 0 THEN 1 ELSE 0 END) as users_near_limit',
                'SUM(total_purchase_amount) as total_purchase_amount',
                'SUM(total_bonus_received) as total_bonus_received',
                'SUM(total_purchase_amount * ' . self::BONUS_LIMIT_RATIO . ') as total_bonus_limit'
            ])
            ->find();
            
        if ($stats) {
            $stats['overall_bonus_ratio'] = $stats['total_purchase_amount'] > 0 
                ? round($stats['total_bonus_received'] / $stats['total_purchase_amount'], 2) 
                : 0;
            $stats['total_remaining_limit'] = $stats['total_bonus_limit'] - $stats['total_bonus_received'];
        }
        
        return $stats ?: [];
    }
    
    /**
     * 批量检查用户收益上限状态
     * @param array $user_ids 用户ID数组
     * @return array
     */
    public static function batchCheckLimitStatus($user_ids)
    {
        if (empty($user_ids)) {
            return [];
        }
        
        $users = Db::name('user')
            ->whereIn('id', $user_ids)
            ->field('id, total_purchase_amount, total_bonus_received, bonus_limit_reached')
            ->select();
            
        $result = [];
        foreach ($users as $user) {
            $can_receive = ($user['total_purchase_amount'] > 0 && 
                           $user['total_bonus_received'] < $user['total_purchase_amount'] * self::BONUS_LIMIT_RATIO &&
                           $user['bonus_limit_reached'] == 0);
            $result[$user['id']] = $can_receive;
        }
        
        return $result;
    }
    
    /**
     * 记录收益上限变更日志
     * @param int $user_id 用户ID
     * @param string $action_type 操作类型
     * @param int $order_id 订单ID
     * @param float $amount 金额
     * @param float $before_purchase 操作前购买金额
     * @param float $after_purchase 操作后购买金额
     * @param float $before_bonus 操作前收益金额
     * @param float $after_bonus 操作后收益金额
     * @param float $bonus_limit 收益上限
     * @param int $before_limit_status 操作前上限状态
     * @param int $after_limit_status 操作后上限状态
     * @param string $memo 备注
     */
    private static function logLimitChange($user_id, $action_type, $order_id, $amount, 
                                         $before_purchase, $after_purchase, 
                                         $before_bonus, $after_bonus, $bonus_limit,
                                         $before_limit_status, $after_limit_status, $memo)
    {
        try {
            Db::name('user_bonus_limit_log')->insert([
                'user_id' => $user_id,
                'action_type' => $action_type,
                'order_id' => $order_id,
                'amount' => $amount,
                'before_purchase_amount' => $before_purchase,
                'after_purchase_amount' => $after_purchase,
                'before_bonus_amount' => $before_bonus,
                'after_bonus_amount' => $after_bonus,
                'bonus_limit' => $bonus_limit,
                'limit_ratio' => self::BONUS_LIMIT_RATIO,
                'before_limit_status' => $before_limit_status,
                'after_limit_status' => $after_limit_status,
                'memo' => $memo,
                'oper_type' => 'system',
                'oper_id' => 0,
                'createtime' => time()
            ]);
        } catch (\Exception $e) {
            Log::error("记录收益上限日志异常: " . $e->getMessage());
        }
    }
    
    /**
     * 获取用户收益上限日志
     * @param int $user_id 用户ID
     * @param int $limit 限制数量
     * @return array
     */
    public static function getUserLimitLogs($user_id, $limit = 20)
    {
        $logs = Db::name('user_bonus_limit_log')
            ->where('user_id', $user_id)
            ->order('createtime', 'desc')
            ->limit($limit)
            ->select();
            
        foreach ($logs as &$log) {
            $log['createtime_format'] = date('Y-m-d H:i:s', $log['createtime']);
        }
        
        return $logs;
    }
    
    /**
     * 清除收益上限状态缓存
     * @param int $user_id 用户ID，为空则清除所有缓存
     */
    public static function clearLimitCache($user_id = null)
    {
        if ($user_id === null) {
            self::$limitStatusCache = [];
        } else {
            unset(self::$limitStatusCache[$user_id]);
        }
    }
    
    /**
     * 手动重置用户收益上限状态
     * @param int $user_id 用户ID
     * @param string $memo 备注
     * @param int $oper_id 操作人ID
     * @return bool
     */
    public static function resetUserLimitStatus($user_id, $memo = '', $oper_id = 0)
    {
        try {
            $user = Db::name('user')->where('id', $user_id)->find();
            if (!$user) {
                return false;
            }
            
            $before_limit_status = intval($user['bonus_limit_reached']);
            
            // 重置上限状态
            Db::name('user')->where('id', $user_id)->update([
                'bonus_limit_reached' => 0
            ]);
            
            // 记录重置日志
            self::logLimitChange(
                $user_id,
                self::ACTION_LIMIT_RESET,
                0,
                0,
                $user['total_purchase_amount'],
                $user['total_purchase_amount'],
                $user['total_bonus_received'],
                $user['total_bonus_received'],
                $user['total_purchase_amount'] * self::BONUS_LIMIT_RATIO,
                $before_limit_status,
                0,
                $memo ?: '手动重置收益上限状态'
            );
            
            // 清除缓存
            unset(self::$limitStatusCache[$user_id]);
            
            return true;
            
        } catch (\Exception $e) {
            Log::error("重置收益上限状态异常: " . $e->getMessage());
            return false;
        }
    }
}
