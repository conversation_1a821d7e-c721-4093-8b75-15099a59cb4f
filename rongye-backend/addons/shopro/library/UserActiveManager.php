<?php

namespace addons\shopro\library;

use think\Db;
use think\Log;
use addons\shopro\library\BonusLimitManager;
use think\Exception;

/**
 * 用户激活状态管理类
 * 负责用户激活状态的检查、更新和管理
 */
class UserActiveManager
{
    // 激活动作类型常量
    const ACTION_PURCHASE = 'purchase';        // 购买激活
    const ACTION_DIRECT_PUSH = 'direct_push'; // 直推延续
    const ACTION_EXPIRE = 'expire';            // 过期
    const ACTION_MANUAL = 'manual';            // 手动操作
    
    // 操作人类型常量
    const OPER_SYSTEM = 'system';              // 系统操作
    const OPER_ADMIN = 'admin';                // 管理员操作
    
    // 默认激活天数
    const DEFAULT_ACTIVE_DAYS = 30;
    
    // 激活状态缓存
    private static $activeStatusCache = [];

    /**
     * 检查用户是否激活（可以获得分润）
     * 同时检查激活状态和收益上限
     * @param int $user_id 用户ID
     * @param bool $use_cache 是否使用缓存
     * @return bool
     */
    public static function isUserActive($user_id, $use_cache = true)
    {
        if ($use_cache && isset(self::$activeStatusCache[$user_id])) {
            return self::$activeStatusCache[$user_id];
        }

        $user = Db::name('user')->where('id', $user_id)->find();
        if (!$user) {
            return false;
        }

        $current_time = time();
        // 检查分润激活状态：只需要检查分润激活状态和过期时间
        $is_active = ($user['is_active'] == 1 &&         // 分润激活状态
                     $user['active_expire_time'] > 0 &&
                     $user['active_expire_time'] > $current_time);

        // 如果激活状态通过，还需要检查收益上限
        if ($is_active) {
            $can_receive_bonus = BonusLimitManager::canReceiveBonus($user_id, $use_cache);
            $is_active = $can_receive_bonus;
        }

        if ($use_cache) {
            self::$activeStatusCache[$user_id] = $is_active;
        }

        return $is_active;
    }

    /**
     * 激活用户（购买奖金商品后）
     * @param int $user_id 用户ID
     * @param int $order_id 触发订单ID
     * @param int $active_days 激活天数
     * @return bool
     */
    public static function activateUser($user_id, $order_id = 0, $active_days = self::DEFAULT_ACTIVE_DAYS)
    {
        try {
            $user = Db::name('user')->where('id', $user_id)->find();
            if (!$user) {
                Log::error("激活用户失败：用户不存在 user_id={$user_id}");
                return false;
            }

            $current_time = time();
            $new_start_time = $current_time;
            $new_end_time = $current_time + ($active_days * 86400);

            // 记录变更前状态
            $before_status = intval($user['is_active']);
            $before_start_time = intval($user['active_start_time']);
            $before_end_time = intval($user['active_expire_time']);

            // 更新用户激活状态
            $update_data = [
                'is_active' => 1,
                'active_start_time' => $new_start_time,
                'active_expire_time' => $new_end_time,
                'last_purchase_time' => $current_time,
                'active_count' => $user['active_count'] + 1
            ];

            Db::name('user')->where('id', $user_id)->update($update_data);

            // 记录激活日志
            self::logActiveChange(
                $user_id,
                self::ACTION_PURCHASE,
                $order_id,
                0,
                $before_status,
                1,
                $before_start_time,
                $new_start_time,
                $before_end_time,
                $new_end_time,
                $active_days,
                "购买奖金商品激活用户{$active_days}天"
            );

            // 清除缓存
            unset(self::$activeStatusCache[$user_id]);

            Log::info("用户激活成功: user_id={$user_id}, order_id={$order_id}, active_days={$active_days}");
            return true;

        } catch (\Exception $e) {
            Log::error("激活用户异常: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 延续用户激活时间（直推成功后）
     * @param int $user_id 用户ID
     * @param int $trigger_order_id 触发订单ID
     * @param int $trigger_user_id 触发用户ID（下级用户）
     * @param int $active_days 激活天数
     * @return bool
     */
    public static function extendActiveTime($user_id, $trigger_order_id = 0, $trigger_user_id = 0, $active_days = self::DEFAULT_ACTIVE_DAYS)
    {
        try {
            $user = Db::name('user')->where('id', $user_id)->find();
            if (!$user) {
                Log::error("延续激活失败：用户不存在 user_id={$user_id}");
                return false;
            }

            $current_time = time();
            $new_start_time = $current_time;
            $new_end_time = $current_time + ($active_days * 86400);

            // 记录变更前状态
            $before_status = intval($user['is_active']);
            $before_start_time = intval($user['active_start_time']);
            $before_end_time = intval($user['active_expire_time']);

            // 计算累计激活天数
            $additional_days = 0;
            if ($before_status == 1 && $before_end_time > $current_time) {
                // 如果当前还在激活期内，累加剩余天数
                $remaining_days = ceil(($before_end_time - $current_time) / 86400);
                $additional_days = min($remaining_days, 30); // 最多累加30天
            }

            // 更新用户激活状态
            $update_data = [
                'is_active' => 1,
                'active_start_time' => $new_start_time,
                'active_expire_time' => $new_end_time + ($additional_days * 86400),
                'last_direct_push_time' => $current_time
            ];

            Db::name('user')->where('id', $user_id)->update($update_data);

            // 记录激活日志
            self::logActiveChange(
                $user_id,
                self::ACTION_DIRECT_PUSH,
                $trigger_order_id,
                $trigger_user_id,
                $before_status,
                1,
                $before_start_time,
                $new_start_time,
                $before_end_time,
                $new_end_time + ($additional_days * 86400),
                $active_days + $additional_days,
                "直推成功延续激活时间{$active_days}天" . ($additional_days > 0 ? "，累加剩余{$additional_days}天" : "")
            );

            // 清除缓存
            unset(self::$activeStatusCache[$user_id]);

            Log::info("用户激活延续成功: user_id={$user_id}, trigger_order_id={$trigger_order_id}, trigger_user_id={$trigger_user_id}, total_days=" . ($active_days + $additional_days));
            return true;

        } catch (\Exception $e) {
            Log::error("延续用户激活异常: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 处理订单激活逻辑
     * @param int $order_id 订单ID
     * @param int $buyer_user_id 购买用户ID
     * @param bool $has_bonus_goods 是否包含奖金商品
     * @return bool
     */
    public static function processOrderActive($order_id, $buyer_user_id, $has_bonus_goods = false)
    {
        try {
            if (!$has_bonus_goods) {
                Log::info("订单{$order_id}不包含奖金商品，跳过激活处理");
                return true;
            }

            // 1. 激活购买用户
            $activate_result = self::activateUser($buyer_user_id, $order_id);
            if (!$activate_result) {
                Log::error("激活购买用户失败: user_id={$buyer_user_id}, order_id={$order_id}");
                return false;
            }

            // 2. 检查并延续直接上级的激活时间
            $buyer = Db::name('user')->where('id', $buyer_user_id)->find();
            if ($buyer && $buyer['parent_user_id'] > 0) {
                $parent_user_id = $buyer['parent_user_id'];
                
                // 检查上级是否在激活期内
                if (self::isUserActive($parent_user_id, false)) {
                    $extend_result = self::extendActiveTime($parent_user_id, $order_id, $buyer_user_id);
                    if ($extend_result) {
                        Log::info("延续上级用户激活成功: parent_user_id={$parent_user_id}, buyer_user_id={$buyer_user_id}, order_id={$order_id}");
                    } else {
                        Log::error("延续上级用户激活失败: parent_user_id={$parent_user_id}, buyer_user_id={$buyer_user_id}, order_id={$order_id}");
                    }
                } else {
                    Log::info("上级用户未激活，跳过延续: parent_user_id={$parent_user_id}");
                }
            }

            return true;

        } catch (\Exception $e) {
            Log::error("处理订单激活逻辑异常: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 批量检查激活状态过期
     * @return int 处理的用户数量
     */
    public static function checkExpiredUsers()
    {
        try {
            $current_time = time();
            
            // 查找过期的激活用户
            $expired_users = Db::name('user')
                ->where('is_active', 1)
                ->where('active_expire_time', '>', 0)
                ->where('active_expire_time', '<=', $current_time)
                ->field('id, active_start_time, active_expire_time, total_active_days')
                ->select();

            $processed_count = 0;
            
            foreach ($expired_users as $user) {
                // 计算本次激活的天数
                $this_active_days = 0;
                if ($user['active_start_time'] > 0 && $user['active_expire_time'] > $user['active_start_time']) {
                    $this_active_days = ceil(($user['active_expire_time'] - $user['active_start_time']) / 86400);
                }
                
                // 更新用户状态
                $update_data = [
                    'is_active' => 0,
                    'total_active_days' => $user['total_active_days'] + $this_active_days
                ];
                
                Db::name('user')->where('id', $user['id'])->update($update_data);
                
                // 记录过期日志
                self::logActiveChange(
                    $user['id'],
                    self::ACTION_EXPIRE,
                    0,
                    0,
                    1,
                    0,
                    $user['active_start_time'],
                    0,
                    $user['active_expire_time'],
                    0,
                    0,
                    "激活状态自动过期，本次激活{$this_active_days}天"
                );
                
                // 清除缓存
                unset(self::$activeStatusCache[$user['id']]);
                
                $processed_count++;
            }

            if ($processed_count > 0) {
                Log::info("批量处理过期激活用户完成: 处理数量={$processed_count}");
            }

            return $processed_count;

        } catch (\Exception $e) {
            Log::error("检查过期激活用户异常: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * 获取用户激活状态详情
     * @param int $user_id 用户ID
     * @return array
     */
    public static function getUserActiveStatus($user_id)
    {
        $user = Db::name('user')->where('id', $user_id)->find();
        if (!$user) {
            return [];
        }

        $current_time = time();
        // 检查分润激活状态：只需要检查分润激活状态和过期时间
        $is_active = ($user['is_active'] == 1 &&         // 分润激活状态
                     $user['active_expire_time'] > 0 &&
                     $user['active_expire_time'] > $current_time);

        $remaining_seconds = 0;
        $remaining_days = 0;
        if ($is_active) {
            $remaining_seconds = $user['active_expire_time'] - $current_time;
            $remaining_days = ceil($remaining_seconds / 86400);
        }

        return [
            'user_id' => $user['id'],
            'username' => $user['username'],
            'nickname' => $user['nickname'],
            'is_active' => $is_active,
            'active_status' => $user['is_active'],
            'active_start_time' => $user['active_start_time'],
            'active_expire_time' => $user['active_expire_time'],
            'last_purchase_time' => $user['last_purchase_time'],
            'last_direct_push_time' => $user['last_direct_push_time'],
            'total_active_days' => $user['total_active_days'],
            'active_count' => $user['active_count'],
            'remaining_seconds' => $remaining_seconds,
            'remaining_days' => $remaining_days,
            'status_desc' => $is_active ? 'active' : ($user['is_active'] == 1 ? 'expired' : 'inactive')
        ];
    }

    /**
     * 获取激活状态统计
     * @return array
     */
    public static function getActiveStats()
    {
        $current_time = time();

        $stats = Db::name('user')
            ->field([
                'COUNT(*) as total_users',
                'SUM(CASE WHEN is_active = 1 AND active_expire_time > ' . $current_time . ' THEN 1 ELSE 0 END) as active_users',
                'SUM(CASE WHEN is_active = 1 AND active_expire_time <= ' . $current_time . ' THEN 1 ELSE 0 END) as expired_users',
                'SUM(CASE WHEN is_active = 0 THEN 1 ELSE 0 END) as inactive_users',
                'AVG(total_active_days) as avg_active_days',
                'SUM(total_active_days) as total_active_days'
            ])
            ->find();

        // 获取今日激活统计
        $today_start = strtotime(date('Y-m-d 00:00:00'));
        $today_stats = Db::name('user_active_log')
            ->where('createtime', '>=', $today_start)
            ->field([
                'COUNT(CASE WHEN action_type = "purchase" THEN 1 END) as today_activations',
                'COUNT(CASE WHEN action_type = "direct_push" THEN 1 END) as today_extensions',
                'COUNT(CASE WHEN action_type = "expire" THEN 1 END) as today_expires'
            ])
            ->find();

        return array_merge($stats ?: [], $today_stats ?: []);
    }

    /**
     * 获取用户激活日志
     * @param int $user_id 用户ID
     * @param int $limit 限制数量
     * @return array
     */
    public static function getUserActiveLogs($user_id, $limit = 20)
    {
        $logs = Db::name('user_active_log')
            ->where('user_id', $user_id)
            ->order('createtime', 'desc')
            ->limit($limit)
            ->select();

        foreach ($logs as &$log) {
            $log['createtime_format'] = date('Y-m-d H:i:s', $log['createtime']);
            $log['before_start_time_format'] = $log['before_start_time'] > 0 ? date('Y-m-d H:i:s', $log['before_start_time']) : '';
            $log['after_start_time_format'] = $log['after_start_time'] > 0 ? date('Y-m-d H:i:s', $log['after_start_time']) : '';
            $log['before_end_time_format'] = $log['before_end_time'] > 0 ? date('Y-m-d H:i:s', $log['before_end_time']) : '';
            $log['after_end_time_format'] = $log['after_end_time'] > 0 ? date('Y-m-d H:i:s', $log['after_end_time']) : '';
        }

        return $logs ?: [];
    }

    /**
     * 批量检查用户激活状态
     * @param array $user_ids 用户ID数组
     * @return array
     */
    public static function batchCheckActiveStatus($user_ids)
    {
        if (empty($user_ids)) {
            return [];
        }

        $current_time = time();
        $users = Db::name('user')
            ->whereIn('id', $user_ids)
            ->field('id, is_active, active_expire_time')
            ->select();

        $result = [];
        foreach ($users as $user) {
            $is_active = ($user['is_active'] == 1 &&         // 分润激活状态
                         $user['active_expire_time'] > 0 &&
                         $user['active_expire_time'] > $current_time);
            $result[$user['id']] = $is_active;

            // 更新缓存
            self::$activeStatusCache[$user['id']] = $is_active;
        }

        return $result;
    }

    /**
     * 记录激活状态变更日志
     * @param int $user_id 用户ID
     * @param string $action_type 动作类型
     * @param int $trigger_order_id 触发订单ID
     * @param int $trigger_user_id 触发用户ID
     * @param int $before_status 变更前状态
     * @param int $after_status 变更后状态
     * @param int $before_start_time 变更前开始时间
     * @param int $after_start_time 变更后开始时间
     * @param int $before_end_time 变更前结束时间
     * @param int $after_end_time 变更后结束时间
     * @param int $active_days 激活天数
     * @param string $memo 备注
     * @return bool
     */
    private static function logActiveChange($user_id, $action_type, $trigger_order_id, $trigger_user_id,
                                          $before_status, $after_status, $before_start_time, $after_start_time,
                                          $before_end_time, $after_end_time, $active_days, $memo)
    {
        try {
            $log_data = [
                'user_id' => $user_id,
                'action_type' => $action_type,
                'trigger_order_id' => $trigger_order_id,
                'trigger_user_id' => $trigger_user_id,
                'before_status' => $before_status,
                'after_status' => $after_status,
                'before_start_time' => $before_start_time,
                'after_start_time' => $after_start_time,
                'before_end_time' => $before_end_time,
                'after_end_time' => $after_end_time,
                'active_days' => $active_days,
                'memo' => $memo,
                'oper_type' => self::OPER_SYSTEM,
                'oper_id' => 0,
                'createtime' => time()
            ];

            return Db::name('user_active_log')->insert($log_data);

        } catch (\Exception $e) {
            Log::error("记录激活状态变更日志失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 清除激活状态缓存
     * @param int $user_id 用户ID，为空则清除所有缓存
     */
    public static function clearActiveCache($user_id = null)
    {
        if ($user_id === null) {
            self::$activeStatusCache = [];
        } else {
            unset(self::$activeStatusCache[$user_id]);
        }
    }

    /**
     * 手动设置用户激活状态
     * @param int $user_id 用户ID
     * @param bool $is_active 是否激活
     * @param int $active_days 激活天数
     * @param string $memo 备注
     * @param int $oper_id 操作人ID
     * @return bool
     */
    public static function manualSetActiveStatus($user_id, $is_active, $active_days = self::DEFAULT_ACTIVE_DAYS, $memo = '', $oper_id = 0)
    {
        try {
            $user = Db::name('user')->where('id', $user_id)->find();
            if (!$user) {
                return false;
            }

            $current_time = time();
            $before_status = intval($user['is_active']);
            $before_start_time = intval($user['active_start_time']);
            $before_end_time = intval($user['active_expire_time']);

            $update_data = [
                'is_active' => $is_active ? 1 : 0
            ];

            if ($is_active) {
                $update_data['active_start_time'] = $current_time;
                $update_data['active_expire_time'] = $current_time + ($active_days * 86400);
            }

            Db::name('user')->where('id', $user_id)->update($update_data);

            // 记录日志
            self::logActiveChange(
                $user_id,
                self::ACTION_MANUAL,
                0,
                0,
                $before_status,
                $is_active ? 1 : 0,
                $before_start_time,
                $is_active ? $current_time : 0,
                $before_end_time,
                $is_active ? $current_time + ($active_days * 86400) : 0,
                $active_days,
                $memo ?: '手动设置激活状态'
            );

            // 清除缓存
            unset(self::$activeStatusCache[$user_id]);

            return true;

        } catch (\Exception $e) {
            Log::error("手动设置用户激活状态异常: " . $e->getMessage());
            return false;
        }
    }
}
