<?php

namespace addons\shopro\library;

use addons\shopro\service\Wallet;
use addons\shopro\library\ScorePool;
use addons\shopro\library\UserActiveManager;
use addons\shopro\library\BonusLimitManager;
use app\admin\model\shopro\order\Order;
use app\admin\model\shopro\trade\Order as TradeOrder;
use app\admin\model\shopro\user\User as UserModel;
use app\admin\model\shopro\user\WalletLog;
use app\common\model\User;
use think\Db;
use think\Log;

/**
 * 分润系统核心类
 * 实现用户等级升级和分润计算分配功能
 */
class Bonus
{
    // 分润类型常量
    const BONUS_DIRECT_SALES = 'direct_sales';    // 直接销售
    const BONUS_TEAM = 'team_bonus';              // 团队奖励
    const BONUS_LEVEL = 'level_bonus';            // 岗位补助
    const BONUS_PING = 'ping_bonus';              // 同岗补助

    // 用户等级常量
    const LEVEL_SALES = 1;        // 创业销售商
    const LEVEL_ADVISOR = 2;      // 拓业顾问
    const LEVEL_MANAGER = 3;      // 守业经理
    const LEVEL_DIRECTOR = 4;     // 享业董事
    const LEVEL_WISDOM = 5;       // 智慧董事

    /**
     * 获取所有用户等级配置
     * @return array
     */
    public static function getUserLevels()
    {
        static $levels = null;

        if ($levels === null) {
            $levels = Db::name('user_group')
                ->where('status', 'normal')
                ->order('level_order', 'asc')
                ->select();

            // 转换为以level_code为键的数组
            $levelMap = [];
            foreach ($levels as $level) {
                $level['upgrade_condition'] = json_decode($level['upgrade_condition'], true) ?: [];
                $level['special_bonus'] = json_decode($level['special_bonus'], true) ?: [];
                $levelMap[$level['level_code']] = $level;
            }
            $levels = $levelMap;
        }

        return $levels;
    }

    /**
     * 根据level_code获取等级配置
     * @param string $level_code
     * @return array|null
     */
    public static function getLevelConfig($level_code)
    {
        $levels = self::getUserLevels();
        return $levels[$level_code] ?? null;
    }

    /**
     * 根据level_order获取等级配置
     * @param int $level_order
     * @return array|null
     */
    public static function getLevelConfigByOrder($level_order)
    {
        $levels = Db::name('user_group')
            ->where('status', 'normal')
            ->where('level_order', $level_order)
            ->find();

        if ($levels) {
            $levels['upgrade_condition'] = json_decode($levels['upgrade_condition'], true) ?: [];
            $levels['special_bonus'] = json_decode($levels['special_bonus'], true) ?: [];
            return $levels;
        }

        return null;
    }

    /**
     * 获取业务配置
     * @return array
     */
    public static function getBusinessConfig()
    {
        static $config = null;

        if ($config === null) {
            // 从数据库获取配置
            $configData = Db::name('shopro_config')
                ->where('parent_code', 'bonus_system')
                ->column('value', 'code');

            $config = [
                'repurchase_multiplier' => intval($configData['repurchase_multiplier'] ?? 10),
                'inactive_days' => intval($configData['inactive_days'] ?? 30),
                'inactive_bonus' => intval($configData['inactive_bonus'] ?? 799),
            ];
        }

        return $config;
    }

    /**
     * 处理订单分润（主入口方法）
     * @param int $order_id 订单ID
     * @param float $unit_price 单盒价格（可选，用于计算销售数量）
     * @return bool
     */
    public static function processOrderBonus($order_id, $unit_price = null)
    {
        try {
            Db::startTrans();

            $order = Order::get($order_id);
            if (!$order || $order->status !== 'paid') {
                Log::error("订单不存在或未支付: {$order_id}");
                return false;
            }

            $user = User::get($order->user_id);
            if (!$user) {
                Log::error("用户不存在: {$order->user_id}");
                return false;
            }

            // 首次购买升级逻辑：普通会员首次购买奖金商品时自动升级为创业销售商
            if ($user->group_id == 1) { // 默认组
                $user->group_id = 2; // 升级为创业销售商
                $user->save();
                Log::info("用户{$user->id}首次购买奖金商品，自动升级为创业销售商");
            }

            // 计算奖金商品的总金额
            $bonus_amount = self::calculateBonusAmount($order_id);

            if ($bonus_amount <= 0) {
                Log::info("订单{$order_id}中没有奖金商品或奖金商品金额为0，跳过分润处理");
                Db::commit();
                return true;
            }

            // 计算销售数量（用于统计，不影响分润金额）
            if ($unit_price && $unit_price > 0) {
                $sales_count = floor($bonus_amount / $unit_price);
                $actual_unit_price = $unit_price;
            } else {
                $sales_count = 1;
                $actual_unit_price = $bonus_amount;
            }

            if ($sales_count <= 0) {
                $sales_count = 1; // 至少算作1次销售
            }

            // 获取用户当前等级配置
            $userLevel = self::getUserLevelByUser($user);
            Log::info("用户等级配置: " . json_encode($userLevel));
            if (!$userLevel) {
                Log::error("用户等级配置不存在: user_id={$user->id}, group_id={$user->group_id}, user_level={$user->user_level}");
                return false;
            }

            // 1. 给直接邀请人发放直接销售奖励（按奖金商品金额百分比）
            self::giveDirectSalesBonus($user, $bonus_amount, $order_id);

            // 2. 记录用户购买金额（用于收益上限控制）
            BonusLimitManager::recordPurchaseAmount($user->id, $bonus_amount, $order_id);

            // 3. 更新用户销售统计
            self::updateUserSalesStats($user->id, $sales_count, $actual_unit_price);

            // 4. 检查购买前的激活状态（用于团队奖励判断）
            $buyer_was_active_before = UserActiveManager::isUserActive($user->id);

            // 4. 处理用户激活状态管理（购买奖金商品自动激活）
            self::processUserActiveStatus($order_id, $user->id, $bonus_amount > 0);

            // 5. 给购买者发放团队奖励（基于购买前的激活状态）
            self::giveBuyerTeamBonus($user, $bonus_amount, $order_id, $buyer_was_active_before);

            // 5. 分配团队奖励给上级（按奖金商品金额百分比）
            self::distributeTeamBonus($user, $bonus_amount, $order_id);

            // 6. 处理积分池沉淀（新增功能）
            self::processScorePoolDeposit($order_id);

            // 7. 检查并处理用户等级升级
            self::checkAndUpgradeUserLevel($user->id);

            // 8. 检查复购要求
            self::checkRepurchaseRequirement($user->id, $actual_unit_price);

            Db::commit();
            Log::info("订单{$order_id}分润处理完成，奖金商品金额: {$bonus_amount}元");
            return true;

        } catch (\Exception $e) {
            Db::rollback();
            Log::error("处理订单分润失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 计算订单中奖金商品的总金额
     * @param int $order_id 订单ID
     * @return float
     */
    private static function calculateBonusAmount($order_id)
    {
        // 查询订单中的所有订单项
        $order_items = Db::name('shopro_order_item')
            ->where('order_id', $order_id)
            ->select();

        $bonus_amount = 0;
        $bonus_items = [];

        foreach ($order_items as $item) {
            // 查询商品是否为奖金商品
            $goods = Db::name('shopro_goods')
                ->where('id', $item['goods_id'])
                ->find();

            if ($goods && $goods['is_bonus'] == 1) {
                $bonus_amount += $item['pay_fee'];
                $bonus_items[] = [
                    'goods_id' => $item['goods_id'],
                    'goods_title' => $goods['title'],
                    'pay_fee' => $item['pay_fee']
                ];
            }
        }

        Log::info("订单{$order_id}奖金商品金额计算: {$bonus_amount}元，奖金商品数量: " . count($bonus_items));

        return floatval($bonus_amount);
    }

    /**
     * 获取订单中奖金商品的详细信息
     * @param int $order_id 订单ID
     * @return array
     */
    private static function getBonusItems($order_id)
    {
        // 查询订单中的所有订单项
        $order_items = Db::name('shopro_order_item')
            ->where('order_id', $order_id)
            ->select();

        $bonus_items = [];

        foreach ($order_items as $item) {
            // 查询商品信息
            $goods = Db::name('shopro_goods')
                ->where('id', $item['goods_id'])
                ->find();

            if ($goods && $goods['is_bonus'] == 1) {
                $bonus_items[] = array_merge($item, [
                    'goods_title' => $goods['title'],
                    'is_bonus' => $goods['is_bonus']
                ]);
            }
        }

        return $bonus_items;
    }

    /**
     * 根据用户获取等级配置
     * @param object $user 用户对象
     * @return array|null
     */
    private static function getUserLevelByUser($user)
    {
        // 优先使用group_id对应的等级配置
        if ($user->group_id > 0) {
            $level = Db::name('user_group')->where('id', $user->group_id)->find();
            if ($level) {
                $level['upgrade_condition'] = json_decode($level['upgrade_condition'], true) ?: [];
                $level['special_bonus'] = json_decode($level['special_bonus'], true) ?: [];
                return $level;
            }
        }

        // 兜底：如果group_id对应的等级不存在，根据user_level字段获取等级
        return self::getLevelConfigByOrder($user->user_level ?? 1);
    }

    /**
     * 发放直接销售奖励给直接邀请人
     * @param object $buyer 购买用户
     * @param float $order_amount 订单金额
     * @param int $order_id 订单ID
     */
    private static function giveDirectSalesBonus($buyer, $order_amount, $order_id)
    {
        // 获取直接邀请人
        if (!$buyer->parent_user_id) {
            Log::info("用户{$buyer->id}没有直接邀请人，跳过直接销售奖励");
            return;
        }

        $inviter = User::get($buyer->parent_user_id);
        if (!$inviter) {
            Log::error("直接邀请人{$buyer->parent_user_id}不存在");
            return;
        }

        $inviterLevel = self::getUserLevelByUser($inviter);
        if (!$inviterLevel) {
            Log::error("直接邀请人{$inviter->id}等级配置不存在");
            return;
        }

        // 检查邀请人激活状态
        if (!UserActiveManager::isUserActive($inviter->id)) {
            Log::info("直接邀请人{$inviter->id}未激活，跳过直接销售奖励");
            return;
        }

        // 按邀请人的等级配置计算奖励金额
        $cash_bonus = bcmul($order_amount, $inviterLevel['direct_sales_bonus_rate'], 2);
        $score_bonus = bcmul($order_amount, $inviterLevel['direct_sales_score_rate'], 2);

        // 发放现金奖励给邀请人
        if ($cash_bonus > 0) {
            Wallet::change($inviter->id, 'money', $cash_bonus, 'order_buy_bonus',
                ['order_id' => $order_id], "直接销售奖励 " . ($inviterLevel['direct_sales_bonus_rate'] * 100) . "%");

            // 记录收益金额（用于收益上限控制）
            BonusLimitManager::recordBonusAmount($inviter->id, $cash_bonus, $order_id, 'direct_sales');
        }

        // 发放积分奖励给邀请人
        if ($score_bonus > 0) {
            Wallet::change($inviter->id, 'score', $score_bonus, 'order_buy_bonus',
                ['order_id' => $order_id], "直接销售积分奖励 " . ($inviterLevel['direct_sales_score_rate'] * 100) . "%");
        }

        // 记录分润日志
        self::logBonusRecord($order_id, $buyer->id, $inviter->id, self::BONUS_DIRECT_SALES,
            $cash_bonus, $score_bonus, '直接销售奖励');

        Log::info("直接销售奖励：邀请人{$inviter->id}获得{$cash_bonus}元现金+{$score_bonus}积分，来自用户{$buyer->id}的订单{$order_id}");
    }

    /**
     * 给购买者发放团队奖励（如果购买者有团队奖励资格）
     * @param object $user 购买用户
     * @param float $order_amount 奖金商品金额
     * @param int $order_id 订单ID
     * @param bool $was_active_before 购买前是否已激活
     */
    private static function giveBuyerTeamBonus($user, $order_amount, $order_id, $was_active_before = null)
    {
        // 获取购买者等级配置
        $buyerLevel = self::getUserLevelByUser($user);
        if (!$buyerLevel) {
            Log::error("购买者等级配置不存在: user_id={$user->id}");
            return;
        }

        // 检查购买者是否有团队奖励资格
        $buyerTeamBonusRate = floatval($buyerLevel['team_bonus_rate'] ?? 0);
        $buyerTeamScoreRate = floatval($buyerLevel['team_score_rate'] ?? 0);

        // 创业销售商和智慧董事不参与团队奖励
        if ($user->group_id == 2 || $user->group_id == 6) {
            Log::info("用户{$user->id}({$buyerLevel['name']})不参与团队奖励");
            return;
        }

        // 检查购买者激活状态（基于购买前的状态或当前状态）
        $should_get_bonus = false;
        if ($was_active_before !== null) {
            // 如果提供了购买前状态，则基于购买前状态判断
            $should_get_bonus = $was_active_before;
            $status_desc = $was_active_before ? "购买前已激活" : "购买前未激活";
        } else {
            // 如果没有提供购买前状态，则基于当前状态判断
            $should_get_bonus = UserActiveManager::isUserActive($user->id);
            $status_desc = $should_get_bonus ? "当前已激活" : "当前未激活";
        }

        if (!$should_get_bonus) {
            Log::info("购买者{$user->id}{$status_desc}，跳过购买者团队奖励");
            return;
        }

        // 如果购买者有团队奖励资格，发放团队奖励
        if ($buyerTeamBonusRate > 0) {
            $team_bonus = bcmul($order_amount, $buyerTeamBonusRate, 2);

            if ($team_bonus > 0) {
                Wallet::change($user->id, 'money', $team_bonus, 'order_team_bonus',
                    ['order_id' => $order_id], $buyerLevel['name'] . "购买者团队奖励 " . ($buyerTeamBonusRate * 100) . "%");

                // 记录收益金额（用于收益上限控制）
                BonusLimitManager::recordBonusAmount($user->id, $team_bonus, $order_id, 'team_bonus');

                Log::info("购买者团队奖励: 用户{$user->id}({$buyerLevel['name']})获得{$team_bonus}元团队奖励");
            }
        }

        // 如果购买者有团队积分奖励资格，发放团队积分奖励
        if ($buyerTeamScoreRate > 0) {
            $score_bonus = bcmul($order_amount, $buyerTeamScoreRate, 2);

            if ($score_bonus > 0) {
                Wallet::change($user->id, 'score', $score_bonus, 'order_team_bonus',
                    ['order_id' => $order_id], $buyerLevel['name'] . "购买者团队积分奖励 " . ($buyerTeamScoreRate * 100) . "%");

                Log::info("购买者团队积分奖励: 用户{$user->id}({$buyerLevel['name']})获得{$score_bonus}积分");
            }
        }

        // 记录分润日志
        if ($buyerTeamBonusRate > 0 || $buyerTeamScoreRate > 0) {
            self::logBonusRecord($order_id, $user->id, $user->id, self::BONUS_TEAM,
                $team_bonus ?? 0, $score_bonus ?? 0, $buyerLevel['name'] . '购买者团队奖励');
        }
    }

    /**
     * 分配团队奖励给上级
     * @param object $user 购买用户
     * @param float $order_amount 订单金额
     * @param int $order_id 订单ID
     */
    private static function distributeTeamBonus($user, $order_amount, $order_id)
    {
        // 获取购买用户的等级配置，作为基础分润比例
        $buyerLevel = self::getUserLevelByUser($user);
        if (!$buyerLevel) {
            Log::error("无法获取购买用户等级配置: user_id={$user->id}");
            return;
        }

        // 当前已分配的分润比例（从购买用户等级开始）
        // 如果购买者有团队奖励资格，则从购买者等级开始计算级差
        // 如果购买者没有团队奖励资格（创业销售商、智慧董事），则从0开始计算
        $currentBonusRate = 0;
        $currentScoreRate = 0;

        if ($user->group_id != 2 && $user->group_id != 6) { // 不是创业销售商和智慧董事
            $currentBonusRate = floatval($buyerLevel['team_bonus_rate'] ?? 0);
            $currentScoreRate = floatval($buyerLevel['team_score_rate'] ?? 0);
        }

        Log::info("团队奖励分配开始: 购买用户{$user->id}等级{$buyerLevel['name']}，初始分润比例{$currentBonusRate}，初始积分比例{$currentScoreRate}");

        // 使用parent_user_path优化查询，一次性获取所有上级用户
        if (!empty($user->parent_user_path) && $user->parent_user_path !== '0') {
            // 解析parent_user_path获取所有上级用户ID
            $parent_ids = explode(',', $user->parent_user_path);
            $parent_ids = array_filter($parent_ids, function($id) {
                return $id > 0;
            });

            if (!empty($parent_ids)) {
                // 一次性查询所有上级用户
                $parent_users = User::where('id', 'in', $parent_ids)->select();

                // 创建用户映射
                $parent_users_map = [];
                foreach ($parent_users as $parent) {
                    $parent_users_map[$parent->id] = $parent;
                }

                // 按照从直接上级到顶层的顺序处理（parent_user_path是从顶层到直接上级）
                $reversed_parent_ids = array_reverse($parent_ids);

                foreach ($reversed_parent_ids as $parent_id) {
                    if (!isset($parent_users_map[$parent_id])) {
                        continue;
                    }

                    $parent_user = $parent_users_map[$parent_id];

                    // 获取上级用户的等级配置
                    $parentLevel = self::getUserLevelByUser($parent_user);
                    if (!$parentLevel) {
                        continue;
                    }

                    // 检查上级用户激活状态
                    if (!UserActiveManager::isUserActive($parent_user->id)) {
                        Log::info("上级用户{$parent_user->id}未激活，跳过团队奖励");
                        continue;
                    }

                    // 智慧董事只获得特殊补助，不获得团队奖励
                    if ($parent_user->group_id == 6 || $parentLevel['level_code'] == 'wisdom') {
                        // 处理智慧董事特殊补助（按商品数量计算）
                        if (!empty($parentLevel['special_bonus'])) {
                            self::processSpecialBonus($parent_user, $parentLevel['special_bonus'], $order_amount, $order_id, $user->id);
                        }
                        Log::info("智慧董事{$parent_user->id}跳过团队奖励，仅获得特殊补助");
                        continue; // 跳过团队奖励处理
                    }

                    // 计算级差奖励
                    $parentBonusRate = floatval($parentLevel['team_bonus_rate'] ?? 0);
                    $levelDiffRate = $parentBonusRate - $currentBonusRate;

                    Log::info("级差计算: 用户{$parent_user->id}({$parentLevel['name']}) 团队奖励率{$parentBonusRate} - 当前已分配{$currentBonusRate} = 级差{$levelDiffRate}");

                    // 只有当上级等级比例大于当前已分配比例时，才发放级差奖励
                    if ($levelDiffRate <= 0) {
                        Log::info("级差为0或负数，跳过用户{$parent_user->id}");
                        continue;
                    }

                    // 按级差比例计算团队奖励金额
                    $team_bonus = bcmul($order_amount, $levelDiffRate, 2);

                    // 计算积分级差奖励
                    $parentScoreRate = floatval($parentLevel['team_score_rate'] ?? 0);
                    $scoreDiffRate = $parentScoreRate - $currentScoreRate;
                    $score_bonus = $scoreDiffRate > 0 ? bcmul($order_amount, $scoreDiffRate, 2) : 0;

                    // 发放团队奖励
                    if ($team_bonus > 0) {
                        Wallet::change($parent_user->id, 'money', $team_bonus, 'order_team_bonus',
                            ['order_id' => $order_id], $parentLevel['name'] . "级差奖励 " . ($levelDiffRate * 100) . "%");

                        // 记录收益金额（用于收益上限控制）
                        BonusLimitManager::recordBonusAmount($parent_user->id, $team_bonus, $order_id, 'team_bonus');
                    }

                    if ($score_bonus > 0) {
                        Wallet::change($parent_user->id, 'score', $score_bonus, 'order_team_bonus',
                            ['order_id' => $order_id], $parentLevel['name'] . "团队积分级差奖励 " . ($scoreDiffRate * 100) . "%");
                    }

                    // 记录分润日志
                    self::logBonusRecord($order_id, $user->id, $parent_user->id, self::BONUS_TEAM,
                        $team_bonus, $score_bonus, $parentLevel['name'] . '级差奖励');

                    // 更新当前已分配比例
                    $currentBonusRate = $parentBonusRate;
                    $currentScoreRate = $parentScoreRate;

                    Log::info("级差奖励发放: 用户{$parent_user->id}({$parentLevel['name']})获得{$team_bonus}元级差奖励，级差比例{$levelDiffRate}，来自订单{$order_id}");
                }
            }
        }
    }

    /**
     * 处理特殊奖励
     * @param object $user 获得奖励的用户
     * @param array $specialBonus 特殊奖励配置
     * @param float $order_amount 订单金额
     * @param int $order_id 订单ID
     * @param int $buyer_user_id 购买用户ID
     */
    private static function processSpecialBonus($user, $specialBonus, $order_amount, $order_id, $buyer_user_id)
    {
        // 智慧董事特殊补助：每个商品固定3积分
        if (isset($specialBonus['extra_score_rate']) && $specialBonus['extra_score_rate'] > 0) {
            // 查询订单中的商品数量
            $total_quantity = Db::name('shopro_order_item')
                ->where('order_id', $order_id)
                ->sum('goods_num');

            $extra_score = $total_quantity * 3; // 每个商品3积分

            if ($extra_score > 0) {
                Wallet::change($user->id, 'score', $extra_score, 'order_subsidy',
                    ['order_id' => $order_id], "智慧董事特殊补助 {$total_quantity}个商品×3积分");

                self::logBonusRecord($order_id, $buyer_user_id, $user->id, self::BONUS_LEVEL,
                    0, $extra_score, '智慧董事特殊补助');

                Log::info("智慧董事特殊补助：用户{$user->id}获得{$extra_score}积分({$total_quantity}个商品×3)，订单{$order_id}");
            }
        }
    }

    /**
     * 更新用户销售统计
     * @param int $user_id 用户ID
     * @param int $sales_count 销售数量
     * @param float $unit_price 单价
     */
    private static function updateUserSalesStats($user_id, $sales_count, $unit_price)
    {
        $user = User::get($user_id);
        if (!$user) return;

        // 更新个人销售统计
        $user->sales_count = $user->sales_count + $sales_count;
        $user->last_sale_time = time();
        $user->is_active = 1;

        // 更新累计销售金额
        $sales_amount = bcmul($sales_count, $unit_price, 2);
        $user->total_consume = bcadd($user->total_consume, $sales_amount, 2);
        $user->save();

        // 更新上级的团队销售统计
        $current_user = $user;
        while ($current_user->parent_user_id) {
            $parent_user = User::get($current_user->parent_user_id);
            if (!$parent_user) break;

            $parent_user->team_sales_count = $parent_user->team_sales_count + $sales_count;
            $parent_user->save();

            $current_user = $parent_user;
        }
    }

    /**
     * 检查并处理用户等级升级
     * @param int $user_id 用户ID
     */
    public static function checkAndUpgradeUserLevel($user_id)
    {
        $user = User::get($user_id);
        if (!$user) return;

        $currentLevel = self::getUserLevelByUser($user);
        if (!$currentLevel) return;

        $levels = self::getUserLevels();
        $nextLevelOrder = $currentLevel['level_order'] + 1;
        $nextLevel = self::getLevelConfigByOrder($nextLevelOrder);

        if (!$nextLevel) return; // 已经是最高等级

        // 检查升级条件
        if (self::checkUpgradeCondition($user, $nextLevel['upgrade_condition'])) {
            // 检查名额限制
            if ($nextLevel['max_count'] > 0) {
                $currentCount = Db::name('user_group')
                    ->alias('ug')
                    ->join('user u', 'u.group_id = ug.id')
                    ->where('ug.level_code', $nextLevel['level_code'])
                    ->count();

                if ($currentCount >= $nextLevel['max_count']) {
                    Log::info("用户{$user_id}升级失败：{$nextLevel['name']}名额已满");
                    return;
                }
            }

            // 执行升级
            self::upgradeUserLevel($user_id, $currentLevel, $nextLevel);

            // 递归检查是否可以继续升级
            self::checkAndUpgradeUserLevel($user_id);
        }
    }

    /**
     * 检查升级条件
     * @param object $user 用户对象
     * @param array $condition 升级条件
     * @return bool
     */
    private static function checkUpgradeCondition($user, $condition)
    {
        if (empty($condition['type'])) return false;

        switch ($condition['type']) {
            case 'purchase':
                // 购买条件：检查累计消费金额
                return $user->total_consume >= ($condition['amount'] ?? 0);

            case 'personal_sales':
                // 个人销售条件
                return $user->sales_count >= ($condition['count'] ?? 0);

            case 'team_level':
                // 团队等级条件：团队中指定等级的人数
                $targetLevel = self::getLevelConfig($condition['level_code']);
                if (!$targetLevel) return false;

                $count = self::getTeamLevelCount($user->id, $targetLevel['level_order']);
                return $count >= ($condition['count'] ?? 0);

            default:
                return false;
        }
    }

    /**
     * 执行用户等级升级
     * @param int $user_id 用户ID
     * @param array $old_level 原等级配置
     * @param array $new_level 新等级配置
     */
    private static function upgradeUserLevel($user_id, $old_level, $new_level)
    {
        $user = User::get($user_id);
        if (!$user) return;

        // 更新用户等级和组别
        $user->user_level = $new_level['level_order'];
        $user->group_id = $new_level['id'];
        $user->save();

        // 记录升级日志
        $team_stats = self::getTeamStats($user_id);
        self::logLevelUpgrade($user_id, $old_level['level_order'], $new_level['level_order'],
            "满足升级条件自动升级", json_encode($team_stats));

        Log::info("用户等级升级: 用户{$user_id} 从 {$old_level['name']} 升级到 {$new_level['name']}");
    }

    /**
     * 获取团队中指定等级的人数
     * @param int $user_id 用户ID
     * @param int $level_order 等级排序
     * @return int
     */
    private static function getTeamLevelCount($user_id, $level_order)
    {
        // 使用递归函数替代WITH RECURSIVE语法，兼容MySQL 5.7
        $count = 0;
        $visited = []; // 防止循环引用

        // 递归查询所有下级用户
        self::countTeamLevelRecursive($user_id, $level_order, $count, $visited, 0, 10);

        return $count;
    }

    /**
     * 递归统计团队中指定等级的用户数量
     * @param int $parentUserId 父级用户ID
     * @param int $targetLevelOrder 目标等级
     * @param int &$count 计数器（引用传递）
     * @param array &$visited 已访问的用户ID（防止循环引用）
     * @param int $currentDepth 当前深度
     * @param int $maxDepth 最大深度
     */
    private static function countTeamLevelRecursive($parentUserId, $targetLevelOrder, &$count, &$visited, $currentDepth, $maxDepth)
    {
        if ($currentDepth >= $maxDepth || in_array($parentUserId, $visited)) {
            return;
        }

        $visited[] = $parentUserId;

        // 获取直接下级用户
        $children = Db::name('user')
            ->where('parent_user_id', $parentUserId)
            ->field('id, user_level')
            ->select();

        foreach ($children as $child) {
            // 如果是目标等级，计数+1
            if ($child['user_level'] == $targetLevelOrder) {
                $count++;
            }

            // 递归查询下级
            self::countTeamLevelRecursive($child['id'], $targetLevelOrder, $count, $visited, $currentDepth + 1, $maxDepth);
        }
    }

    /**
     * 获取团队统计数据
     * @param int $user_id 用户ID
     * @return array
     */
    private static function getTeamStats($user_id)
    {
        return [
            'direct_subordinates' => User::where('parent_user_id', $user_id)->count(),
            'level_2_count' => self::getTeamLevelCount($user_id, self::LEVEL_ADVISOR),
            'level_3_count' => self::getTeamLevelCount($user_id, self::LEVEL_MANAGER),
            'level_4_count' => self::getTeamLevelCount($user_id, self::LEVEL_DIRECTOR),
            'level_5_count' => self::getTeamLevelCount($user_id, self::LEVEL_WISDOM),
        ];
    }

    /**
     * 检查是否有资格升级为享业董事（名额限制）
     * @return bool
     */
    private static function isEligibleForDirectorLevel()
    {
        $config = self::getBusinessConfig();
        $current_directors = User::where('user_level', self::LEVEL_DIRECTOR)->count();
        return $current_directors < $config['director_limit'];
    }

    /**
     * 检查复购要求
     * @param int $user_id 用户ID
     * @param float $unit_price 单价（可选）
     */
    public static function checkRepurchaseRequirement($user_id, $unit_price = null)
    {
        $user = User::get($user_id);
        if (!$user) return;

        $config = self::getBusinessConfig();

        // 计算用户实际销售金额（基于分润记录中的订单）
        $total_sales_amount = self::getUserSalesAmount($user_id);

        // 计算用户累计获得分润
        $bonusStats = self::getUserBonusStats($user_id);
        $total_bonus = $bonusStats['total_bonus'];

        // 检查是否达到复购要求（分润达到销售金额的倍数）
        $repurchase_threshold = bcmul($total_sales_amount, $config['repurchase_multiplier'], 2);

        if (bccomp($total_bonus, $repurchase_threshold, 2) >= 0) {
            $user->repurchase_required = 1;
            $user->total_bonus_received = $total_bonus; // 更新累计分润字段
            $user->save();

            Log::info("用户{$user_id}需要复购：累计分润{$total_bonus}，实际销售金额{$total_sales_amount}，复购阈值{$repurchase_threshold}");
        } else {
            Log::info("用户{$user_id}暂不需要复购：累计分润{$total_bonus}，实际销售金额{$total_sales_amount}，复购阈值{$repurchase_threshold}");
        }
    }

    /**
     * 处理非活跃用户出局
     * 定时任务调用，处理1个月内无销售的用户
     */
    public static function handleInactiveUsers()
    {
        $config = self::getBusinessConfig();
        $inactive_timestamp = time() - ($config['inactive_days'] * 24 * 3600);

        // 查找非活跃用户（1个月内无销售且已激活的用户）
        $inactive_users = User::where('is_active', 1)
            ->where('last_sale_time', '<', $inactive_timestamp)
            ->where('last_sale_time', '>', 0)
            ->select();

        foreach ($inactive_users as $user) {
            try {
                Db::startTrans();

                // 按购买顺序一次性补助积分后出局
                $bonus_score = $config['inactive_bonus'];
                Wallet::change($user->id, 'score', $bonus_score, 'activity_gift',
                    [], '非活跃用户出局补助');

                // 设置为非激活状态
                $user->is_active = 0;
                $user->save();

                Log::info("用户{$user->id}因非活跃出局，补助{$bonus_score}积分");

                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                Log::error("处理非活跃用户{$user->id}出局失败: " . $e->getMessage());
            }
        }
    }

    /**
     * 记录分润日志
     * @param int $order_id 订单ID
     * @param int $user_id 购买用户ID
     * @param int $bonus_user_id 获得分润的用户ID
     * @param string $bonus_type 分润类型
     * @param float $amount 分润金额
     * @param float $score_amount 积分金额
     * @param string $memo 备注
     */
    private static function logBonusRecord($order_id, $user_id, $bonus_user_id, $bonus_type, $amount, $score_amount, $memo)
    {
        Db::name('shopro_bonus_log')->insert([
            'order_id' => $order_id,
            'user_id' => $user_id,
            'bonus_user_id' => $bonus_user_id,
            'bonus_type' => $bonus_type,
            'amount' => $amount,
            'score_amount' => $score_amount,
            'memo' => $memo,
            'createtime' => time()
        ]);
    }

    /**
     * 记录等级升级日志
     * @param int $user_id 用户ID
     * @param int $old_level 原等级
     * @param int $new_level 新等级
     * @param string $reason 升级原因
     * @param string $team_stats 团队统计数据
     */
    private static function logLevelUpgrade($user_id, $old_level, $new_level, $reason, $team_stats)
    {
        Db::name('shopro_user_level_log')->insert([
            'user_id' => $user_id,
            'old_level' => $old_level,
            'new_level' => $new_level,
            'upgrade_reason' => $reason,
            'team_stats' => $team_stats,
            'createtime' => time()
        ]);
    }

    /**
     * 获取用户等级名称
     * @param int $level_order 等级排序
     * @return string
     */
    public static function getLevelName($level_order)
    {
        $level = self::getLevelConfigByOrder($level_order);
        return $level ? $level['name'] : '未知等级';
    }

    /**
     * 获取用户实际销售金额（基于钱包日志中的直接销售奖励反推）
     * @param int $user_id 用户ID
     * @return float
     */
    public static function getUserSalesAmount($user_id)
    {
        // 查询用户的直接销售现金奖励记录
        $direct_bonus_records = Db::name('shopro_user_wallet_log')
            ->where('user_id', $user_id)
            ->where('type', 'money')
            ->where('event', 'order_buy_bonus')  // 直接销售奖励事件
            ->where('amount', '>', 0)  // 只统计正数（收入）
            ->select();

        $total_sales = 0;

        foreach ($direct_bonus_records as $record) {
            // 根据20%的比例反推销售金额
            $sales_amount = bcdiv($record['amount'], 0.2, 2);
            $total_sales = bcadd($total_sales, $sales_amount, 2);
        }

        return floatval($total_sales);
    }

    /**
     * 获取用户分润统计（基于钱包日志）
     * @param int $user_id 用户ID
     * @return array
     */
    public static function getUserBonusStats($user_id)
    {
        // 统计所有分润相关的现金收入
        $bonus_events = [
            'order_buy_bonus',      // 直接销售奖励
            'order_invite_bonus',   // 团队奖励
            'order_team_bonus',     // 团队奖励（新增）
            'order_level_bonus',    // 岗位补助
            'order_ping_bonus',     // 同岗补助
            'order_subsidy'         // 特殊补助（新增）
        ];

        $total_bonus = Db::name('shopro_user_wallet_log')
            ->where('user_id', $user_id)
            ->where('type', 'money')
            ->whereIn('event', $bonus_events)
            ->where('amount', '>', 0)  // 只统计正数（收入）
            ->sum('amount');

        // 统计所有分润相关的积分收入
        $total_score = Db::name('shopro_user_wallet_log')
            ->where('user_id', $user_id)
            ->where('type', 'score')
            ->whereIn('event', $bonus_events)
            ->where('amount', '>', 0)  // 只统计正数（收入）
            ->sum('amount');

        return [
            'total_bonus' => $total_bonus ?: 0,
            'total_score' => $total_score ?: 0
        ];
    }

    /**
     * 处理积分池沉淀
     * @param int $order_id 订单ID
     * @return bool
     */
    private static function processScorePoolDeposit($order_id)
    {
        try {
            // 获取订单中的奖金商品信息
            $bonus_items = self::getBonusItems($order_id);

            if (empty($bonus_items)) {
                Log::info("订单{$order_id}中没有奖金商品，跳过积分池沉淀");
                return true;
            }

            // 调用积分池处理
            $result = ScorePool::processDeposit($order_id, $bonus_items);

            if ($result) {
                Log::info("订单{$order_id}积分池沉淀处理成功");
            } else {
                Log::error("订单{$order_id}积分池沉淀处理失败");
            }

            return $result;

        } catch (\Exception $e) {
            Log::error("处理订单{$order_id}积分池沉淀异常: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 处理用户激活状态管理
     * @param int $order_id 订单ID
     * @param int $buyer_user_id 购买用户ID
     * @param bool $has_bonus_goods 是否包含奖金商品
     * @return bool
     */
    private static function processUserActiveStatus($order_id, $buyer_user_id, $has_bonus_goods)
    {
        try {
            if (!$has_bonus_goods) {
                Log::info("订单{$order_id}不包含奖金商品，跳过激活状态处理");
                return true;
            }

            // 调用激活状态管理器处理
            $result = UserActiveManager::processOrderActive($order_id, $buyer_user_id, $has_bonus_goods);

            if ($result) {
                Log::info("订单{$order_id}激活状态处理成功");
            } else {
                Log::error("订单{$order_id}激活状态处理失败");
            }

            return $result;

        } catch (\Exception $e) {
            Log::error("处理订单{$order_id}激活状态异常: " . $e->getMessage());
            return false;
        }
    }

}