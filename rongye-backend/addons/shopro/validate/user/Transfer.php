<?php

namespace addons\shopro\validate\user;

use think\Validate;

class Transfer extends Validate
{
    protected $rule = [
        'to_user_id' => 'require|integer|gt:0',
        'amount' => 'require|float|gt:0|elt:10000',
        'memo' => 'max:100'
    ];

    protected $message = [
        'to_user_id.require' => '请选择转账目标用户',
        'to_user_id.integer' => '用户ID格式错误',
        'to_user_id.gt' => '用户ID无效',
        'amount.require' => '请输入转账金额',
        'amount.float' => '转账金额格式错误',
        'amount.gt' => '转账金额必须大于0',
        'amount.elt' => '单次转账金额不能超过10000元',
        'memo.max' => '备注不能超过100个字符'
    ];

    protected $scene = [
        'transfer' => ['to_user_id', 'amount', 'memo']
    ];
}
