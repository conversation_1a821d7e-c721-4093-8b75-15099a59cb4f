<?php

namespace addons\shopro\controller\user;

use addons\shopro\controller\Common;
use addons\shopro\service\Wallet;
use app\admin\model\shopro\user\User as UserModel;
use app\admin\model\shopro\user\WalletLog as WalletLogModel;
use think\Db;
use think\Exception;

class Transfer extends Common
{
    protected $noNeedLogin = [];
    protected $noNeedRight = ['*'];

    /**
     * 搜索用户
     * 支持按用户名、手机号、用户ID搜索
     */
    public function searchUser()
    {
        $keyword = $this->request->param('keyword', '');
        $currentUser = auth_user();
        
        if (empty($keyword)) {
            $this->error('请输入搜索关键词');
        }

        // 构建搜索条件
        $where = [];
        if (is_numeric($keyword)) {
            // 如果是纯数字，可能是用户ID或手机号
            if (strlen($keyword) == 11) {
                // 11位数字，按手机号搜索
                $where['mobile'] = $keyword;
            } else {
                // 其他数字，按用户ID搜索
                $where['id'] = $keyword;
            }
        } else {
            // 非数字，按用户名搜索
            $where['username'] = $keyword;
        }

        // 排除当前用户
        $user = UserModel::where($where)
            ->where('id', '<>', $currentUser->id)
            ->where('status', 'normal')
            ->field('id,username,nickname,avatar,mobile')
            ->find();

        if (!$user) {
            $this->error('未找到该用户');
        }

        // 隐藏手机号中间4位
        if ($user->mobile) {
            $user->mobile = substr_replace($user->mobile, '****', 3, 4);
        }

        $this->success('查找成功', $user);
    }

    /**
     * 执行转账
     */
    public function transfer()
    {
        $params = $this->request->param();
        $currentUser = auth_user();

        // 验证参数
        $this->svalidate($params, '.transfer');

        $toUserId = $params['to_user_id'];
        $amount = floatval($params['amount']);
        $memo = $params['memo'] ?? '';

        // 验证转账金额
        if ($amount <= 0) {
            $this->error('转账金额必须大于0');
        }

        if ($amount > 10000) {
            $this->error('单次转账金额不能超过10000元');
        }

        // 验证余额是否充足
        if ($currentUser->money < $amount) {
            $this->error('余额不足');
        }

        // 验证目标用户
        $toUser = UserModel::where('id', $toUserId)
            ->where('status', 'normal')
            ->find();
        
        if (!$toUser) {
            $this->error('目标用户不存在或已被禁用');
        }

        if ($toUser->id == $currentUser->id) {
            $this->error('不能转账给自己');
        }

        // 执行转账操作
        Db::startTrans();
        try {
            // 转出操作
            Wallet::change(
                $currentUser,
                'money',
                -$amount,
                'transfer_out',
                ['to_user_id' => $toUser->id, 'to_username' => $toUser->username],
                $memo ? "转账给{$toUser->username}: {$memo}" : "转账给{$toUser->username}"
            );

            // 转入操作
            Wallet::change(
                $toUser,
                'money',
                $amount,
                'transfer_in',
                ['from_user_id' => $currentUser->id, 'from_username' => $currentUser->username],
                $memo ? "来自{$currentUser->username}的转账: {$memo}" : "来自{$currentUser->username}的转账"
            );

            Db::commit();
            $this->success('转账成功');
        } catch (Exception $e) {
            Db::rollback();
            $this->error('转账失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取转账记录
     */
    public function transferLog()
    {
        $type = $this->request->param('type', 'all'); // all, out, in
        $page = $this->request->param('page', 1);
        $listRows = $this->request->param('list_rows', 10);
        $currentUser = auth_user();

        $where = [
            'user_id' => $currentUser->id,
            'type' => 'money'
        ];

        // 根据类型筛选
        switch ($type) {
            case 'out':
                $where['event'] = 'transfer_out';
                break;
            case 'in':
                $where['event'] = 'transfer_in';
                break;
            case 'all':
                $where['event'] = ['in', ['transfer_out', 'transfer_in']];
                break;
        }

        $logs = WalletLogModel::where($where)
            ->order('createtime', 'desc')
            ->paginate($listRows);

        $this->success('获取成功', $logs);
    }
}
