<?php
namespace addons\shopro\controller;

use addons\shopro\model\TourismSpot as TourismSpotModel;
use addons\shopro\controller\Common;
use think\Controller;

class Tourism extends Common
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];
    
    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 获取旅游景点列表
     */
    public function index()
    {
        $page = $this->request->param('page', 1, 'intval');
        $pageSize = $this->request->param('pageSize', 10, 'intval');
        $status = $this->request->param('status', '');
        
        // 使用Service层处理业务逻辑
        $result = \addons\shopro\service\Tourism::getList($page, $pageSize, $status);
        
        $this->success('获取成功', $result);
    }

    /**
     * 获取旅游景点详情
     */
    public function detail()
    {
        $id = $this->request->param('id', 0, 'intval');

        if (!$id) {
            $this->error('参数错误');
        }

        // 使用Service层处理业务逻辑
        $detail = \addons\shopro\service\Tourism::getDetail($id);

        $this->success('获取成功', $detail);
    }

    /**
     * 提交景点报名
     */
    public function register()
    {
        $spotId = $this->request->param('spot_id', 0, 'intval');
        $name = $this->request->param('name', '', 'trim');
        $mobile = $this->request->param('mobile', '', 'trim');
        $idCard = $this->request->param('id_card', '', 'trim');
        $remark = $this->request->param('remark', '', 'trim');

        // 参数验证
        if (!$spotId) {
            $this->error('请选择景点');
        }

        if (!$name) {
            $this->error('请输入姓名');
        }

        if (!$mobile) {
            $this->error('请输入手机号');
        }

        if (!preg_match('/^1[3-9]\d{9}$/', $mobile)) {
            $this->error('手机号格式不正确');
        }

        if (!$idCard) {
            $this->error('请输入身份证号');
        }

        if (!preg_match('/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/', $idCard)) {
            $this->error('身份证号格式不正确');
        }

        // 检查景点是否存在
        $spot = \think\Db::name('shopro_tourism_spot')->where('id', $spotId)->find();
        if (!$spot) {
            $this->error('景点不存在');
        }

        // 获取当前用户ID（如果已登录）
        $userId = 0;
        if ($this->auth && $this->auth->isLogin()) {
            $userId = $this->auth->id;
        }

        // 检查是否已经报名
        $where = ['spot_id' => $spotId, 'status' => 1];

        // 如果用户已登录，优先按用户ID检查
        if ($userId > 0) {
            $where['user_id'] = $userId;
            $errorMsg = '您已报名此景点，请勿重复报名';
        } else {
            // 未登录用户按手机号检查
            $where['mobile'] = $mobile;
            $errorMsg = '该手机号已报名此景点';
        }

        $existRegistration = \think\Db::name('shopro_tourism_registration')
            ->where($where)
            ->find();

        if ($existRegistration) {
            $this->error($errorMsg);
        }

        // 保存报名信息
        $data = [
            'spot_id' => $spotId,
            'user_id' => $userId,
            'name' => $name,
            'mobile' => $mobile,
            'id_card' => $idCard,
            'remark' => $remark,
            'status' => 1,
            'createtime' => time(),
            'updatetime' => time()
        ];

        $result = \think\Db::name('shopro_tourism_registration')->insert($data);

        if ($result) {
            $this->success('报名成功');
        } else {
            $this->error('报名失败，请重试');
        }
    }

    /**
     * 检查用户是否已报名某个景点
     */
    public function checkRegistration()
    {
        $spotId = $this->request->param('spot_id', 0, 'intval');

        if (!$spotId) {
            $this->error('请选择景点');
        }

        // 获取当前用户ID（如果已登录）
        $userId = 0;
        if ($this->auth && $this->auth->isLogin()) {
            $userId = $this->auth->id;
        }

        if ($userId <= 0) {
            // 未登录用户返回未报名状态
            $this->success('未报名', ['registered' => false]);
            return;
        }

        // 检查是否已经报名
        $existRegistration = \think\Db::name('shopro_tourism_registration')
            ->where('spot_id', $spotId)
            ->where('user_id', $userId)
            ->where('status', 1)
            ->find();

        if ($existRegistration) {
            $this->success('已报名', [
                'registered' => true,
                'registration' => [
                    'id' => $existRegistration['id'],
                    'name' => $existRegistration['name'],
                    'mobile' => $existRegistration['mobile'],
                    'createtime' => $existRegistration['createtime']
                ]
            ]);
        } else {
            $this->success('未报名', ['registered' => false]);
        }
    }

    /**
     * 获取用户报名记录
     */
    public function getRegistrations()
    {
        $page = $this->request->param('page', 1, 'intval');
        $pageSize = $this->request->param('pageSize', 10, 'intval');
        $mobile = $this->request->param('mobile', '', 'trim');

        if (!$mobile) {
            $this->error('请输入手机号');
        }

        $where = ['mobile' => $mobile];

        $total = \think\Db::name('shopro_tourism_registration')->where($where)->count();

        $list = \think\Db::name('shopro_tourism_registration')
            ->alias('r')
            ->join('shopro_tourism_spot s', 'r.spot_id = s.id')
            ->where($where)
            ->field('r.*, s.name as spot_name, s.address, s.price')
            ->order('r.createtime DESC')
            ->page($page, $pageSize)
            ->select();

        // 格式化时间
        foreach ($list as &$item) {
            $item['createtime'] = date('Y-m-d H:i:s', $item['createtime']);
            $item['status_text'] = $this->getStatusText($item['status']);
        }

        $this->success('获取成功', [
            'total' => $total,
            'data' => $list,
            'page' => $page,
            'pageSize' => $pageSize
        ]);
    }

    /**
     * 获取状态文本
     */
    private function getStatusText($status)
    {
        $statusMap = [
            0 => '已取消',
            1 => '已报名',
            2 => '已确认'
        ];

        return $statusMap[$status] ?? '未知';
    }
}