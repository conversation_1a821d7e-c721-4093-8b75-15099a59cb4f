<?php
namespace addons\shopro\controller;

use addons\shopro\controller\Common;
use think\Controller;
use think\Db;

class TourismRegistration extends Common
{
    protected $noNeedLogin = ['*'];  // 临时允许无需登录，生产环境应该移除
    protected $noNeedRight = ['*'];
    
    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 确认报名
     * 管理员确认用户的文旅景点报名申请
     */
    public function confirm($ids)
    {
        // 获取报名记录ID
        $id = 0;
        if ($ids) {
            // 如果是多个ID，取第一个
            $idArray = explode(',', $ids);
            $id = intval($idArray[0]);
        }

        if (!$id) {
            return json(['code' => 0, 'msg' => '参数错误：报名记录ID不能为空']);
        }
        
        // 获取当前操作员信息（如果需要记录操作人员）
        $operatorId = 0;
        $operatorName = '系统管理员';
        if ($this->auth && $this->auth->isLogin()) {
            $operatorId = $this->auth->id;
            $operatorName = $this->auth->username;
        }
        
        try {
            // 开启事务
            Db::startTrans();
            
            // 查询报名记录
            $registration = Db::name('shopro_tourism_registration')
                ->where('id', $id)
                ->find();
            
            if (!$registration) {
                Db::rollback();
                return json(['code' => 0, 'msg' => '报名记录不存在']);
            }

            // 验证报名状态
            if ($registration['status'] != 1) {
                Db::rollback();
                $statusText = $this->getStatusText($registration['status']);
                return json(['code' => 0, 'msg' => "当前报名状态为：{$statusText}，无法确认"]);
            }
            
            // 检查confirmtime字段是否存在，如果不存在则添加
            $this->ensureConfirmtimeField();
            
            // 更新报名状态为已确认
            $updateData = [
                'status' => 2,
                'updatetime' => time(),
                'confirmtime' => time()
            ];
            
            $result = Db::name('shopro_tourism_registration')
                ->where('id', $id)
                ->update($updateData);
            
            if (!$result) {
                Db::rollback();
                return json(['code' => 0, 'msg' => '确认失败，请重试']);
            }

            // 记录操作日志（可选）
            $this->logOperation($id, $registration, $operatorId, $operatorName);

            // 提交事务
            Db::commit();

            // 返回成功结果
            return json([
                'code' => 1,
                'msg' => '确认成功',
                'data' => [
                    'id' => $id,
                    'status' => 2,
                    'status_text' => '已确认',
                    'confirmtime' => time(),
                    'operator' => $operatorName
                ]
            ]);

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();

            // 记录错误日志
            \think\Log::error('确认报名失败：' . $e->getMessage());

            return json(['code' => 0, 'msg' => '确认失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 获取报名记录列表（管理员用）
     */
    public function index()
    {
        try {
            // 简单测试：直接查询所有记录
            $list = Db::name('shopro_tourism_registration')
                ->limit(10)
                ->select();

            $total = Db::name('shopro_tourism_registration')->count();

            // 格式化数据
            foreach ($list as &$item) {
                $item['status_text'] = $this->getStatusText($item['status']);
                if (isset($item['createtime']) && $item['createtime']) {
                    $item['createtime'] = date('Y-m-d H:i:s', $item['createtime']);
                }
                if (isset($item['updatetime']) && $item['updatetime']) {
                    $item['updatetime'] = date('Y-m-d H:i:s', $item['updatetime']);
                }
                $item['confirmtime'] = '';
            }

            $result = [
                'list' => $list,
                'total' => $total,
                'page' => 1,
                'pageSize' => 10,
                'totalPages' => ceil($total / 10)
            ];

            return json(['code' => 1, 'msg' => '获取成功', 'data' => $result]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 获取报名状态文本
     */
    private function getStatusText($status)
    {
        $statusMap = [
            0 => '已取消',
            1 => '待确认',
            2 => '已确认'
        ];
        
        return isset($statusMap[$status]) ? $statusMap[$status] : '未知状态';
    }
    
    /**
     * 确保confirmtime字段存在
     */
    private function ensureConfirmtimeField()
    {
        try {
            // 检查字段是否存在
            $columns = Db::query("SHOW COLUMNS FROM " . config('database.prefix') . "shopro_tourism_registration LIKE 'confirmtime'");
            
            if (empty($columns)) {
                // 字段不存在，添加字段
                $sql = "ALTER TABLE " . config('database.prefix') . "shopro_tourism_registration ADD COLUMN confirmtime bigint(16) DEFAULT NULL COMMENT '确认时间'";
                Db::execute($sql);
                \think\Log::info('已自动添加confirmtime字段');
            }
        } catch (\Exception $e) {
            \think\Log::error('检查/添加confirmtime字段失败：' . $e->getMessage());
            // 不抛出异常，继续执行
        }
    }
    
    /**
     * 记录操作日志
     */
    private function logOperation($registrationId, $registration, $operatorId, $operatorName)
    {
        try {
            $logData = [
                'type' => 'tourism_registration_confirm',
                'target_id' => $registrationId,
                'operator_id' => $operatorId,
                'operator_name' => $operatorName,
                'content' => "确认了报名申请：{$registration['name']} ({$registration['mobile']}) 报名景点ID：{$registration['spot_id']}",
                'ip' => $this->request->ip(),
                'user_agent' => $this->request->header('User-Agent'),
                'createtime' => time()
            ];
            
            // 这里可以根据实际的日志表结构来保存
            // Db::name('admin_log')->insert($logData);
            
            \think\Log::info('报名确认操作日志：' . json_encode($logData));
        } catch (\Exception $e) {
            \think\Log::error('记录操作日志失败：' . $e->getMessage());
            // 不抛出异常，不影响主流程
        }
    }
}
