<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/>
    <title>地图位置(经纬度)选择插件</title>
    <link rel="stylesheet" href="__CDN__/assets/css/frontend.min.css"/>
    <link rel="stylesheet" href="__CDN__/assets/libs/font-awesome/css/font-awesome.min.css"/>
</head>
<body>
<div class="container">

    <div class="bs-docs-section clearfix">
        <div class="row">
            <div class="col-lg-12">
                <div class="page-header">
                    <h2>地图位置(经纬度)选择示例</h2>
                </div>

                <div class="bs-component">
                    <form action="" method="post" role="form">
                        <div class="form-group">
                            <label for=""></label>
                            <input type="text" class="form-control" name="" id="address" placeholder="地址">
                        </div>
                        <div class="form-group">
                            <label for=""></label>
                            <input type="text" class="form-control" name="" id="lng" placeholder="经度">
                        </div>
                        <div class="form-group">
                            <label for=""></label>
                            <input type="text" class="form-control" name="" id="lat" placeholder="纬度">
                        </div>
                        <div class="form-group">
                            <label for=""></label>
                            <input type="text" class="form-control" name="" id="zoom" placeholder="缩放">
                        </div>

                        <button type="button" class="btn btn-primary" data-toggle='addresspicker' data-input-id="address" data-lng-id="lng" data-lat-id="lat" data-zoom-id="zoom">点击选择</button>
                    </form>
                </div>

                <div class="page-header">
                    <h2 id="code">调用代码</h2>
                </div>
                <div class="bs-component">
                        <textarea class="form-control" rows="17">
                            <form action="" method="post" role="form">
                                <div class="form-group">
                                    <label for=""></label>
                                    <input type="text" class="form-control" name="" id="address" placeholder="地址">
                                </div>
                                <div class="form-group">
                                    <label for=""></label>
                                    <input type="text" class="form-control" name="" id="lng" placeholder="经度">
                                </div>
                                <div class="form-group">
                                    <label for=""></label>
                                    <input type="text" class="form-control" name="" id="lat" placeholder="纬度">
                                </div>
                                <div class="form-group">
                                    <label for=""></label>
                                    <input type="text" class="form-control" name="" id="zoom" placeholder="缩放">
                                </div>

                                <button type="button" class="btn btn-primary" data-toggle='addresspicker' data-input-id="address" data-lng-id="lng" data-lat-id="lat" data-zoom-id="zoom">点击选择</button>
                            </form>
                        </textarea>
                </div>
                <div class="page-header">
                    <h2>参数说明</h2>
                </div>

                <div class="bs-component" style="background:#fff;">
                    <table class="table table-bordered">
                        <thead>
                        <tr>
                            <th>参数</th>
                            <th>释义</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>data-input-id</td>
                            <td>填充地址的文本框ID</td>
                        </tr>
                        <tr>
                            <td>data-lng-id</td>
                            <td>填充经度的文本框ID</td>
                        </tr>
                        <tr>
                            <td>data-lat-id</td>
                            <td>填充纬度的文本框ID</td>
                        </tr>
                        <tr>
                            <td>data-zoom-id</td>
                            <td>填充缩放的文本框ID</td>
                        </tr>
                        </tbody>
                    </table>
                </div>

            </div>
        </div>
    </div>

</div>
<!--@formatter:off-->
<script type="text/javascript">
    var require = {
        config: {$config|json_encode}
    };
</script>
<!--@formatter:on-->

<script>
    require.callback = function () {
        define('addons/address', ['jquery', 'bootstrap', 'frontend', 'template'], function ($, undefined, Frontend, Template) {
            var Controller = {
                index: function () {
                }
            };
            return Controller;
        });
        define('lang', function () {
            return [];
        });
    }
</script>

<script src="__CDN__/assets/js/require.min.js" data-main="__CDN__/assets/js/require-frontend.min.js?v={$site.version}"></script>
</body>
</html>
