<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>地址选择器</title>
    <link rel="stylesheet" href="__CDN__/assets/css/frontend.min.css"/>
    <link rel="stylesheet" href="__CDN__/assets/libs/font-awesome/css/font-awesome.min.css"/>
    <style type="text/css">
        body {
            margin: 0;
            padding: 0;
        }

        #container {
            position: absolute;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
        }

        .confirm {
            position: absolute;
            bottom: 30px;
            right: 4%;
            z-index: 99;
            height: 50px;
            width: 50px;
            line-height: 50px;
            font-size: 15px;
            text-align: center;
            background-color: white;
            background: #1ABC9C;
            color: white;
            border: none;
            cursor: pointer;
            border-radius: 50%;
        }

        .search {
            position: absolute;
            width: 400px;
            top: 0;
            left: 50%;
            padding: 5px;
            margin-left: -200px;
        }

        label.BMapLabel {
            max-width: inherit;
            padding: .75rem 1.25rem;
            margin-bottom: 1rem;
            background-color: white;
            width: auto;
            min-width: 22rem;
            border: none;
            box-shadow: 0 2px 6px 0 rgba(114, 124, 245, .5);
        }

    </style>
</head>
<body>
<div class="search">
    <div class="input-group">
        <input type="text" id="place" name="q" class="form-control" placeholder="输入地点"/>
        <div id="searchResultPanel" style="border:1px solid #C0C0C0;width:150px;height:auto; display:none;"></div>
        <span class="input-group-btn">
            <button type="button" name="search" id="search-btn" class="btn btn-success">
                <i class="fa fa-search"></i>
            </button>
        </span>
    </div>
</div>
<div class="confirm">确定</div>
<div id="container"></div>
<script type="text/javascript" src="//api.map.baidu.com/api?v=2.0&ak={$config.baidukey|default=''}"></script>
<script src="__CDN__/assets/libs/jquery/dist/jquery.min.js"></script>
<script src="__CDN__/assets/addons/address/js/gcoord.min.js"></script>
<script type="text/javascript">
    $(function () {
        var map, marker, point, fromtype, totype;
        var zoom = parseInt("{$zoom}");
        var address = "{$address|htmlentities}";
        var lng = Number("{$lng}");
        var lat = Number("{$lat}");
        fromtype = "BD09";
        totype = "{$config.coordtype|default='DEFAULT'}"
        totype = totype === 'DEFAULT' ? "BD09" : totype;

        if (lng && lat && fromtype !== totype) {
            var result = gcoord.transform([lng, lat], gcoord[totype], gcoord[fromtype]);
            lng = result[0] || lng;
            lat = result[1] || lat;
        }

        var geocoder = new BMap.Geocoder();

        var addPointMarker = function (point, addr) {
            deletePoint();
            addPoint(point);

            if (addr) {
                addMarker(point, addr);
            } else {
                geocoder.getLocation(point, function (rs) {
                    addMarker(point, rs.address);
                });
            }

        };

        var addPoint = function (point) {
            lng = point.lng;
            lat = point.lat;
            marker = new BMap.Marker(point);
            map.addOverlay(marker);
            map.panTo(point);
        };

        var addMarker = function (point, addr) {
            address = addr;
            // var labelhtml = '<div class="info">地址:' + address + '<br>经度:' + point.lng + '<br>纬度:' + point.lat + '</div>';
            var labelhtml = '<div class="info">地址:' + address + '</div>';
            var label = new BMap.Label(labelhtml, {offset: new BMap.Size(16, 20)});
            label.setStyle({
                border: 'none',
                padding: '.75rem 1.25rem'
            });
            marker.setLabel(label);
        };

        var deletePoint = function () {
            var allOverlay = map.getOverlays();
            for (var i = 0; i < allOverlay.length; i++) {
                map.removeOverlay(allOverlay[i]);
            }
        };

        var init = function () {
            map = new BMap.Map("container"); // 创建地图实例
            var point = new BMap.Point(lng, lat); // 创建点坐标
            map.enableScrollWheelZoom(true); //开启鼠标滚轮缩放
            map.centerAndZoom(point, zoom); // 初始化地图，设置中心点坐标和地图级别

            var size = new BMap.Size(10, 20);
            map.addControl(new BMap.CityListControl({
                anchor: BMAP_ANCHOR_TOP_LEFT,
                offset: size,
            }));

            if ("{$lng}" != '' && "{$lat}" != '') {
                addPointMarker(point, address);
            }

            ac = new BMap.Autocomplete({"input": "place", "location": map}); //建立一个自动完成的对象
            ac.addEventListener("onhighlight", function (e) {  //鼠标放在下拉列表上的事件
                var str = "";
                var _value = e.fromitem.value;
                var value = "";
                if (e.fromitem.index > -1) {
                    value = _value.province + _value.city + _value.district + _value.street + _value.business;
                }
                str = "FromItem<br />index = " + e.fromitem.index + "<br />value = " + value;

                value = "";
                if (e.toitem.index > -1) {
                    _value = e.toitem.value;
                    value = _value.province + _value.city + _value.district + _value.street + _value.business;
                }
                str += "<br />ToItem<br />index = " + e.toitem.index + "<br />value = " + value;
                $("#searchResultPanel").html(str);
            });
            ac.addEventListener("onconfirm", function (e) {    //鼠标点击下拉列表后的事件
                var _value = e.item.value;
                myValue = _value.province + _value.city + _value.district + _value.street + _value.business;
                $("#searchResultPanel").html("onconfirm<br />index = " + e.item.index + "<br />myValue = " + myValue);
                setPlace();
            });

            function setPlace(text) {
                map.clearOverlays();    //清除地图上所有覆盖物
                function myFun() {
                    var results = local.getResults();
                    var result = local.getResults().getPoi(0);
                    var point = result.point;    //获取第一个智能搜索的结果
                    map.centerAndZoom(point, 18);
                    // map.addOverlay(new BMap.Marker(point));    //添加标注
                    if (result.type != 0) {
                        address = results.province + results.city + result.address;
                    } else {
                        address = result.address;
                    }
                    addPointMarker(point, address);
                }

                var local = new BMap.LocalSearch(map, { //智能搜索
                    onSearchComplete: myFun
                });
                local.search(text || myValue);
            }

            map.addEventListener("click", function (e) {
                //通过点击百度地图，可以获取到对应的point, 由point的lng、lat属性就可以获取对应的经度纬度
                addPointMarker(e.point);
            });

            //点击搜索按钮
            $(document).on('click', '#search-btn', function () {
                if ($("#place").val() == '')
                    return;
                setPlace($("#place").val());
            });
        };

        var close = function (data) {
            var index = parent.Layer.getFrameIndex(window.name);
            var callback = parent.$("#layui-layer" + index).data("callback");
            //再执行关闭
            parent.Layer.close(index);
            //再调用回传函数
            if (typeof callback === 'function') {
                callback.call(undefined, data);
            }
        };

        //点击确定后执行回调赋值
        $(document).on('click', '.confirm', function () {
            var zoom = map.getZoom();
            var data = {lat: lat, lng: lng, zoom: zoom, address: address};
            if (fromtype !== totype) {
                var result = gcoord.transform([data.lng, data.lat], gcoord[fromtype], gcoord[totype]);
                data.lng = (result[0] || data.lng).toFixed(5);
                data.lat = (result[1] || data.lat).toFixed(5);
                console.log(data, result, fromtype, totype);
            }
            close(data);
        });

        init();
    });
</script>
</body>
</html>
