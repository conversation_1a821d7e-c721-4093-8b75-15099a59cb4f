<?php

return [
    [
        'name' => 'maptype',
        'title' => '默认地图类型',
        'type' => 'radio',
        'content' => [
            'baidu' => '百度地图',
            'amap' => '高德地图',
            'tencent' => '腾讯地图',
        ],
        'value' => 'amap',
        'rule' => 'required',
        'msg' => '',
        'tip' => '',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'zoom',
        'title' => '默认缩放级别',
        'type' => 'string',
        'content' => [],
        'value' => '11',
        'rule' => 'required',
        'msg' => '',
        'tip' => '',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'lat',
        'title' => '默认Lat',
        'type' => 'string',
        'content' => [],
        'value' => '39.919990',
        'rule' => 'required',
        'msg' => '',
        'tip' => '',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'lng',
        'title' => '默认Lng',
        'type' => 'string',
        'content' => [],
        'value' => '116.456270',
        'rule' => 'required',
        'msg' => '',
        'tip' => '',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'baidukey',
        'title' => '百度地图KEY',
        'type' => 'string',
        'content' => [],
        'value' => '',
        'rule' => '',
        'msg' => '',
        'tip' => '',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'amapkey',
        'title' => '高德地图KEY',
        'type' => 'string',
        'content' => [],
        'value' => '8e0f9b685d846ef1a53e066c3f3516a0',
        'rule' => '',
        'msg' => '',
        'tip' => '',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'amapsecurityjscode',
        'title' => '高德地图安全密钥',
        'type' => 'string',
        'content' => [],
        'value' => '841415ee3dcc4a35d685e3a088806c26',
        'rule' => '',
        'msg' => '',
        'tip' => '',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'tencentkey',
        'title' => '腾讯地图KEY',
        'type' => 'string',
        'content' => [],
        'value' => '',
        'rule' => '',
        'msg' => '',
        'tip' => '',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'coordtype',
        'title' => '坐标系类型',
        'type' => 'select',
        'content' => [
            'DEFAULT' => '默认(使用所选地图默认坐标系)',
            'GCJ02' => 'GCJ-02',
            'BD09' => 'BD-09',
        ],
        'value' => 'DEFAULT',
        'rule' => 'required',
        'msg' => '',
        'tip' => '',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => '__tips__',
        'title' => '温馨提示',
        'type' => '',
        'content' => [],
        'value' => '请先申请对应地图的Key，配置后再使用',
        'rule' => '',
        'msg' => '',
        'tip' => '',
        'ok' => '',
        'extend' => 'alert-danger-light',
    ],
];
