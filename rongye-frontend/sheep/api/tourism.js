import request from '@/sheep/request';

export default {
  // 获取旅游景点列表
  list: (params) =>
    request({
      url: 'tourism/index',
      method: 'GET',
      params,
      custom: {
        showLoading: false,
        showError: false,
      },
    }),
  // 获取旅游景点详情
  detail: (params) =>
    request({
      url: 'tourism/detail',
      method: 'GET',
      params,
      custom: {
        showLoading: false,
        showError: false,
      },
    }),
  // 检查用户是否已报名
  checkRegistration: (params) =>
    request({
      url: 'tourism/checkRegistration',
      method: 'GET',
      params,
      custom: {
        showLoading: false,
        showError: false,
      },
    }),
  // 提交景点报名
  register: (data) =>
    request({
      url: 'tourism/register',
      method: 'POST',
      data,
      custom: {
        showLoading: true,
        showError: true,
      },
    }),
  // 获取用户报名记录
  getRegistrations: (params) =>
    request({
      url: 'tourism/getRegistrations',
      method: 'GET',
      params,
      custom: {
        showLoading: false,
        showError: false,
      },
    }),
};