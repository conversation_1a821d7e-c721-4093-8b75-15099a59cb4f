<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>旅游报名弹窗测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8f8f8;
            padding: 20px;
        }
        
        .test-container {
            max-width: 400px;
            margin: 0 auto;
            background: #fff;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }
        
        .test-btn {
            background: linear-gradient(135deg, #4a90e2, #357abd);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
        }
        
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        
        .modal-overlay.show {
            display: flex;
        }
        
        /* 弹窗样式 - 转换rpx为px (1rpx ≈ 0.5px) */
        .register-modal {
            width: 90vw;
            max-width: 300px;
            min-width: 250px;
            background-color: #fff;
            border-radius: 10px;
            overflow: hidden;
            margin: 0 auto;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
        }
        
        @media screen and (max-width: 300px) {
            .register-modal {
                width: 95vw;
                min-width: auto;
                border-radius: 8px;
            }
        }
        
        .modal-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
            background-color: #fafafa;
        }
        
        .modal-title {
            font-size: 17px;
            font-weight: bold;
            color: #333;
        }
        
        .close-btn {
            font-size: 18px;
            color: #999;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: #f0f0f0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .close-btn:hover {
            background-color: #e0e0e0;
            color: #666;
            transform: scale(0.95);
        }
        
        .modal-body {
            padding: 15px;
            max-height: 60vh;
            overflow-y: auto;
        }
        
        .form-item {
            margin-bottom: 15px;
        }
        
        .form-item:last-child {
            margin-bottom: 0;
        }
        
        .label {
            display: block;
            font-size: 14px;
            color: #333;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .required {
            color: #ff4757;
            margin-left: 2px;
        }
        
        .input, .textarea {
            width: 100%;
            padding: 12px 10px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
            color: #333;
            background-color: #fff;
            box-sizing: border-box;
            transition: border-color 0.3s ease;
        }
        
        .input:focus, .textarea:focus {
            border-color: #4a90e2;
            outline: none;
        }
        
        .input::placeholder, .textarea::placeholder {
            color: #999;
            font-size: 13px;
        }
        
        .textarea {
            height: 60px;
            resize: none;
            line-height: 1.5;
        }
        
        .modal-footer {
            display: flex;
            padding: 15px;
            gap: 10px;
            border-top: 1px solid #f0f0f0;
            background-color: #fafafa;
        }
        
        .cancel-btn, .confirm-btn {
            flex: 1;
            height: 44px;
            border-radius: 6px;
            font-size: 15px;
            font-weight: 500;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .cancel-btn {
            background-color: #f5f5f5;
            color: #666;
            border: 1px solid #e0e0e0;
        }
        
        .cancel-btn:hover {
            background-color: #e0e0e0;
            transform: scale(0.98);
        }
        
        .confirm-btn {
            background: linear-gradient(135deg, #4a90e2, #357abd);
            color: #fff;
            box-shadow: 0 2px 6px rgba(74, 144, 226, 0.3);
        }
        
        .confirm-btn:hover {
            background: linear-gradient(135deg, #357abd, #2968a3);
            transform: scale(0.98);
        }
        
        .confirm-btn:disabled {
            background: #ccc;
            color: #999;
            box-shadow: none;
            transform: none;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>旅游报名弹窗测试</h2>
        <p style="margin: 20px 0; color: #666;">点击下方按钮测试报名弹窗样式</p>
        <button class="test-btn" onclick="showModal()">立即报名</button>
    </div>
    
    <div class="modal-overlay" id="modalOverlay" onclick="hideModal(event)">
        <div class="register-modal" onclick="event.stopPropagation()">
            <div class="modal-header">
                <span class="modal-title">景点报名</span>
                <span class="close-btn" onclick="hideModal()">×</span>
            </div>
            <div class="modal-body">
                <div class="form-item">
                    <label class="label">姓名 <span class="required">*</span></label>
                    <input class="input" placeholder="请输入真实姓名" maxlength="20" />
                </div>
                <div class="form-item">
                    <label class="label">手机号 <span class="required">*</span></label>
                    <input class="input" placeholder="请输入手机号" type="tel" maxlength="11" />
                </div>
                <div class="form-item">
                    <label class="label">身份证号 <span class="required">*</span></label>
                    <input class="input" placeholder="请输入身份证号" maxlength="18" />
                </div>
                <div class="form-item">
                    <label class="label">备注</label>
                    <textarea class="textarea" placeholder="请输入备注信息（选填）" maxlength="200"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button class="cancel-btn" onclick="hideModal()">取消</button>
                <button class="confirm-btn" onclick="submitForm()">确认报名</button>
            </div>
        </div>
    </div>
    
    <script>
        function showModal() {
            document.getElementById('modalOverlay').classList.add('show');
        }
        
        function hideModal(event) {
            if (event && event.target !== event.currentTarget) return;
            document.getElementById('modalOverlay').classList.remove('show');
        }
        
        function submitForm() {
            alert('报名提交成功！（这是测试）');
            hideModal();
        }
    </script>
</body>
</html>
