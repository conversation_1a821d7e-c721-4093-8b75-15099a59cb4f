<template>
  <s-layout title="景点详情">
    <view class="tourism-detail">
      <!-- 图片轮播 -->
      <swiper class="swiper" circular indicator-dots autoplay :interval="3000" :duration="500">
        <swiper-item v-for="(image, index) in state.imageList" :key="index">
          <image :src="sheep.$url.cdn(image)" mode="aspectFill" class="swiper-image"></image>
        </swiper-item>
      </swiper>
      
      <!-- 基本信息 -->
      <view class="info-section">
        <view class="name">{{ state.detail.name }}</view>
        <view class="price-open">
          <view class="price" v-if="state.detail.price">
            <text class="label">门票</text>
            <text class="value">¥{{ state.detail.price }}</text>
          </view>
          <view class="open-time">
            <text class="label">开放时间</text>
            <text class="value">{{ state.detail.open_time }}</text>
          </view>
        </view>
        <view class="address">
          <text class="icon">📍</text>
          <text>{{ state.detail.address }}</text>
        </view>
        <view class="createtime">更新时间：{{ state.detail.createtime }}</view>
      </view>
      
      <!-- 详细描述 -->
      <view class="description-section">
        <view class="section-title">景点介绍</view>
        <view class="description">{{ state.detail.description }}</view>
      </view>
      
      <!-- 地图位置 -->
      <view class="map-section" v-if="state.detail.latitude && state.detail.longitude">
        <view class="section-title">地理位置</view>
        <map
          class="map"
          :latitude="Number(state.detail.latitude)"
          :longitude="Number(state.detail.longitude)"
          :markers="state.markers"
          scale="14"
        ></map>
      </view>

      <!-- 报名按钮 -->
      <view class="register-section">
        <button
          class="register-btn"
          :class="{ 'registered': state.hasRegistered }"
          @tap="handleRegisterClick"
        >
          {{ state.hasRegistered ? '已报名' : '立即报名' }}
        </button>
      </view>
    </view>

    <!-- 报名弹窗 -->
    <su-popup
      :show="state.showRegisterModal"
      type="center"
      :isMaskClick="false"
      round="20"
      @close="closeRegisterModal"
    >
      <view class="register-modal">
        <view class="modal-header">
          <text class="modal-title">景点报名</text>
          <text class="close-btn" @tap="closeRegisterModal">×</text>
        </view>
        <view class="modal-body">
          <view class="form-item">
            <text class="label">姓名 <text class="required">*</text></text>
            <input
              class="input"
              v-model="state.registerForm.name"
              placeholder="请输入真实姓名"
              maxlength="20"
              @focus="handleFocus('name')"
              @blur="handleBlur('name')"
              @input="handleInput('name', $event)"
            />
          </view>
          <view class="form-item">
            <text class="label">手机号 <text class="required">*</text></text>
            <input
              class="input"
              v-model="state.registerForm.mobile"
              placeholder="请输入手机号"
              type="tel"
              maxlength="11"
              @focus="handleFocus('mobile')"
              @blur="handleBlur('mobile')"
              @input="handleInput('mobile', $event)"
            />
          </view>
          <view class="form-item">
            <text class="label">身份证号 <text class="required">*</text></text>
            <input
              class="input"
              v-model="state.registerForm.idCard"
              placeholder="请输入身份证号"
              maxlength="18"
              @focus="handleFocus('idCard')"
              @blur="handleBlur('idCard')"
              @input="handleInput('idCard', $event)"
            />
          </view>
          <view class="form-item">
            <text class="label">备注</text>
            <textarea
              class="textarea"
              v-model="state.registerForm.remark"
              placeholder="请输入备注信息（选填）"
              maxlength="200"
              @focus="handleFocus('remark')"
              @blur="handleBlur('remark')"
              @input="handleInput('remark', $event)"
            ></textarea>
          </view>
        </view>
        <view class="modal-footer">
          <button class="cancel-btn" @tap="closeRegisterModal">取消</button>
          <button class="confirm-btn" @tap="submitRegister" :disabled="state.submitting">
            {{ state.submitting ? '提交中...' : '确认报名' }}
          </button>
        </view>
      </view>
    </su-popup>
  </s-layout>
</template>

<script setup>
  import sheep from '@/sheep';
  import { onLoad } from '@dcloudio/uni-app';
  import { reactive, ref } from 'vue';
  import tools from '@/sheep/helper/tools';

  const state = reactive({
    detail: {},
    imageList: [],
    markers: [],
    showRegisterModal: false,
    registerForm: {
      name: '',
      mobile: '',
      idCard: '',
      remark: ''
    },
    submitting: false,
    hasRegistered: false,
    registrationInfo: null
  });

  // 获取文旅详情
  async function getDetail(id) {
    const { data, code } = await sheep.$api.tourism.detail({
      id: id
    });
    if (code === 1) {
      // 格式化日期
      if (data.createtime) {
        data.createtime = tools.dateFormat(data.createtime, 'YYYY-MM-DD HH:mm:ss');
      }
      
      
      
      // 处理图片列表
      if (data.images) {
        state.imageList = data.images.split(',');
      }
      
      // 设置地图标记
      if (data.latitude && data.longitude) {
        state.markers = [{
          id: 1,
          latitude: Number(data.latitude),
          longitude: Number(data.longitude),
          title: data.name,
          iconPath: '/static/images/marker.png', // 确保有这个图标
          width: 32,
          height: 32
        }];
      }
      
      state.detail = data;

      // 检查用户是否已报名
      await checkRegistrationStatus();
    }
  }

  // 检查用户是否已报名
  async function checkRegistrationStatus() {
    try {
      const { data } = await sheep.$api.tourism.checkRegistration({
        spot_id: state.detail.id
      });

      state.hasRegistered = data.registered;
      if (data.registered && data.registration) {
        state.registrationInfo = data.registration;
      }
    } catch (error) {
      console.error('检查报名状态失败:', error);
    }
  }

  // 处理报名按钮点击
  function handleRegisterClick() {
    if (state.hasRegistered) {
      // 已报名，显示报名信息
      uni.showModal({
        title: '报名信息',
        content: `您已成功报名此景点\n报名姓名：${state.registrationInfo?.name || '未知'}\n报名时间：${formatTime(state.registrationInfo?.createtime)}`,
        showCancel: false,
        confirmText: '知道了'
      });
    } else {
      // 未报名，显示报名弹窗
      showRegisterModal();
    }
  }

  // 显示报名弹窗
  function showRegisterModal() {
    state.showRegisterModal = true;
  }

  // 格式化时间
  function formatTime(timestamp) {
    if (!timestamp) return '未知';

    // 确保时间戳是数字类型
    const ts = Number(timestamp);
    if (isNaN(ts)) return '未知';

    // 如果是10位时间戳，需要转换为毫秒
    const date = new Date(ts * 1000);

    // 检查日期是否有效
    if (isNaN(date.getTime())) return '未知';

    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
  }

  // 关闭报名弹窗
  function closeRegisterModal() {
    state.showRegisterModal = false;
    // 重置表单
    state.registerForm = {
      name: '',
      mobile: '',
      idCard: '',
      remark: ''
    };
  }

  // 处理输入框焦点
  function handleFocus(field) {
    console.log(`${field} 获得焦点`);
  }

  // 处理输入框失去焦点
  function handleBlur(field) {
    console.log(`${field} 失去焦点`);
  }

  // 处理输入事件
  function handleInput(field, event) {
    let value = '';
    if (event.target) {
      value = event.target.value;
    } else if (event.detail) {
      value = event.detail.value;
    } else if (typeof event === 'string') {
      value = event;
    }
    console.log(`${field} 输入值:`, value);
    state.registerForm[field] = value || '';
  }

  // 提交报名
  async function submitRegister() {
    // 获取实际的输入框值
    const inputs = document.querySelectorAll('input');
    const textarea = document.querySelector('textarea');

    if (inputs.length >= 3) {
      state.registerForm.name = inputs[0].value || '';
      state.registerForm.mobile = inputs[1].value || '';
      state.registerForm.idCard = inputs[2].value || '';
    }

    if (textarea) {
      state.registerForm.remark = textarea.value || '';
    }

    // 表单验证
    if (!state.registerForm.name || !state.registerForm.name.trim()) {
      uni.showToast({
        title: '请输入姓名',
        icon: 'none'
      });
      return;
    }

    if (!state.registerForm.mobile || !state.registerForm.mobile.trim()) {
      uni.showToast({
        title: '请输入手机号',
        icon: 'none'
      });
      return;
    }

    if (!/^1[3-9]\d{9}$/.test(state.registerForm.mobile)) {
      uni.showToast({
        title: '手机号格式不正确',
        icon: 'none'
      });
      return;
    }

    if (!state.registerForm.idCard || !state.registerForm.idCard.trim()) {
      uni.showToast({
        title: '请输入身份证号',
        icon: 'none'
      });
      return;
    }

    if (!/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(state.registerForm.idCard)) {
      uni.showToast({
        title: '身份证号格式不正确',
        icon: 'none'
      });
      return;
    }

    state.submitting = true;

    try {
      const { code, msg } = await sheep.$api.tourism.register({
        spot_id: state.detail.id,
        name: state.registerForm.name,
        mobile: state.registerForm.mobile,
        id_card: state.registerForm.idCard,
        remark: state.registerForm.remark
      });

      if (code === 1) {
        uni.showToast({
          title: '报名成功',
          icon: 'success'
        });
        closeRegisterModal();

        // 更新报名状态
        state.hasRegistered = true;
        state.registrationInfo = {
          name: state.registerForm.name,
          mobile: state.registerForm.mobile,
          createtime: Math.floor(Date.now() / 1000) // 10位时间戳
        };
      } else {
        uni.showToast({
          title: msg || '报名失败',
          icon: 'none'
        });
      }
    } catch (error) {
      uni.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    } finally {
      state.submitting = false;
    }
  }

  onLoad((options) => {
    if (options.id) {
      getDetail(options.id);
    }
  });
</script>

<style lang="scss" scoped>
  .tourism-detail {
    background-color: #f8f8f8;
    
    .swiper {
      width: 100%;
      height: 500rpx;
      
      .swiper-image {
        width: 100%;
        height: 100%;
      }
    }
    
    .info-section {
      background-color: #fff;
      padding: 30rpx;
      margin-bottom: 20rpx;
      
      .name {
        font-size: 36rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 20rpx;
      }
      
      .price-open {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20rpx;
        
        .price, .open-time {
          .label {
            font-size: 24rpx;
            color: #999;
            margin-right: 10rpx;
          }
          
          .value {
            font-size: 28rpx;
            color: #333;
            font-weight: 500;
          }
        }
        
        .price .value {
          color: #ff6b6b;
        }
      }
      
      .address {
        display: flex;
        align-items: center;
        font-size: 26rpx;
        color: #666;
        margin-bottom: 20rpx;
        
        .icon {
          margin-right: 10rpx;
        }
      }
      
      .createtime {
        font-size: 22rpx;
        color: #999;
        text-align: right;
      }
    }
    
    .description-section, .map-section {
      background-color: #fff;
      padding: 30rpx;
      margin-bottom: 20rpx;
      
      .section-title {
        font-size: 30rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 20rpx;
        position: relative;
        padding-left: 20rpx;
        
        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 6rpx;
          width: 8rpx;
          height: 30rpx;
          background-color: #4a90e2;
          border-radius: 4rpx;
        }
      }
      
      .description {
        font-size: 28rpx;
        color: #666;
        line-height: 1.8;
        text-align: justify;
      }
    }
    
    .map {
      width: 100%;
      height: 400rpx;
      border-radius: 8rpx;
    }

    .register-section {
      padding: 30rpx;
      background-color: #fff;

      .register-btn {
        width: 100%;
        height: 88rpx;
        background: linear-gradient(135deg, #4a90e2, #357abd);
        color: #fff;
        font-size: 32rpx;
        font-weight: bold;
        border-radius: 44rpx;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;

        &:active {
          opacity: 0.8;
        }

        &.registered {
          background: linear-gradient(135deg, #95a5a6, #7f8c8d);

          &:active {
            opacity: 0.8;
          }
        }
      }
    }
  }

  // 报名弹窗样式
  .register-modal {
    width: 90vw;
    max-width: 600rpx;
    min-width: 500rpx;
    background-color: #fff;
    border-radius: 20rpx;
    overflow: hidden;
    margin: 0 auto;
    box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.15);

    // 响应式设计
    @media screen and (max-width: 600rpx) {
      width: 95vw;
      min-width: auto;
      border-radius: 16rpx;
    }

    .modal-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 30rpx;
      border-bottom: 2rpx solid #f0f0f0;
      background-color: #fafafa;

      .modal-title {
        font-size: 34rpx;
        font-weight: bold;
        color: #333;
      }

      .close-btn {
        font-size: 36rpx;
        color: #999;
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background-color: #f0f0f0;
        transition: all 0.3s ease;

        &:active {
          background-color: #e0e0e0;
          color: #666;
          transform: scale(0.95);
        }
      }
    }

    .modal-body {
      padding: 30rpx;
      max-height: 60vh;
      overflow-y: auto;

      .form-item {
        margin-bottom: 30rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          display: block;
          font-size: 28rpx;
          color: #333;
          margin-bottom: 15rpx;
          font-weight: 500;

          .required {
            color: #ff4757;
            margin-left: 4rpx;
          }
        }

        .input, .textarea {
          width: 100%;
          height: 80rpx;
          padding: 0 20rpx;
          border: 2rpx solid #e0e0e0;
          border-radius: 12rpx;
          font-size: 28rpx;
          color: #333;
          background-color: #fff;
          box-sizing: border-box;
          transition: border-color 0.3s ease;
          -webkit-appearance: none;
          appearance: none;
          line-height: 76rpx;
          position: relative;
          z-index: 1;

          &:focus {
            border-color: #4a90e2;
            outline: none;
            box-shadow: 0 0 0 4rpx rgba(74, 144, 226, 0.1);
          }

          &::placeholder {
            color: #999;
            font-size: 26rpx;
          }
        }

        .textarea {
          height: 120rpx;
          resize: none;
          line-height: 1.5;
          padding: 20rpx;
          z-index: 1;
          position: relative;
        }
      }
    }

    .modal-footer {
      display: flex;
      padding: 30rpx;
      gap: 20rpx;
      border-top: 2rpx solid #f0f0f0;
      background-color: #fafafa;

      .cancel-btn, .confirm-btn {
        flex: 1;
        height: 88rpx;
        border-radius: 12rpx;
        font-size: 30rpx;
        font-weight: 500;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
      }

      .cancel-btn {
        background-color: #f5f5f5;
        color: #666;
        border: 2rpx solid #e0e0e0;

        &:active {
          background-color: #e0e0e0;
          transform: scale(0.98);
        }
      }

      .confirm-btn {
        background: linear-gradient(135deg, #4a90e2, #357abd);
        color: #fff;
        box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.3);

        &:active {
          background: linear-gradient(135deg, #357abd, #2968a3);
          transform: scale(0.98);
        }

        &:disabled {
          background: #ccc;
          color: #999;
          box-shadow: none;
          transform: none;
        }
      }
    }
  }
</style>