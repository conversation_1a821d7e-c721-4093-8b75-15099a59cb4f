<template>
  <s-layout class="transfer-wrap" title="余额互转">
    <!-- 当前余额显示 -->
    <view class="balance-card ss-m-20">
      <view class="balance-info">
        <text class="balance-label">当前余额</text>
        <text class="balance-amount">¥{{ userInfo.money || '0.00' }}</text>
      </view>
    </view>

    <!-- 转账表单 -->
    <view class="form-card ss-m-20">
      <!-- 目标用户搜索 -->
      <view class="form-item">
        <view class="form-label">转账对象</view>
        <view class="search-box" @tap="showUserSearch = true">
          <text v-if="!selectedUser" class="placeholder">请输入用户名/手机号</text>
          <view v-else class="selected-user">
            <image class="user-avatar" :src="selectedUser.avatar" mode="aspectFill"></image>
            <text class="user-name">{{ selectedUser.nickname || selectedUser.username }}</text>
          </view>
        </view>
      </view>

      <!-- 转账金额 -->
      <view class="form-item">
        <view class="form-label">转账金额</view>
        <view class="amount-input-box">
          <text class="currency">¥</text>
          <input 
            class="amount-input" 
            type="digit" 
            placeholder="请输入转账金额"
            v-model="transferForm.amount"
            @input="onAmountInput"
          />
        </view>
      </view>

      <!-- 转账备注 -->
      <view class="form-item">
        <view class="form-label">转账备注（可选）</view>
        <input 
          class="memo-input" 
          type="text" 
          placeholder="请输入转账备注"
          maxlength="100"
          v-model="transferForm.memo"
        />
      </view>

      <!-- 确认转账按钮 -->
      <button 
        class="transfer-btn" 
        :disabled="!canTransfer"
        @tap="confirmTransfer"
      >
        确认转账
      </button>
    </view>

    <!-- 最近转账记录 -->
    <view class="recent-records ss-m-20">
      <view class="record-header">
        <text class="record-title">最近转账</text>
        <text class="view-all" @tap="viewAllRecords">查看全部</text>
      </view>
      <view v-if="recentRecords.length === 0" class="no-records">
        <text>暂无转账记录</text>
      </view>
      <view v-else class="record-list">
        <view 
          class="record-item" 
          v-for="record in recentRecords" 
          :key="record.id"
        >
          <view class="record-info">
            <text class="record-type">{{ record.event === 'transfer_out' ? '转出' : '转入' }}</text>
            <text class="record-user">{{ getRecordUser(record) }}</text>
            <text class="record-time">{{ record.createtime }}</text>
          </view>
          <text class="record-amount" :class="record.event === 'transfer_out' ? 'out' : 'in'">
            {{ record.event === 'transfer_out' ? '-' : '+' }}¥{{ Math.abs(record.amount) }}
          </text>
        </view>
      </view>
    </view>

    <!-- 用户搜索弹窗 -->
    <su-popup 
      :show="showUserSearch" 
      type="bottom" 
      round="20"
      @close="showUserSearch = false"
    >
      <view class="search-popup">
        <view class="popup-header">
          <text class="popup-title">搜索用户</text>
          <text class="popup-close" @tap="showUserSearch = false">×</text>
        </view>
        <view class="search-input-box">
          <input 
            class="search-input" 
            type="text" 
            placeholder="请输入用户名/手机号"
            v-model="searchKeyword"
            @confirm="searchUser"
          />
          <button class="search-btn" @tap="searchUser">搜索</button>
        </view>
        <view v-if="searchResult" class="search-result">
          <view class="user-item" @tap="selectUser(searchResult)">
            <image class="user-avatar" :src="searchResult.avatar" mode="aspectFill"></image>
            <view class="user-info">
              <text class="user-name">{{ searchResult.nickname || searchResult.username }}</text>
              <text class="user-mobile">{{ searchResult.mobile }}</text>
            </view>
          </view>
        </view>
        <view v-else-if="searchKeyword && !searchLoading" class="no-result">
          <text>未找到该用户</text>
        </view>
      </view>
    </su-popup>
  </s-layout>
</template>

<script setup>
import { reactive, ref, computed, onMounted } from 'vue';
import sheep from '@/sheep';

// 用户信息
const userInfo = computed(() => sheep.$store('user').userInfo);

// 表单数据
const transferForm = reactive({
  amount: '',
  memo: ''
});

// 选中的用户
const selectedUser = ref(null);

// 搜索相关
const showUserSearch = ref(false);
const searchKeyword = ref('');
const searchResult = ref(null);
const searchLoading = ref(false);

// 最近转账记录
const recentRecords = ref([]);

// 计算属性
const canTransfer = computed(() => {
  return selectedUser.value && 
         transferForm.amount && 
         parseFloat(transferForm.amount) > 0 && 
         parseFloat(transferForm.amount) <= parseFloat(userInfo.value.money || 0);
});

// 方法
const onAmountInput = (e) => {
  let value = e.detail.value;
  // 限制小数点后两位
  if (value.includes('.')) {
    const parts = value.split('.');
    if (parts[1] && parts[1].length > 2) {
      value = parts[0] + '.' + parts[1].substring(0, 2);
      transferForm.amount = value;
    }
  }
};

const searchUser = async () => {
  if (!searchKeyword.value.trim()) {
    sheep.$helper.toast('请输入搜索关键词');
    return;
  }

  searchLoading.value = true;
  try {
    const res = await sheep.$api.user.searchUser({
      keyword: searchKeyword.value.trim()
    });
    if (res.code === 1) {
      searchResult.value = res.data;
    } else {
      searchResult.value = null;
      sheep.$helper.toast(res.msg || '搜索失败');
    }
  } catch (error) {
    searchResult.value = null;
    sheep.$helper.toast('搜索失败');
  } finally {
    searchLoading.value = false;
  }
};

const selectUser = (user) => {
  selectedUser.value = user;
  showUserSearch.value = false;
  searchKeyword.value = '';
  searchResult.value = null;
};



const confirmTransfer = async () => {
  if (!canTransfer.value) return;

  uni.showModal({
    title: '确认转账',
    content: `确认向 ${selectedUser.value.nickname || selectedUser.value.username} 转账 ¥${transferForm.amount} 吗？`,
    success: async function (res) {
      if (res.confirm) {
        try {
          const apiRes = await sheep.$api.user.transfer({
            to_user_id: selectedUser.value.id,
            amount: parseFloat(transferForm.amount),
            memo: transferForm.memo
          });

          if (apiRes.code === 1) {
            sheep.$helper.toast('转账成功');
            // 重置表单
            transferForm.amount = '';
            transferForm.memo = '';
            selectedUser.value = null;
            // 刷新用户信息和记录
            await sheep.$store('user').getInfo();
            loadRecentRecords();
          } else {
            sheep.$helper.toast(apiRes.msg || '转账失败');
          }
        } catch (error) {
          console.error('转账失败:', error);
          sheep.$helper.toast('转账失败');
        }
      }
    }
  });
};

const loadRecentRecords = async () => {
  try {
    const res = await sheep.$api.user.transferLog({
      type: 'all',
      list_rows: 5
    });
    if (res.code === 1) {
      recentRecords.value = res.data.data || [];
    }
  } catch (error) {
    console.error('加载转账记录失败', error);
  }
};

const getRecordUser = (record) => {
  if (record.event === 'transfer_out') {
    return record.ext?.to_username || '未知用户';
  } else {
    return record.ext?.from_username || '未知用户';
  }
};

const viewAllRecords = () => {
  sheep.$router.go('/pages/user/wallet/transfer-log');
};

onMounted(() => {
  loadRecentRecords();
});
</script>

<style lang="scss" scoped>
.transfer-wrap {
  background-color: #f7f7f7;
}

.balance-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  color: white;

  .balance-info {
    text-align: center;

    .balance-label {
      display: block;
      font-size: 28rpx;
      opacity: 0.8;
      margin-bottom: 10rpx;
    }

    .balance-amount {
      display: block;
      font-size: 48rpx;
      font-weight: bold;
      font-family: 'DIN Alternate', 'Arial', sans-serif;
    }
  }
}

.form-card {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;

  .form-item {
    margin-bottom: 40rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .form-label {
      font-size: 28rpx;
      color: #333;
      margin-bottom: 20rpx;
      font-weight: 500;
    }

    .search-box {
      display: flex;
      align-items: center;
      padding: 20rpx;
      border: 2rpx solid #e5e5e5;
      border-radius: 12rpx;
      min-height: 80rpx;

      .placeholder {
        color: #999;
        font-size: 28rpx;
        flex: 1;
      }

      .selected-user {
        display: flex;
        align-items: center;
        flex: 1;

        .user-avatar {
          width: 60rpx;
          height: 60rpx;
          border-radius: 50%;
          margin-right: 20rpx;
        }

        .user-name {
          font-size: 28rpx;
          color: #333;
        }
      }
    }

    .amount-input-box {
      display: flex;
      align-items: center;
      border: 2rpx solid #e5e5e5;
      border-radius: 12rpx;
      padding: 0 20rpx;

      .currency {
        font-size: 32rpx;
        color: #333;
        margin-right: 10rpx;
      }

      .amount-input {
        flex: 1;
        height: 80rpx;
        font-size: 32rpx;
        color: #333;
      }
    }

    .memo-input {
      width: 100%;
      height: 80rpx;
      padding: 0 20rpx;
      border: 2rpx solid #e5e5e5;
      border-radius: 12rpx;
      font-size: 28rpx;
    }
  }

  .transfer-btn {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 44rpx;
    color: white;
    font-size: 32rpx;
    font-weight: 500;
    margin-top: 40rpx;

    &:disabled {
      background: #ccc;
    }
  }
}

.recent-records {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;

  .record-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;

    .record-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
    }

    .view-all {
      font-size: 26rpx;
      color: #667eea;
    }
  }

  .no-records {
    text-align: center;
    padding: 60rpx 0;
    color: #999;
    font-size: 28rpx;
  }

  .record-list {
    .record-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 30rpx 0;
      border-bottom: 1rpx solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }

      .record-info {
        flex: 1;

        .record-type {
          font-size: 28rpx;
          color: #333;
          font-weight: 500;
        }

        .record-user {
          display: block;
          font-size: 24rpx;
          color: #666;
          margin: 8rpx 0;
        }

        .record-time {
          font-size: 22rpx;
          color: #999;
        }
      }

      .record-amount {
        font-size: 28rpx;
        font-weight: 500;

        &.out {
          color: #ff4757;
        }

        &.in {
          color: #2ed573;
        }
      }
    }
  }
}

.search-popup {
  padding: 40rpx;

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40rpx;

    .popup-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
    }

    .popup-close {
      font-size: 48rpx;
      color: #999;
    }
  }

  .search-input-box {
    display: flex;
    margin-bottom: 40rpx;

    .search-input {
      flex: 1;
      height: 80rpx;
      padding: 0 20rpx;
      border: 2rpx solid #e5e5e5;
      border-radius: 12rpx 0 0 12rpx;
      font-size: 28rpx;
    }

    .search-btn {
      width: 120rpx;
      height: 80rpx;
      background: #667eea;
      color: white;
      border-radius: 0 12rpx 12rpx 0;
      font-size: 28rpx;
    }
  }

  .search-result {
    .user-item {
      display: flex;
      align-items: center;
      padding: 30rpx;
      border: 2rpx solid #e5e5e5;
      border-radius: 12rpx;

      .user-avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        margin-right: 20rpx;
      }

      .user-info {
        flex: 1;

        .user-name {
          display: block;
          font-size: 28rpx;
          color: #333;
          font-weight: 500;
        }

        .user-mobile {
          display: block;
          font-size: 24rpx;
          color: #666;
          margin-top: 8rpx;
        }
      }
    }
  }

  .no-result {
    text-align: center;
    padding: 60rpx 0;
    color: #999;
    font-size: 28rpx;
  }
}
</style>
