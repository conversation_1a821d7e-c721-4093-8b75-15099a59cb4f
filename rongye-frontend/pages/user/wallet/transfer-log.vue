<template>
  <s-layout class="transfer-log-wrap" title="转账记录">
    <!-- 筛选标签 -->
    <su-sticky>
      <view class="filter-tabs">
        <view 
          class="tab-item" 
          :class="{ active: currentTab === 'all' }"
          @tap="switchTab('all')"
        >
          全部
        </view>
        <view 
          class="tab-item" 
          :class="{ active: currentTab === 'out' }"
          @tap="switchTab('out')"
        >
          转出
        </view>
        <view 
          class="tab-item" 
          :class="{ active: currentTab === 'in' }"
          @tap="switchTab('in')"
        >
          转入
        </view>
      </view>
    </su-sticky>

    <!-- 转账记录列表 -->
    <s-empty v-if="state.pagination.total === 0" text="暂无转账记录" icon="/static/data-empty.png" />
    
    <view v-if="state.pagination.total > 0" class="record-list">
      <view 
        class="record-item" 
        v-for="record in state.pagination.data" 
        :key="record.id"
      >
        <view class="record-left">
          <view class="record-type">
            <text class="type-text">{{ record.event === 'transfer_out' ? '转出' : '转入' }}</text>
            <text class="user-text">{{ getRecordUser(record) }}</text>
          </view>
          <view class="record-info">
            <text class="record-time">{{ record.createtime }}</text>
            <text v-if="record.memo" class="record-memo">{{ record.memo }}</text>
          </view>
        </view>
        <view class="record-right">
          <text class="record-amount" :class="record.event === 'transfer_out' ? 'out' : 'in'">
            {{ record.event === 'transfer_out' ? '-' : '+' }}¥{{ Math.abs(record.amount) }}
          </text>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <uni-load-more 
      v-if="state.pagination.total > 0" 
      :status="state.loadStatus" 
      :content-text="{
        contentdown: '上拉加载更多',
      }" 
    />
  </s-layout>
</template>

<script setup>
import { reactive, ref, onMounted } from 'vue';
import { onReachBottom } from '@dcloudio/uni-app';
import sheep from '@/sheep';

// 当前选中的标签
const currentTab = ref('all');

// 分页数据
const pagination = {
  data: [],
  current_page: 1,
  total: 0,
  last_page: 1,
};

// 状态数据
const state = reactive({
  pagination,
  loadStatus: '',
});

// 切换标签
const switchTab = (tab) => {
  if (currentTab.value === tab) return;
  
  currentTab.value = tab;
  state.pagination = {
    data: [],
    current_page: 1,
    total: 0,
    last_page: 1,
  };
  loadTransferLog();
};

// 获取转账记录
const loadTransferLog = async (page = 1, listRows = 10) => {
  state.loadStatus = 'loading';
  
  try {
    const res = await sheep.$api.user.transferLog({
      type: currentTab.value,
      page,
      list_rows: listRows,
    });
    
    if (res.code === 1) {
      const newData = res.data.data || [];
      
      if (page === 1) {
        state.pagination.data = newData;
      } else {
        state.pagination.data = [...state.pagination.data, ...newData];
      }
      
      state.pagination.current_page = res.data.current_page || page;
      state.pagination.total = res.data.total || 0;
      state.pagination.last_page = res.data.last_page || 1;
      
      if (state.pagination.current_page < state.pagination.last_page) {
        state.loadStatus = 'more';
      } else {
        state.loadStatus = 'noMore';
      }
    } else {
      state.loadStatus = 'noMore';
      sheep.$helper.toast(res.msg || '加载失败');
    }
  } catch (error) {
    state.loadStatus = 'noMore';
    sheep.$helper.toast('加载失败');
  }
};

// 获取转账对象用户名
const getRecordUser = (record) => {
  if (record.event === 'transfer_out') {
    return record.ext?.to_username || '未知用户';
  } else {
    return record.ext?.from_username || '未知用户';
  }
};

// 上拉加载更多
onReachBottom(() => {
  if (state.loadStatus === 'more') {
    loadTransferLog(state.pagination.current_page + 1);
  }
});

// 页面加载
onMounted(() => {
  loadTransferLog();
});
</script>

<style lang="scss" scoped>
.transfer-log-wrap {
  background-color: #f7f7f7;
}

.filter-tabs {
  display: flex;
  background: white;
  padding: 20rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;

  .tab-item {
    flex: 1;
    text-align: center;
    padding: 20rpx 0;
    font-size: 28rpx;
    color: #666;
    position: relative;

    &.active {
      color: #667eea;
      font-weight: 500;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 60rpx;
        height: 4rpx;
        background: #667eea;
        border-radius: 2rpx;
      }
    }
  }
}

.record-list {
  padding: 0 20rpx;

  .record-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    margin: 20rpx 0;
    padding: 40rpx;
    border-radius: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

    .record-left {
      flex: 1;

      .record-type {
        margin-bottom: 16rpx;

        .type-text {
          font-size: 32rpx;
          font-weight: 500;
          color: #333;
        }

        .user-text {
          font-size: 28rpx;
          color: #666;
          margin-left: 20rpx;
        }
      }

      .record-info {
        .record-time {
          font-size: 24rpx;
          color: #999;
        }

        .record-memo {
          display: block;
          font-size: 24rpx;
          color: #666;
          margin-top: 8rpx;
          background: #f8f9fa;
          padding: 8rpx 16rpx;
          border-radius: 8rpx;
          margin-top: 16rpx;
        }
      }
    }

    .record-right {
      .record-amount {
        font-size: 32rpx;
        font-weight: 500;
        font-family: 'DIN Alternate', 'Arial', sans-serif;

        &.out {
          color: #ff4757;
        }

        &.in {
          color: #2ed573;
        }
      }
    }
  }
}
</style>
